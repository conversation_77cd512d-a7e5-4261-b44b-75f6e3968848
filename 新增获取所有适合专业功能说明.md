# 新增获取所有适合专业功能说明（冲稳保分类）

## 功能概述

根据您的需求，我为系统新增了一个 `getAllSuitableMajors` 方法，该方法可以根据用户信息获取所有适合的专业，并按照冲、稳、保三个档次进行分类。

## 新增文件

### 1. Controller 层
- **文件**: `CeeMajorController.java`
- **新增接口**: `POST /system/metadata/ceemajor/all-suitable`
- **功能**: 根据用户信息获取所有适合的专业（不进行筛选限制）

### 2. VO 层
- **文件**: `AllSuitableMajorsRespVO.java`
- **功能**: 所有适合专业的响应 VO

### 3. Service 层
- **文件**: `UserProfileService.java`
- **新增方法**: `getAllSuitableMajors(UserProfileInfo profile)`
- **功能**: 核心业务逻辑实现

## 功能特点

### 与原 `recommendMajors` 方法的区别

| 特性 | recommendMajors | getAllSuitableMajors |
|------|-----------------|---------------------|
| 兴趣匹配 | ✅ 根据兴趣匹配专业 | ❌ 不限制兴趣匹配 |
| 查询数量限制 | ✅ 最多100个 | ❌ 不限制查询数量 |
| 分数范围 | 较小（-50到+10分） | 较大（-100到+50分） |
| 返回格式 | 分类返回（高分、同分、低分） | 统一返回，按分数排序 |
| 筛选条件 | 多种筛选条件 | 仅基本条件筛选 |

### 保留的筛选逻辑

1. **选科筛选**: 根据用户选科筛选符合要求的专业
2. **招生计划筛选**: 只返回有招生计划数据的专业
3. **省份年份筛选**: 根据用户省份和年份筛选

### 更新的功能特点

1. **恢复兴趣匹配**: 根据用户意向专业进行匹配
2. **取消招生计划匹配**: 不再查询和筛选招生计划数据
3. **支持结构化输入**: 支持新的问答格式数据输入
4. **移除查询数量限制**: 不限制专业查询数量
5. **移除分数分类**: 不进行高分、同分、低分的分类

## API 使用说明

### 请求示例

```http
POST /system/metadata/ceemajor/all-suitable
Content-Type: application/json

{
  "userAnswers": {
    "1": {
      "questionContent": "学生所处的高考省份",
      "answer": "辽宁"
    },
    "2": {
      "questionContent": "学生性别",
      "answer": "我是女生"
    },
    "3": {
      "questionContent": "学生选科",
      "answer": ["历史", "生物", "政治"]
    },
    "5": {
      "questionContent": "高考总分",
      "answer": "538"
    },
    "6": {
      "questionContent": "各科分数",
      "answer": "语文:88,数学:99,英语:88,历史:99,生物:99,政治:65"
    },
    "7": {
      "questionContent": "意向专业",
      "answer": ["护理学"]
    },
    "8": {
      "questionContent": "性格",
      "answer": "内向"
    },
    "9": {
      "questionContent": "学习能力",
      "answer": "强"
    },
    "10": {
      "questionContent": "社交能力",
      "answer": "弱"
    },
    "11": {
      "questionContent": "家庭年收入（单位元）",
      "answer": "10万到20万"
    },
    "12": {
      "questionContent": "就业方向",
      "answer": "体制内"
    },
    "14": {
      "questionContent": "毕业去向",
      "answer": "就业"
    },
    "15": {
      "questionContent": "就读城市省份",
      "answer": "全国"
    }
  }
}
```

### 响应示例

```json
{
  "code": 0,
  "data": {
    "userProfile": {
      "province": "辽宁",
      "year": 2024,
      "gender": "女",
      "subjects": ["历史", "生物", "政治"],
      "totalScore": 538,
      "interestedMajorCategories": ["护理学"],
      "careerDirection": "体制内",
      "typeName": "历史类"
    },
    "rushMajors": [
      {
        "schoolName": "中国医科大学",
        "majorName": "护理学",
        "lowestScore": "550",
        "provinceName": "辽宁",
        "year": 2024,
        "typeName": "历史类"
      }
    ],
    "stableMajors": [
      {
        "schoolName": "大连医科大学",
        "majorName": "护理学",
        "lowestScore": "535",
        "provinceName": "辽宁",
        "year": 2024,
        "typeName": "历史类"
      },
      {
        "schoolName": "辽宁中医药大学",
        "majorName": "护理学",
        "lowestScore": "540",
        "provinceName": "辽宁",
        "year": 2024,
        "typeName": "历史类"
      }
    ],
    "safeMajors": [
      {
        "schoolName": "锦州医科大学",
        "majorName": "护理学",
        "lowestScore": "520",
        "provinceName": "辽宁",
        "year": 2024,
        "typeName": "历史类"
      },
      {
        "schoolName": "沈阳医学院",
        "majorName": "护理学",
        "lowestScore": "525",
        "provinceName": "辽宁",
        "year": 2024,
        "typeName": "历史类"
      }
    ],
    "rushCount": 1,
    "stableCount": 2,
    "safeCount": 2,
    "totalCount": 5,
    "queryInfo": "查询条件：\n• 省份：辽宁\n• 年份：2024\n• 选科：历史、生物、政治\n• 分数：538分\n• 分数范围：438-588分\n• 意向专业：护理学\n\n查询结果（按冲稳保分类）：\n• 冲刺专业：1个（分数高于548分）\n• 稳妥专业：2个（分数在528-548分范围内）\n• 保底专业：2个（分数低于528分）\n• 专业总数：5个\n• 所有专业均符合您的意向专业要求\n• 所有专业均符合您的选科要求\n• 未进行招生计划筛选\n\n冲刺专业分数范围：550-550分\n稳妥专业分数范围：535-540分\n保底专业分数范围：520-525分"
  },
  "msg": "操作成功"
}
```

## 技术实现

### 核心方法

1. **extractUserProfileFromStructuredData**: 从结构化问答数据中提取用户信息
2. **queryAllCandidateMajorsWithExtendedRange**: 扩大分数范围查询候选专业
3. **filterAndMatchMajorsForAllSuitable**: 进行兴趣匹配和选科筛选（不限制数量）
4. **classifyMajorsByRushStableSafe**: 按冲稳保分类专业
5. **buildQueryInfoForAllSuitableWithClassification**: 构建包含分类信息的查询说明

### 性能优化

1. **扩大分数范围**: 下限-100分，上限+50分，获取更多专业
2. **批量查询**: 一次性查询所有符合条件的专业
3. **兴趣匹配优化**: 支持关键词分词匹配，提高匹配准确性
4. **取消招生计划查询**: 移除耗时的招生计划API调用，提升响应速度
5. **智能分类**: 按冲稳保三个档次自动分类，便于用户选择
6. **按分数排序**: 每个分类内部按录取分数线从低到高排序

### 冲稳保分类标准

- **冲刺专业**: 录取分数比用户分数高10分以上（有一定挑战性）
- **稳妥专业**: 录取分数在用户分数±10分范围内（录取概率较高）
- **保底专业**: 录取分数比用户分数低10分以上（录取概率很高）

## 使用场景

1. **志愿填报指导**: 为用户提供冲稳保三个层次的专业选择
2. **意向专业查询**: 根据用户明确的意向专业进行精准匹配
3. **分数范围探索**: 了解更大分数范围内的专业分布
4. **快速专业筛选**: 无需等待招生计划数据，快速获取专业列表
5. **结构化数据处理**: 处理问答形式的用户输入数据

## 注意事项

1. **输入格式**: 必须使用新的结构化问答格式，不再支持文本输入
2. **意向专业**: 如果用户未填写意向专业，将返回所有符合条件的专业
3. **招生计划**: 返回的专业不包含招生计划数据，如需要请使用其他接口
4. **选科要求**: 仍然会根据选科要求筛选专业，确保推荐的准确性
5. **响应速度**: 由于取消招生计划查询，响应速度显著提升

## 后续优化建议

1. **分页支持**: 可以考虑添加分页参数支持
2. **排序选项**: 提供多种排序方式（按分数、按学校等级等）
3. **筛选参数**: 可以添加可选的筛选参数（如学校类型、地区等）
4. **异步处理**: 对于大量数据的查询，可以考虑异步处理机制
