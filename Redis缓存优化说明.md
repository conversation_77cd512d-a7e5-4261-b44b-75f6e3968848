# Redis缓存优化说明

## 问题描述

在当前的实现中，每次查询专业信息时都会执行数据库查询，这导致了大量的数据库查询操作，给数据库造成很大压力。由于专业数据不会经常变化，使用Redis缓存是一个很好的优化方案。

## 优化方案

我们通过以下步骤实现了Redis缓存优化：

1. **创建缓存配置类**：创建了`CeeMajorCacheConfig`类，用于配置专业数据的缓存，包括缓存名称、过期时间等。

2. **添加缓存注解**：为`CeeMajorServiceImpl`类中的关键方法添加了缓存注解：
   - `@Cacheable`：用于缓存方法的返回结果
   - `@CacheEvict`：用于在数据更新时清除缓存

3. **设置合理的缓存过期时间**：由于专业数据不会经常变化，我们设置了较长的缓存过期时间（1天）。

## 缓存的方法

我们为以下关键方法添加了缓存：

1. **getMajorByNameAndEducationLevel**：根据专业名称和教育级别查询专业信息
   ```java
   @Cacheable(value = CACHE_NAME_BY_NAME_AND_LEVEL, key = "#majorName + ':' + #educationLevel", unless = "#result == null")
   ```

2. **searchMajorsByName**：根据专业名称模糊搜索专业
   ```java
   @Cacheable(value = CACHE_NAME_SEARCH_BY_NAME, key = "#majorName + ':' + #educationLevel", unless = "#result == null or #result.isEmpty()")
   ```

3. **saveMajorInfoList**：保存专业数据时清除缓存
   ```java
   @CacheEvict(value = {CACHE_NAME_BY_NAME_AND_LEVEL, CACHE_NAME_SEARCH_BY_NAME}, allEntries = true)
   ```

## 缓存策略

1. **缓存键设计**：使用专业名称和教育级别作为缓存键，确保缓存的唯一性和准确性。

2. **缓存过期时间**：设置为1天，平衡了缓存效率和数据一致性。

3. **缓存条件**：使用`unless`属性避免缓存null值或空列表，减少无效缓存。

4. **缓存清除策略**：在数据更新时清除所有相关缓存，确保数据一致性。

## 预期效果

1. **减少数据库查询次数**：大部分专业查询将直接从Redis缓存中获取，而不是查询数据库。

2. **提高查询性能**：从Redis缓存中获取数据比从数据库查询快得多。

3. **降低数据库压力**：减少了对数据库的频繁访问，降低了数据库的负载。

4. **保持数据一致性**：在数据更新时清除缓存，确保用户始终能获取到最新的数据。

## 注意事项

1. **缓存空间占用**：专业数据量不大，不会占用太多Redis内存。

2. **缓存穿透防护**：使用`unless`属性避免缓存null值，防止缓存穿透。

3. **缓存一致性**：通过`@CacheEvict`注解在数据更新时清除缓存，确保数据一致性。
