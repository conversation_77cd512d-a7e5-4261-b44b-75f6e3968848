# 招生计划过滤配置示例
enrollment:
  filter:
    # 每批处理数量
    batch-size: 50
    
    # 最大并发批次数
    max-concurrent-batches: 4
    
    # 目标结果数量，达到后可提前退出
    target-result-count: 200
    
    # 最大处理数量，避免处理过多数据
    max-process-count: 1000
    
    # 单个批次超时时间（秒）
    batch-timeout-seconds: 30
    
    # 线程池关闭等待时间（秒）
    shutdown-timeout-seconds: 10
    
    # 是否启用性能监控
    enable-performance-monitoring: true
    
    # 是否启用优先级排序
    enable-priority-sort: true
    
    # 是否启用早期退出
    enable-early-exit: true

# 不同环境的配置建议
---
# 开发环境配置
spring:
  profiles: dev
enrollment:
  filter:
    batch-size: 20
    max-concurrent-batches: 2
    target-result-count: 50
    max-process-count: 500

---
# 测试环境配置  
spring:
  profiles: test
enrollment:
  filter:
    batch-size: 30
    max-concurrent-batches: 3
    target-result-count: 100
    max-process-count: 800

---
# 生产环境配置
spring:
  profiles: prod
enrollment:
  filter:
    batch-size: 50
    max-concurrent-batches: 4
    target-result-count: 200
    max-process-count: 1000
    batch-timeout-seconds: 60
    shutdown-timeout-seconds: 15
