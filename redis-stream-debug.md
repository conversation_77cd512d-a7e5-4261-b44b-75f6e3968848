# Redis Stream 消费者问题诊断

## 问题描述
`sendReportGenerateMessage` 发送后消费者没有消费消息

## 可能原因分析

### 1. 消费者组配置问题
- 消费者组名称：`yudao-server` (来自 spring.application.name)
- Stream Key: `system.report.generate`

### 2. 检查步骤

#### 步骤1: 检查 Redis 连接
```bash
# 连接到 Redis
redis-cli -h 110.42.111.212 -p 4928 -a mhhJJ4nhYeXWEdAC

# 检查 Stream 是否存在
XINFO STREAM system.report.generate

# 检查消费者组是否存在
XINFO GROUPS system.report.generate

# 检查消费者组中的消费者
XINFO CONSUMERS system.report.generate yudao-server
```

#### 步骤2: 检查消息是否发送成功
```bash
# 查看 Stream 中的消息
XRANGE system.report.generate - +

# 查看待处理的消息
XPENDING system.report.generate yudao-server
```

#### 步骤3: 检查应用启动日志
查找以下关键日志：
- `[redisStreamMessageListenerContainer][开始注册 StreamKey(system.report.generate) 对应的监听器]`
- `[redisStreamMessageListenerContainer][完成注册 StreamKey(system.report.generate) 对应的监听器]`

### 3. 常见问题及解决方案

#### 问题1: 消费者组不存在
**现象**: Redis 中没有创建消费者组
**解决**: 检查应用启动时是否有异常，确保 StreamMessageListenerContainer 正常启动

#### 问题2: 消费者没有注册
**现象**: 消费者组存在但没有消费者
**解决**: 检查 ReportGenerateConsumer 是否被 Spring 正确扫描和注册

#### 问题3: 消息处理异常
**现象**: 消息被消费但处理失败
**解决**: 检查消费者的 onMessage 方法是否有异常

#### 问题4: Redis 配置问题
**现象**: 无法连接到 Redis 或权限不足
**解决**: 检查 Redis 连接配置和权限

### 4. 调试建议

1. **添加更多日志**
   在 ReportGenerateConsumer.onMessage 方法开始处添加日志

2. **检查 Spring Bean 注册**
   确认 ReportGenerateConsumer 被正确注册为 Spring Bean

3. **检查 Redis 连接**
   确认应用能正常连接到 Redis

4. **手动测试消费**
   可以手动向 Stream 发送消息测试消费者是否工作

### 5. 临时解决方案

如果问题持续存在，可以考虑：
1. 重启应用
2. 清理 Redis Stream 数据重新开始
3. 检查是否有多个应用实例导致的冲突
