// 修改选科要求过滤逻辑，放宽过滤条件
// 将原来的完全匹配改为部分匹配

// 原代码：
// 如果用户有选科信息，判断是否符合选科要求
if (!userSubjects.isEmpty() && info.getSubjectSelection() != null) {
    // 解析选科要求，例如"首选物理，再选化学"
    Set<String> requiredSubjects = parseSubjectSelection(info.getSubjectSelection());

    // 判断用户是否选择了所有要求的科目
    if (!userSubjects.containsAll(requiredSubjects)) {
        // 如果用户没有选择所有要求的科目，跳过该专业
        log.debug("Skipping major {} as user subjects {} don't match required subjects {}",
                info.getMajorName(), userSubjects, requiredSubjects);
        continue;
    }
}

// 修改后的代码：
// 如果用户有选科信息，判断是否符合选科要求
if (!userSubjects.isEmpty() && info.getSubjectSelection() != null) {
    // 解析选科要求，例如"首选物理，再选化学"
    Set<String> requiredSubjects = parseSubjectSelection(info.getSubjectSelection());

    // 放宽选科要求过滤条件：如果没有任何匹配的科目，才跳过该专业
    // 计算用户选科与专业要求的交集
    Set<String> intersection = new HashSet<>(userSubjects);
    intersection.retainAll(requiredSubjects);
    
    if (intersection.isEmpty() && !requiredSubjects.isEmpty()) {
        // 如果交集为空且专业有选科要求，跳过该专业
        log.debug("Skipping major {} as user subjects {} don't match any of required subjects {}",
                info.getMajorName(), userSubjects, requiredSubjects);
        continue;
    }
}

// 修改模糊查询的数量限制
// 原代码：
// 限制模糊查询的数量，最多查询20个专业
List<String> limitedRemainingMajors = new ArrayList<>(remainingMajors);
if (limitedRemainingMajors.size() > 20) {
    log.info("Too many majors for fuzzy search, limiting to 20");
    limitedRemainingMajors = limitedRemainingMajors.subList(0, 20);
}

// 修改后的代码：
// 增加模糊查询的数量限制，最多查询100个专业
List<String> limitedRemainingMajors = new ArrayList<>(remainingMajors);
if (limitedRemainingMajors.size() > 100) {
    log.info("Too many majors for fuzzy search, limiting to 100");
    limitedRemainingMajors = limitedRemainingMajors.subList(0, 100);
}
