package cn.iocoder.yudao.module.system.controller.admin.user.vo.user;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.time.LocalDateTime;

@Schema(description = "管理后台 - 用户资源新增/修改 Request VO")
@Data
public class UserAssetsSaveReqVO {

    @Schema(description = "主键id", requiredMode = Schema.RequiredMode.REQUIRED, example = "6714")
    private Integer id;

    @Schema(description = "用户id", example = "13201")
    private Long userId;

    @Schema(description = "体验版次数", example = "8522")
    private Integer trailCount;

    @Schema(description = "体验版剩余次数", example = "1251")
    private Integer trailLeftCount;

    @Schema(description = "专业版次数", example = "10314")
    private Integer psCount;

    @Schema(description = "专业版剩余次数", example = "21260")
    private Integer psLeftCount;

    @Schema(description = "内容查看次数", example = "23296")
    private Integer contentCount;

    @Schema(description = "内容查看剩余次数", example = "18759")
    private Integer contentLeftCount;

    @Schema(description = "问答次数", example = "3143")
    private Integer askCount;

    @Schema(description = "问答剩余次数", example = "17518")
    private Integer askLeftCount;

    @Schema(description = "体验版最新购买时间")
    private LocalDateTime trailUpdateTime;

    @Schema(description = "专业版最新购买时间")
    private LocalDateTime psUpdateTime;

    @Schema(description = "内容会员到期时间")
    private LocalDateTime contentEndTime;

    @Schema(description = "内容会员开始时间")
    private LocalDateTime contentStartTime;

}