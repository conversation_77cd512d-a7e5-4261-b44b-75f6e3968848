package cn.iocoder.yudao.module.system.controller.admin.userassetscode.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotEmpty;

@Schema(description = "管理后台 - 用户资源兑换码兑换 Request VO")
@Data
public class UserAssetsCodeRedeemReqVO {

    @Schema(description = "兑换码", requiredMode = Schema.RequiredMode.REQUIRED, example = "A1B2C3D4E5F6G7H8")
    @NotEmpty(message = "兑换码不能为空")
    private String code;

    @Schema(description = "用户id", requiredMode = Schema.RequiredMode.REQUIRED, example = "1024")
    private Long userId;

    @Schema(description = "用户名称", example = "张三")
    private String userName;
}
