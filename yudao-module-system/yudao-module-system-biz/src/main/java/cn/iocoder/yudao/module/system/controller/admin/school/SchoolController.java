package cn.iocoder.yudao.module.system.controller.admin.school;

import cn.iocoder.yudao.module.system.controller.admin.school.vo.SchoolPageReqVO;
import cn.iocoder.yudao.module.system.controller.admin.school.vo.SchoolRespVO;
import cn.iocoder.yudao.module.system.controller.admin.school.vo.SchoolSaveReqVO;
import cn.iocoder.yudao.module.system.dal.dataobject.school.SchoolDO;
import cn.iocoder.yudao.module.system.service.school.SchoolService;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import javax.annotation.security.PermitAll;
import javax.validation.constraints.*;
import javax.validation.*;
import javax.servlet.http.*;
import java.util.*;
import java.io.IOException;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

import cn.iocoder.yudao.framework.excel.core.util.ExcelUtils;

import cn.iocoder.yudao.framework.apilog.core.annotation.ApiAccessLog;
import static cn.iocoder.yudao.framework.apilog.core.enums.OperateTypeEnum.*;


@Tag(name = "管理后台 - 院校")
@RestController
@RequestMapping("/kf/school")
@Validated
public class SchoolController {

    @Resource
    private SchoolService schoolService;

    @PostMapping("/create")
    @Operation(summary = "创建院校")
    @PermitAll
    public CommonResult<Long> createSchool(@Valid @RequestBody SchoolSaveReqVO createReqVO) {
        return success(schoolService.createSchool(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新院校")
    @PermitAll
    public CommonResult<Boolean> updateSchool(@Valid @RequestBody SchoolSaveReqVO updateReqVO) {
        schoolService.updateSchool(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除院校")
    @Parameter(name = "id", description = "编号", required = true)
    @PermitAll
    public CommonResult<Boolean> deleteSchool(@RequestParam("id") Long id) {
        schoolService.deleteSchool(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得院校")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PermitAll
    public CommonResult<SchoolRespVO> getSchool(@RequestParam("id") Long id) {
        SchoolDO school = schoolService.getSchool(id);
        return success(BeanUtils.toBean(school, SchoolRespVO.class));
    }

    @GetMapping("/getAllSchools")
    @Operation(summary = "获得院校")
    @Parameter(name = "name", description = "学校名称", required = false)
    @PermitAll
    public CommonResult<List<Map<String, Object>>> getSchool(String name) {
        List<Map<String, Object>> schools = schoolService.getAllSchool(name);
        return success(schools);
    }


    @GetMapping("/getAll")
    @Operation(summary = "获得院校")
    @Parameter(name = "name", description = "学校名称", required = false)
    @PermitAll
    public CommonResult<List<SchoolRespVO>> getAllSchools(String name) {
        List<SchoolDO> schools = schoolService.getAllSchools(name);
        return success(BeanUtils.toBean(schools, SchoolRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得院校分页")
    @PermitAll
    public CommonResult<PageResult<SchoolRespVO>> getSchoolPage(@Valid SchoolPageReqVO pageReqVO) {
        PageResult<SchoolDO> pageResult = schoolService.getSchoolPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, SchoolRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出院校 Excel")
    @PermitAll
    @ApiAccessLog(operateType = EXPORT)
    public void exportSchoolExcel(@Valid SchoolPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<SchoolDO> list = schoolService.getSchoolPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "院校.xls", "数据", SchoolRespVO.class,
                        BeanUtils.toBean(list, SchoolRespVO.class));
    }

}