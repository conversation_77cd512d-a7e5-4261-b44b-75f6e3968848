package cn.iocoder.yudao.module.system.dal.mysql.normalquestion;

import java.util.*;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.module.system.controller.admin.normalquestion.vo.NormalQuestionPageReqVO;
import cn.iocoder.yudao.module.system.dal.dataobject.normalquestion.NormalQuestionDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * AI 报告 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface NormalQuestionMapper extends BaseMapperX<NormalQuestionDO> {

    default PageResult<NormalQuestionDO> selectPage(NormalQuestionPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<NormalQuestionDO>()
                .eqIfPresent(NormalQuestionDO::getContent, reqVO.getContent())
                .eqIfPresent(NormalQuestionDO::getType, reqVO.getType())
                .eqIfPresent(NormalQuestionDO::getParentType, reqVO.getParentType())
                .eqIfPresent(NormalQuestionDO::getQuestion, reqVO.getQuestion())
                .eqIfPresent(NormalQuestionDO::getVersion, reqVO.getVersion())
                .betweenIfPresent(NormalQuestionDO::getCreateTime, reqVO.getCreateTime())
                .eqIfPresent(NormalQuestionDO::getStar, reqVO.getStar())
                .orderByDesc(NormalQuestionDO::getId));
    }

}