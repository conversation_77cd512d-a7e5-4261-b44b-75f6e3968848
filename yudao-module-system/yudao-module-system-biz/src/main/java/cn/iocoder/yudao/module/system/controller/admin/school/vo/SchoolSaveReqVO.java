package cn.iocoder.yudao.module.system.controller.admin.school.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import javax.validation.constraints.*;

@Schema(description = "管理后台 - 院校新增/修改 Request VO")
@Data
public class SchoolSaveReqVO {

    @Schema(description = "主键id", requiredMode = Schema.RequiredMode.REQUIRED, example = "882")
    private Long id;

    @Schema(description = "名称", example = "李四")
    private String name;

    @Schema(description = "省份")
    private String province;

    @Schema(description = "省份名")
    private String provinceName;

    @Schema(description = "排序")
    private Integer sort;

}