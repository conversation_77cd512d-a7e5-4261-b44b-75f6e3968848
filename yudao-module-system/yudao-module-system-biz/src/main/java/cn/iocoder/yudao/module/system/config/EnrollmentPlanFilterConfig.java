package cn.iocoder.yudao.module.system.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 招生计划过滤配置
 */
@Data
@Component
@ConfigurationProperties(prefix = "enrollment.filter")
public class EnrollmentPlanFilterConfig {

    /**
     * 每批处理数量
     */
    private int batchSize = 50;

    /**
     * 最大并发批次数
     */
    private int maxConcurrentBatches = 4;

    /**
     * 目标结果数量，达到后可提前退出
     */
    private int targetResultCount = 200;

    /**
     * 最大处理数量，避免处理过多数据
     */
    private int maxProcessCount = 1000;

    /**
     * 单个批次超时时间（秒）
     */
    private int batchTimeoutSeconds = 30;

    /**
     * 线程池关闭等待时间（秒）
     */
    private int shutdownTimeoutSeconds = 10;

    /**
     * 是否启用性能监控
     */
    private boolean enablePerformanceMonitoring = true;

    /**
     * 是否启用优先级排序
     */
    private boolean enablePrioritySort = true;

    /**
     * 是否启用早期退出
     */
    private boolean enableEarlyExit = true;

    /**
     * 验证配置参数
     */
    public void validate() {
        if (batchSize <= 0) {
            throw new IllegalArgumentException("batchSize must be positive");
        }
        if (maxConcurrentBatches <= 0) {
            throw new IllegalArgumentException("maxConcurrentBatches must be positive");
        }
        if (targetResultCount <= 0) {
            throw new IllegalArgumentException("targetResultCount must be positive");
        }
        if (maxProcessCount <= 0) {
            throw new IllegalArgumentException("maxProcessCount must be positive");
        }
        if (batchTimeoutSeconds <= 0) {
            throw new IllegalArgumentException("batchTimeoutSeconds must be positive");
        }
        if (shutdownTimeoutSeconds <= 0) {
            throw new IllegalArgumentException("shutdownTimeoutSeconds must be positive");
        }
    }

    /**
     * 获取推荐的配置（根据数据量）
     */
    public static EnrollmentPlanFilterConfig getRecommendedConfig(int dataSize) {
        EnrollmentPlanFilterConfig config = new EnrollmentPlanFilterConfig();
        
        if (dataSize <= 100) {
            // 小数据量：简单配置
            config.setBatchSize(20);
            config.setMaxConcurrentBatches(2);
            config.setTargetResultCount(50);
        } else if (dataSize <= 500) {
            // 中等数据量：平衡配置
            config.setBatchSize(30);
            config.setMaxConcurrentBatches(3);
            config.setTargetResultCount(100);
        } else if (dataSize <= 2000) {
            // 大数据量：高性能配置
            config.setBatchSize(50);
            config.setMaxConcurrentBatches(4);
            config.setTargetResultCount(200);
        } else {
            // 超大数据量：最大性能配置
            config.setBatchSize(100);
            config.setMaxConcurrentBatches(6);
            config.setTargetResultCount(300);
            config.setMaxProcessCount(2000);
        }
        
        return config;
    }

    @Override
    public String toString() {
        return String.format(
            "EnrollmentPlanFilterConfig{batchSize=%d, maxConcurrentBatches=%d, targetResultCount=%d, maxProcessCount=%d}",
            batchSize, maxConcurrentBatches, targetResultCount, maxProcessCount
        );
    }
}
