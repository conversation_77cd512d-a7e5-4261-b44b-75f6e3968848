package cn.iocoder.yudao.module.system.dal.mysql.user;

import java.util.*;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.module.system.controller.admin.user.vo.user.UserAssetsPageReqVO;
import cn.iocoder.yudao.module.system.dal.dataobject.user.UserAssetsDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 用户资源 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface UserAssetsMapper extends BaseMapperX<UserAssetsDO> {

    default PageResult<UserAssetsDO> selectPage(UserAssetsPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<UserAssetsDO>()
                .eqIfPresent(UserAssetsDO::getUserId, reqVO.getUserId())
                .eqIfPresent(UserAssetsDO::getTrailCount, reqVO.getTrailCount())
                .eqIfPresent(UserAssetsDO::getTrailLeftCount, reqVO.getTrailLeftCount())
                .eqIfPresent(UserAssetsDO::getPsCount, reqVO.getPsCount())
                .eqIfPresent(UserAssetsDO::getPsLeftCount, reqVO.getPsLeftCount())
                .eqIfPresent(UserAssetsDO::getContentCount, reqVO.getContentCount())
                .eqIfPresent(UserAssetsDO::getContentLeftCount, reqVO.getContentLeftCount())
                .eqIfPresent(UserAssetsDO::getAskCount, reqVO.getAskCount())
                .eqIfPresent(UserAssetsDO::getAskLeftCount, reqVO.getAskLeftCount())
                .betweenIfPresent(UserAssetsDO::getTrailUpdateTime, reqVO.getTrailUpdateTime())
                .betweenIfPresent(UserAssetsDO::getPsUpdateTime, reqVO.getPsUpdateTime())
                .betweenIfPresent(UserAssetsDO::getContentEndTime, reqVO.getContentEndTime())
                .betweenIfPresent(UserAssetsDO::getContentStartTime, reqVO.getContentStartTime())
                .betweenIfPresent(UserAssetsDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(UserAssetsDO::getId));
    }

}