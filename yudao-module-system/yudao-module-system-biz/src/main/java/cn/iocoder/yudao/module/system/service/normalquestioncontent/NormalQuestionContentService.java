package cn.iocoder.yudao.module.system.service.normalquestioncontent;

import java.util.*;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.system.controller.admin.normalquestioncontent.vo.NormalQuestionContentPageReqVO;
import cn.iocoder.yudao.module.system.controller.admin.normalquestioncontent.vo.NormalQuestionContentSaveReqVO;
import cn.iocoder.yudao.module.system.dal.dataobject.normalquestioncontent.NormalQuestionContentDO;

import javax.validation.Valid;

/**
 * AI 报告 Service 接口
 *
 * <AUTHOR>
 */
public interface NormalQuestionContentService {

    /**
     * 创建AI 报告
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createNormalQuestionContent(@Valid NormalQuestionContentSaveReqVO createReqVO);

    /**
     * 更新AI 报告
     *
     * @param updateReqVO 更新信息
     */
    void updateNormalQuestionContent(@Valid NormalQuestionContentSaveReqVO updateReqVO);

    /**
     * 删除AI 报告
     *
     * @param id 编号
     */
    void deleteNormalQuestionContent(Long id);

    /**
     * 获得AI 报告
     *
     * @param id 编号
     * @return AI 报告
     */
    NormalQuestionContentDO getNormalQuestionContent(Long id);

    /**
     * 获得AI 报告分页
     *
     * @param pageReqVO 分页查询
     * @return AI 报告分页
     */
    PageResult<NormalQuestionContentDO> getNormalQuestionContentPage(NormalQuestionContentPageReqVO pageReqVO);

    List<NormalQuestionContentDO> getCategoryList(String parentType);

    NormalQuestionContentDO getNormalQuestionContentByName(String name);

    /**
     * 读取所有专业信息并生成AI报告内容
     *
     * @return 处理的专业数量
     */
    int generateMajorContents();
}