package cn.iocoder.yudao.module.system.service.user;

import java.util.*;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.system.controller.admin.user.vo.user.UserAssetsPageReqVO;
import cn.iocoder.yudao.module.system.controller.admin.user.vo.user.UserAssetsSaveReqVO;
import cn.iocoder.yudao.module.system.dal.dataobject.user.UserAssetsDO;

import javax.validation.Valid;

/**
 * 用户资源 Service 接口
 *
 * <AUTHOR>
 */
public interface UserAssetsService {

    /**
     * 创建用户资源
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Integer createUserAssets(@Valid UserAssetsSaveReqVO createReqVO);

    /**
     * 更新用户资源
     *
     * @param updateReqVO 更新信息
     */
    void updateUserAssets(@Valid UserAssetsSaveReqVO updateReqVO);

    /**
     * 删除用户资源
     *
     * @param id 编号
     */
    void deleteUserAssets(Integer id);

    /**
     * 获得用户资源
     *
     * @param id 编号
     * @return 用户资源
     */
    UserAssetsDO getUserAssets(Integer id);

    /**
     * 获得用户资源分页
     *
     * @param pageReqVO 分页查询
     * @return 用户资源分页
     */
    PageResult<UserAssetsDO> getUserAssetsPage(UserAssetsPageReqVO pageReqVO);

    UserAssetsDO getUserAssetsByUserId(Long userId);

    /**
     * 检查用户内容会员剩余次数
     *
     * @param userId 用户ID
     * @return 如果检查通过，返回true；否则抛出异常
     */
    boolean validateUserContentMembership(Long userId);

    /**
     * 检查用户问答会员剩余次数
     *
     * @param userId 用户ID
     * @return 如果检查通过，返回true；否则抛出异常
     */
    boolean validateAskMembership(Long userId);
}