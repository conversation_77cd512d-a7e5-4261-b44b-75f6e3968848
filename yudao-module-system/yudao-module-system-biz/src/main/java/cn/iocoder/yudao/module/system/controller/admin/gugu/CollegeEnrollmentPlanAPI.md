# 历年高校招生计划查询接口文档

## 接口概述

历年高校招生计划查询接口提供了查询全国各高校历年招生计划数据的功能，包括招生人数、专业信息、录取批次等详细信息。

## 基础信息

- **API Key**: `8N9BFH4DPAXWL4JJB5WAJYBJ3QCL77FM`
- **基础路径**: `/system/college-enrollment-plan`
- **数据源**: 咕咕数据平台

## 接口列表

### 1. 查询历年高校招生计划数据

**接口地址**: `GET /system/college-enrollment-plan/query`

**接口描述**: 根据条件查询历年高校招生计划数据

**请求参数**:

| 参数名 | 类型 | 必填 | 默认值 | 说明 |
|--------|------|------|--------|------|
| appkey | String | 是 | - | API密钥 |
| pageIndex | Integer | 是 | - | 页码，从1开始 |
| pageSize | Integer | 是 | - | 每页数据量，取值范围10-100 |
| collegeMajorName | String | 否 | - | 高校专业名称，支持模糊查询 |
| year | Integer | 否 | 0 | 招生年份，0表示获取所有年份 |
| schoolName | String | 否 | - | 高校名称，支持模糊查询 |
| provinceName | String | 否 | - | 招生省份 |
| classOne | String | 否 | - | 专业大类 |
| classTwo | String | 否 | - | 专业小类 |
| batchName | String | 否 | - | 录取批次 |
| type | String | 否 | - | 文理综合类别 |
| schoolUuid | String | 否 | - | 高校唯一ID |

**请求示例**:
```
GET /system/college-enrollment-plan/query?appkey=8N9BFH4DPAXWL4JJB5WAJYBJ3QCL77FM&pageIndex=1&pageSize=10&collegeMajorName=计算机科学与技术&year=2024&schoolName=北京大学&provinceName=北京
```

**响应示例**:
```json
{
  "code": 200,
  "data": {
    "enrollmentPlanList": [
      {
        "inSchoolYears": "4",
        "classOne": "工学",
        "classTwo": "计算机类",
        "batchName": "本科一批",
        "type": "理科",
        "schoolName": "北京大学",
        "enrollmentNumbers": 30,
        "schoolUuid": "c24a67f87405b82bec08a5638c32f282",
        "courseSelectionRequirements": "首选物理，再选化学",
        "collegeMajorName": "计算机科学与技术",
        "collegeMajorCode": "080901",
        "year": 2024,
        "provinceName": "北京"
      }
    ],
    "totalCount": 150,
    "pageIndex": 1,
    "pageSize": 10,
    "totalPages": 15
  },
  "msg": "操作成功"
}
```

### 2. 验证API Key

**接口地址**: `GET /system/college-enrollment-plan/validate-key`

**接口描述**: 验证API Key是否有效

**请求参数**:

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| appkey | String | 是 | 待验证的API密钥 |

**请求示例**:
```
GET /system/college-enrollment-plan/validate-key?appkey=8N9BFH4DPAXWL4JJB5WAJYBJ3QCL77FM
```

**响应示例**:
```json
{
  "code": 200,
  "data": true,
  "msg": "操作成功"
}
```

### 3. 获取录取批次枚举值

**接口地址**: `GET /system/college-enrollment-plan/batch-names`

**接口描述**: 获取所有可用的录取批次选项

**请求示例**:
```
GET /system/college-enrollment-plan/batch-names
```

**响应示例**:
```json
{
  "code": 200,
  "data": [
    "专科批", "专科批A段", "本科一批", "本科二批", "本科提前批", "艺术类本科批"
  ],
  "msg": "操作成功"
}
```

### 4. 获取文理综合类别枚举值

**接口地址**: `GET /system/college-enrollment-plan/types`

**接口描述**: 获取所有可用的文理综合类别选项

**请求示例**:
```
GET /system/college-enrollment-plan/types
```

**响应示例**:
```json
{
  "code": 200,
  "data": [
    "理科", "文科", "综合", "艺术类", "体育类"
  ],
  "msg": "操作成功"
}
```

## 错误码说明

| 错误码 | 说明 |
|--------|------|
| 403 | 无效的APPKEY |
| 500 | 服务器内部错误 |

## 使用注意事项

1. **API Key验证**: 所有查询接口都需要提供有效的API Key
2. **分页限制**: 每页数据量限制在10-100条之间
3. **请求频率**: 建议控制请求频率，避免过于频繁的调用
4. **参数编码**: 中文参数会自动进行URL编码处理
5. **数据时效性**: 招生计划数据会定期更新，建议定期重新获取

## 常见查询场景

### 场景1: 查询某高校某专业的招生计划
```
GET /system/college-enrollment-plan/query?appkey=YOUR_KEY&pageIndex=1&pageSize=20&schoolName=清华大学&collegeMajorName=计算机科学与技术
```

### 场景2: 查询某省份某年度的招生计划
```
GET /system/college-enrollment-plan/query?appkey=YOUR_KEY&pageIndex=1&pageSize=50&provinceName=北京&year=2024
```

### 场景3: 查询某录取批次的招生计划
```
GET /system/college-enrollment-plan/query?appkey=YOUR_KEY&pageIndex=1&pageSize=30&batchName=本科一批&type=理科
```

## 数据导入接口

### 1. 批量导入历年高校招生计划数据

**接口地址**: `POST /system/college-enrollment-plan/batch-import`

**接口描述**: 批量导入2024、2023、2022年全国各省份的招生计划数据到数据库

**请求参数**:
| 参数名 | 类型 | 必填 | 默认值 | 说明 |
|--------|------|------|--------|------|
| appkey | String | 是 | - | API密钥 |
| years | String | 否 | 2024,2023,2022 | 年份列表，用逗号分隔 |
| useMultiThread | Boolean | 否 | true | 是否使用多线程导入 |

**请求示例**:
```
POST /system/college-enrollment-plan/batch-import?appkey=8N9BFH4DPAXWL4JJB5WAJYBJ3QCL77FM&years=2024,2023,2022&useMultiThread=true
```

**响应示例**:
```json
{
  "code": 200,
  "data": {
    "success": true,
    "totalImported": 150000,
    "totalProvinces": 93,
    "processedProvinces": 93,
    "duration": 1800000,
    "message": "批量导入完成，共处理93个省份，导入150000条数据，耗时1800000毫秒"
  }
}
```

### 2. 按省份导入指定年份的招生计划数据

**接口地址**: `POST /system/college-enrollment-plan/import-by-province`

**接口描述**: 导入指定省份和年份的招生计划数据

**请求参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| appkey | String | 是 | API密钥 |
| provinceName | String | 是 | 省份名称，如"北京" |
| year | Integer | 是 | 年份，如2024 |

**请求示例**:
```
POST /system/college-enrollment-plan/import-by-province?appkey=8N9BFH4DPAXWL4JJB5WAJYBJ3QCL77FM&provinceName=北京&year=2024
```

### 3. 获取导入进度统计

**接口地址**: `GET /system/college-enrollment-plan/import-progress`

**接口描述**: 获取数据导入的进度统计信息

**请求参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| appkey | String | 是 | API密钥 |

**响应示例**:
```json
{
  "code": 200,
  "data": {
    "success": true,
    "yearCounts": {
      "2022": 45000,
      "2023": 48000,
      "2024": 50000
    },
    "provinceCounts": {
      "北京": 2500,
      "上海": 2200,
      "广东": 8500
    },
    "totalProvinces": 31
  }
}
```

### 4. 从数据库查询招生计划数据

**接口地址**: `GET /system/college-enrollment-plan/query-from-database`

**接口描述**: 从本地数据库查询已导入的招生计划数据

**请求参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| appkey | String | 是 | API密钥 |
| schoolName | String | 否 | 学校名称，支持模糊查询 |
| majorName | String | 否 | 专业名称，支持模糊查询 |
| provinceName | String | 否 | 省份名称 |
| year | Integer | 否 | 年份 |

## 数据库表结构

### system_college_enrollment_plan 表

| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | bigint | 主键ID |
| province_name | varchar(50) | 招生省份 |
| school_uuid | varchar(64) | 高校唯一ID |
| school_name | varchar(100) | 高校名称 |
| college_major_name | varchar(100) | 高校专业名称 |
| college_major_code | varchar(20) | 高校专业代码 |
| year | int | 招生年份 |
| enrollment_numbers | int | 招生人数 |
| in_school_years | varchar(10) | 学制年限 |
| class_one | varchar(50) | 专业大类 |
| class_two | varchar(50) | 专业小类 |
| batch_name | varchar(50) | 录取批次 |
| type | varchar(20) | 文理综合类别 |
| course_selection_requirements | varchar(200) | 选科要求 |

### 5. 测试单个省份数据导入

**接口地址**: `POST /system/college-enrollment-plan/test-single-province`

**接口描述**: 测试单个省份的数据导入功能，用于验证导入流程

**请求参数**:
| 参数名 | 类型 | 必填 | 默认值 | 说明 |
|--------|------|------|--------|------|
| appkey | String | 是 | - | API密钥 |
| provinceName | String | 否 | 北京 | 省份名称 |
| year | Integer | 否 | 2024 | 年份 |

**请求示例**:
```
POST /system/college-enrollment-plan/test-single-province?appkey=8N9BFH4DPAXWL4JJB5WAJYBJ3QCL77FM&provinceName=北京&year=2024
```

**响应示例**:
```json
{
  "code": 200,
  "data": {
    "success": true,
    "provinceName": "北京",
    "year": 2024,
    "importedCount": 2500,
    "duration": 45000,
    "message": "成功导入北京省2024年的2500条招生计划数据，耗时45000毫秒"
  }
}
```

## 使用流程

1. **测试验证**: 先使用测试接口验证单个省份的导入功能
2. **首次使用**: 调用批量导入接口导入历年数据
3. **进度监控**: 使用进度统计接口监控导入状态
4. **数据查询**: 使用数据库查询接口获取本地数据
5. **增量更新**: 使用按省份导入接口更新特定数据

## 性能优化特性

1. **请求频率控制**: 自动控制API请求频率，避免触发服务器限制
2. **多线程处理**: 支持多线程并发获取数据，提高导入效率
3. **异步保存**: 获取数据后立即异步保存到数据库，避免数据丢失
4. **分批处理**: 大数据量分批保存，避免内存溢出
5. **错误恢复**: 单个请求失败不影响整体导入流程
6. **重复检查**: 自动检查重复数据，避免重复导入

## 注意事项

1. **服务器限制**: 由于服务器有请求频率限制，建议使用较小的并发数
2. **数据量大**: 全国数据量较大，建议分批次或分省份导入
3. **网络稳定**: 确保网络连接稳定，避免导入过程中断
4. **存储空间**: 确保数据库有足够的存储空间
5. **监控日志**: 关注日志输出，及时发现和处理异常
