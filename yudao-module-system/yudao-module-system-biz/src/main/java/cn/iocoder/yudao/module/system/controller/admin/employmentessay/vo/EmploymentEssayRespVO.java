package cn.iocoder.yudao.module.system.controller.admin.employmentessay.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;
import com.alibaba.excel.annotation.*;
import cn.iocoder.yudao.framework.excel.core.annotations.DictFormat;
import cn.iocoder.yudao.framework.excel.core.convert.DictConvert;

@Schema(description = "管理后台 - 就业文章 Response VO")
@Data
@ExcelIgnoreUnannotated
public class EmploymentEssayRespVO {

    @Schema(description = "主键id", requiredMode = Schema.RequiredMode.REQUIRED, example = "31498")
    @ExcelProperty("主键id")
    private Long id;

    @Schema(description = "就业方向名称", example = "王五")
    @ExcelProperty("就业方向名称")
    private String dirName;

    @Schema(description = "就业方向id", example = "3401")
    @ExcelProperty("就业方向id")
    private Integer dirId;

    @Schema(description = "名称", example = "芋艿")
    @ExcelProperty("名称")
    private String name;

    @Schema(description = "对口院校id", example = "23884")
    @ExcelProperty("对口院校id")
    private String schoolId;

    @Schema(description = "对口院校名称", example = "王五")
    @ExcelProperty("对口院校名称")
    private String schoolName;

    @Schema(description = "文章内容")
    @ExcelProperty("文章内容")
    private String content;

    @Schema(description = "状态", example = "2")
    @ExcelProperty(value = "状态", converter = DictConvert.class)
    @DictFormat("kf_type") // TODO 代码优化：建议设置到对应的 DictTypeConstants 枚举类中
    private Integer status;

    @Schema(description = "排序")
    @ExcelProperty("排序")
    private Integer sort;

    @Schema(description = "创建时间")
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}