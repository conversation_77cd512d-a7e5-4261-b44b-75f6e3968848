package cn.iocoder.yudao.module.system.controller.admin.answerRecord.vo;

import lombok.*;
import io.swagger.v3.oas.annotations.media.Schema;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 用户回答记录分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class AnswerRecordPageReqVO extends PageParam {

    @Schema(description = "用户Id", example = "6060")
    private Integer userId;

    @Schema(description = "上一次回答的问题Id", example = "20408")
    private Integer lastAnswerQuestionId;

    @Schema(description = "用户第多少次答题")
    private Integer answerNo;

    @Schema(description = "状态", example = "2")
    private Integer status;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;
    /**
     * 类型 1体验版 2专业版
     */
    private Integer type;
}