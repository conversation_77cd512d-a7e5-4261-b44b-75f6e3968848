package cn.iocoder.yudao.module.system.dal.dataobject.question;

import lombok.*;

import java.util.*;
import java.time.LocalDateTime;
import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.*;
import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;

/**
 * 问题 DO
 *
 * <AUTHOR>
 */
@TableName("tb_question")
@KeySequence("tb_question_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class QuestionDO extends BaseDO {

    /**
     * 主键id
     */
    @TableId
    private Integer id;
    /**
     * 问题内容
     */
    private String content;
    /**
     * 问题类型 1单选题 2多选题 3问答题
     * <p>
     */
    private Integer type;
    /**
     * 是否必答题 1必答 0 可不回答
     * <p>
     */
    private Integer isNecessary;
    /**
     * 状态 1可用 0 不可用
     * <p>
     */
    private Integer status;
    /**
     * 问题顺序
     */
    private Integer sort;
    /**
     * 是否是体验版 1是 0不是
     */
    private Integer isTest;
    /**
     * 是否是专业版 1是 0不是
     */
    private Integer isPro;
}