package cn.iocoder.yudao.module.system.dal.mysql.employmentdir;

import java.util.*;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.module.system.controller.admin.employmentdir.vo.EmploymentDirPageReqVO;
import cn.iocoder.yudao.module.system.dal.dataobject.employmentdir.EmploymentDirDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 就业方向 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface EmploymentDirMapper extends BaseMapperX<EmploymentDirDO> {

    default PageResult<EmploymentDirDO> selectPage(EmploymentDirPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<EmploymentDirDO>()
                .likeIfPresent(EmploymentDirDO::getName, reqVO.getName())
                .eqIfPresent(EmploymentDirDO::getStatus, reqVO.getStatus())
                .betweenIfPresent(EmploymentDirDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(EmploymentDirDO::getId));
    }

}