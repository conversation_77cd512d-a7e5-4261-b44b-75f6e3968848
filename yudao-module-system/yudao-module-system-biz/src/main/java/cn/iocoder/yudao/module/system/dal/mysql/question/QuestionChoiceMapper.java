package cn.iocoder.yudao.module.system.dal.mysql.question;


import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.module.system.dal.dataobject.question.QuestionChoiceDO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 问题选项 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface QuestionChoiceMapper extends BaseMapperX<QuestionChoiceDO> {

    default List<QuestionChoiceDO> selectListByQuestionId(Integer questionId) {
        return selectList(QuestionChoiceDO::getQuestionId, questionId);
    }

    default int deleteByQuestionId(Integer questionId) {
        return delete(QuestionChoiceDO::getQuestionId, questionId);
    }

}
