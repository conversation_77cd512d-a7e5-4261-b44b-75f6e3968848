package cn.iocoder.yudao.module.system.dal.dataobject.answer;

import lombok.*;
import java.util.*;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.*;
import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;

/**
 * 用户答案 DO
 *
 * <AUTHOR>
 */
@TableName("tb_user_answer")
@KeySequence("tb_user_answer_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UserAnswerDO extends BaseDO {

    /**
     * 主键id,即答案Id
     */
    @TableId
    private Integer id;
    /**
     * 用户Id
     */
    private Integer userId;
    /**
     * 问题id
     */
    private Integer questionId;
    /**
     * 用户手写内容
     */
    private String writeContent;
    /**
     * 用户所选择答案Id集合，用逗号连接
     */
    private String answerChoices;
    /**
     * 用户第多少次答题
     */
    private Integer answerNo;

}