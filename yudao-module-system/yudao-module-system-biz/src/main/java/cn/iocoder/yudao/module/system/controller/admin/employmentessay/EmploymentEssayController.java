package cn.iocoder.yudao.module.system.controller.admin.employmentessay;

import cn.iocoder.yudao.module.system.controller.admin.employmentessay.vo.EmploymentEssayPageReqVO;
import cn.iocoder.yudao.module.system.controller.admin.employmentessay.vo.EmploymentEssayRespVO;
import cn.iocoder.yudao.module.system.controller.admin.employmentessay.vo.EmploymentEssaySaveReqVO;
import cn.iocoder.yudao.module.system.dal.dataobject.employmentessay.EmploymentEssayDO;
import cn.iocoder.yudao.module.system.service.employmentessay.EmploymentEssayService;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import javax.annotation.security.PermitAll;
import javax.validation.*;
import javax.servlet.http.*;
import java.util.*;
import java.io.IOException;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

import cn.iocoder.yudao.framework.excel.core.util.ExcelUtils;

import cn.iocoder.yudao.framework.apilog.core.annotation.ApiAccessLog;
import static cn.iocoder.yudao.framework.apilog.core.enums.OperateTypeEnum.*;


@Tag(name = "管理后台 - 就业文章")
@RestController
@RequestMapping("/kf/employment-essay")
@Validated
public class EmploymentEssayController {

    @Resource
    private EmploymentEssayService employmentEssayService;

    @PostMapping("/create")
    @Operation(summary = "创建就业文章")
    @PreAuthorize("@ss.hasPermission('kf:employment-essay:create')")
    public CommonResult<Long> createEmploymentEssay(@Valid @RequestBody EmploymentEssaySaveReqVO createReqVO) {
        return success(employmentEssayService.createEmploymentEssay(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新就业文章")
    @PreAuthorize("@ss.hasPermission('kf:employment-essay:update')")
    public CommonResult<Boolean> updateEmploymentEssay(@Valid @RequestBody EmploymentEssaySaveReqVO updateReqVO) {
        employmentEssayService.updateEmploymentEssay(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除就业文章")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('kf:employment-essay:delete')")
    public CommonResult<Boolean> deleteEmploymentEssay(@RequestParam("id") Long id) {
        employmentEssayService.deleteEmploymentEssay(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得就业文章")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('kf:employment-essay:query')")
    public CommonResult<EmploymentEssayRespVO> getEmploymentEssay(@RequestParam("id") Long id) {
        EmploymentEssayDO employmentEssay = employmentEssayService.getEmploymentEssay(id);
        return success(BeanUtils.toBean(employmentEssay, EmploymentEssayRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得就业文章分页")
    @PermitAll
    public CommonResult<PageResult<EmploymentEssayRespVO>> getEmploymentEssayPage(@Valid EmploymentEssayPageReqVO pageReqVO) {
        PageResult<EmploymentEssayDO> pageResult = employmentEssayService.getEmploymentEssayPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, EmploymentEssayRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出就业文章 Excel")
    @PreAuthorize("@ss.hasPermission('kf:employment-essay:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportEmploymentEssayExcel(@Valid EmploymentEssayPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<EmploymentEssayDO> list = employmentEssayService.getEmploymentEssayPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "就业文章.xls", "数据", EmploymentEssayRespVO.class,
                        BeanUtils.toBean(list, EmploymentEssayRespVO.class));
    }

}