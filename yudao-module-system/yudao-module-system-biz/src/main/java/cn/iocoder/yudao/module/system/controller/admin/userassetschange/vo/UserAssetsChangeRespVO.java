package cn.iocoder.yudao.module.system.controller.admin.userassetschange.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;
import com.alibaba.excel.annotation.*;
import cn.iocoder.yudao.framework.excel.core.annotations.DictFormat;
import cn.iocoder.yudao.framework.excel.core.convert.DictConvert;

@Schema(description = "管理后台 - 用户资源兑换套餐 Response VO")
@Data
@ExcelIgnoreUnannotated
public class UserAssetsChangeRespVO {

    @Schema(description = "主键id", requiredMode = Schema.RequiredMode.REQUIRED, example = "7273")
    @ExcelProperty("主键id")
    private Integer id;

    @Schema(description = "客户id", example = "12017")
    @ExcelProperty(value = "客户id", converter = DictConvert.class)
    @DictFormat("customer") // TODO 代码优化：建议设置到对应的 DictTypeConstants 枚举类中
    private Long customerId;

    @Schema(description = "体验版次数", example = "16563")
    @ExcelProperty("体验版次数")
    private Integer trailCount;

    @Schema(description = "客户名称", example = "王五")
    @ExcelProperty("客户名称")
    private String customerName;

    @Schema(description = "兑换套餐名称", example = "王五")
    @ExcelProperty("兑换套餐名称")
    private String changeName;

    @Schema(description = "内容天数", example = "1")
    @ExcelProperty("内容天数")
    private Integer contentDays;

    @Schema(description = "体验版剩余次数", example = "20190")
    @ExcelProperty("体验版剩余次数")
    private Integer trailLeftCount;

    @Schema(description = "专业版次数", example = "21108")
    @ExcelProperty("专业版次数")
    private Integer psCount;

    @Schema(description = "专业版剩余次数", example = "3725")
    @ExcelProperty("专业版剩余次数")
    private Integer psLeftCount;

    @Schema(description = "内容查看次数", example = "14083")
    @ExcelProperty("内容查看次数")
    private Integer contentCount;

    @Schema(description = "内容查看剩余次数", example = "12870")
    @ExcelProperty("内容查看剩余次数")
    private Integer contentLeftCount;

    @Schema(description = "问答次数", example = "5529")
    @ExcelProperty("问答次数")
    private Integer askCount;

    @Schema(description = "问答剩余次数", example = "11029")
    @ExcelProperty("问答剩余次数")
    private Integer askLeftCount;

    @Schema(description = "体验版最新购买时间")
    @ExcelProperty("体验版最新购买时间")
    private LocalDateTime trailUpdateTime;

    @Schema(description = "专业版最新购买时间")
    @ExcelProperty("专业版最新购买时间")
    private LocalDateTime psUpdateTime;

    @Schema(description = "内容会员到期时间")
    @ExcelProperty("内容会员到期时间")
    private LocalDateTime contentEndTime;

    @Schema(description = "内容会员开始时间")
    @ExcelProperty("内容会员开始时间")
    private LocalDateTime contentStartTime;

    @Schema(description = "创建时间")
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}