package cn.iocoder.yudao.module.system.service.employmentdir;

import cn.iocoder.yudao.framework.common.exception.ErrorCode;
import cn.iocoder.yudao.module.system.controller.admin.employmentdir.vo.EmploymentDirPageReqVO;
import cn.iocoder.yudao.module.system.controller.admin.employmentdir.vo.EmploymentDirSaveReqVO;
import cn.iocoder.yudao.module.system.dal.dataobject.employmentdir.EmploymentDirDO;
import cn.iocoder.yudao.module.system.dal.mysql.employmentdir.EmploymentDirMapper;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;


import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;

/**
 * 就业方向 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class EmploymentDirServiceImpl implements EmploymentDirService {

    @Resource
    private EmploymentDirMapper employmentDirMapper;

    @Override
    public Long createEmploymentDir(EmploymentDirSaveReqVO createReqVO) {
        // 插入
        EmploymentDirDO employmentDir = BeanUtils.toBean(createReqVO, EmploymentDirDO.class);
        employmentDirMapper.insert(employmentDir);
        // 返回
        return employmentDir.getId();
    }

    @Override
    public void updateEmploymentDir(EmploymentDirSaveReqVO updateReqVO) {
        // 校验存在
        validateEmploymentDirExists(updateReqVO.getId());
        // 更新
        EmploymentDirDO updateObj = BeanUtils.toBean(updateReqVO, EmploymentDirDO.class);
        employmentDirMapper.updateById(updateObj);
    }

    @Override
    public void deleteEmploymentDir(Long id) {
        // 校验存在
        validateEmploymentDirExists(id);
        // 删除
        employmentDirMapper.deleteById(id);
    }

    private void validateEmploymentDirExists(Long id) {
        if (employmentDirMapper.selectById(id) == null) {
            throw exception(new ErrorCode(404,"就业方向不存在"));
        }
    }

    @Override
    public EmploymentDirDO getEmploymentDir(Long id) {
        return employmentDirMapper.selectById(id);
    }

    @Override
    public PageResult<EmploymentDirDO> getEmploymentDirPage(EmploymentDirPageReqVO pageReqVO) {
        return employmentDirMapper.selectPage(pageReqVO);
    }

}