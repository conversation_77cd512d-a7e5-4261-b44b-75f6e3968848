package cn.iocoder.yudao.module.system.dal.mysql.useranalysis;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.module.system.dal.dataobject.useranalysis.UserAnalysisDO;
import cn.iocoder.yudao.module.system.controller.admin.useranalysis.vo.UserAnalysisPageReqVO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 用户分析 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface UserAnalysisMapper extends BaseMapperX<UserAnalysisDO> {

    default PageResult<UserAnalysisDO> selectPage(UserAnalysisPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<UserAnalysisDO>()
                .eqIfPresent(UserAnalysisDO::getUserId, reqVO.getUserId())
                .eqIfPresent(UserAnalysisDO::getAnswerNo, reqVO.getAnswerNo())
                .eqIfPresent(UserAnalysisDO::getAnalysisContent, reqVO.getAnalysisContent())
                .betweenIfPresent(UserAnalysisDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(UserAnalysisDO::getId));
    }

}