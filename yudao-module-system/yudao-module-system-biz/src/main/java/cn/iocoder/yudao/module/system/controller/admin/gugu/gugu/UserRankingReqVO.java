package cn.iocoder.yudao.module.system.controller.admin.gugu.gugu;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 用户排名查询请求 VO
 */
@Schema(description = "管理后台 - 用户排名查询请求 VO")
@Data
public class UserRankingReqVO {

    @Schema(description = "年份", requiredMode = Schema.RequiredMode.REQUIRED, example = "2024")
    @NotBlank(message = "年份不能为空")
    private String year;

    @Schema(description = "省份名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "安徽")
    @NotBlank(message = "省份名称不能为空")
    private String provinceName;

    @Schema(description = "选科类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "历史类")
    @NotBlank(message = "选科类型不能为空")
    private String subjectSelection;

    @Schema(description = "用户分数", requiredMode = Schema.RequiredMode.REQUIRED, example = "673")
    @NotNull(message = "用户分数不能为空")
    private Integer score;
}
