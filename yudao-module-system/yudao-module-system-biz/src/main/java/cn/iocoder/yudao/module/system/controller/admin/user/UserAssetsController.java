package cn.iocoder.yudao.module.system.controller.admin.user;

import cn.iocoder.yudao.module.system.controller.admin.user.vo.user.UserAssetsPageReqVO;
import cn.iocoder.yudao.module.system.controller.admin.user.vo.user.UserAssetsRespVO;
import cn.iocoder.yudao.module.system.controller.admin.user.vo.user.UserAssetsSaveReqVO;
import cn.iocoder.yudao.module.system.dal.dataobject.user.UserAssetsDO;
import cn.iocoder.yudao.module.system.service.user.UserAssetsService;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import java.util.*;
import java.io.IOException;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

import cn.iocoder.yudao.framework.excel.core.util.ExcelUtils;

import cn.iocoder.yudao.framework.apilog.core.annotation.ApiAccessLog;

import javax.annotation.Resource;
import javax.annotation.security.PermitAll;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;

import static cn.iocoder.yudao.framework.apilog.core.enums.OperateTypeEnum.*;


@Tag(name = "管理后台 - 用户资源")
@RestController
@RequestMapping("/system/ai/user-assets")
@Validated
public class UserAssetsController {

    @Resource
    private UserAssetsService userAssetsService;

    @PostMapping("/create")
    @Operation(summary = "创建用户资源")
    @PreAuthorize("@ss.hasPermission('ai:user-assets:create')")
    public CommonResult<Integer> createUserAssets(@Valid @RequestBody UserAssetsSaveReqVO createReqVO) {
        return success(userAssetsService.createUserAssets(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新用户资源")
    @PreAuthorize("@ss.hasPermission('ai:user-assets:update')")
    public CommonResult<Boolean> updateUserAssets(@Valid @RequestBody UserAssetsSaveReqVO updateReqVO) {
        userAssetsService.updateUserAssets(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除用户资源")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('ai:user-assets:delete')")
    public CommonResult<Boolean> deleteUserAssets(@RequestParam("id") Integer id) {
        userAssetsService.deleteUserAssets(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得用户资源")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('ai:user-assets:query')")
    public CommonResult<UserAssetsRespVO> getUserAssets(@RequestParam("id") Integer id) {
        UserAssetsDO userAssets = userAssetsService.getUserAssets(id);
        return success(BeanUtils.toBean(userAssets, UserAssetsRespVO.class));
    }

    @GetMapping("/getByUserId")
    @Operation(summary = "获得用户资源")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PermitAll
    public CommonResult<UserAssetsRespVO> getUserAssets(@RequestParam("userId") Long userId) {
        UserAssetsDO userAssets = userAssetsService.getUserAssetsByUserId(userId);
        return success(BeanUtils.toBean(userAssets, UserAssetsRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得用户资源分页")
    @PreAuthorize("@ss.hasPermission('ai:user-assets:query')")
    public CommonResult<PageResult<UserAssetsRespVO>> getUserAssetsPage(@Valid UserAssetsPageReqVO pageReqVO) {
        PageResult<UserAssetsDO> pageResult = userAssetsService.getUserAssetsPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, UserAssetsRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出用户资源 Excel")
    @PreAuthorize("@ss.hasPermission('ai:user-assets:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportUserAssetsExcel(@Valid UserAssetsPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<UserAssetsDO> list = userAssetsService.getUserAssetsPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "用户资源.xls", "数据", UserAssetsRespVO.class,
                        BeanUtils.toBean(list, UserAssetsRespVO.class));
    }

}