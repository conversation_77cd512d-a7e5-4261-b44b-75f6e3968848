package cn.iocoder.yudao.module.system.controller.admin.gugu.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 高考一分一段数据响应 VO
 */
@Schema(description = "管理后台 - 高考一分一段数据响应 VO")
@Data
public class ScoreSegmentRespVO {

    @Schema(description = "主键ID", example = "1")
    private Long id;

    @Schema(description = "年份", example = "2024")
    private String year;

    @Schema(description = "省份名称", example = "北京")
    private String provinceName;

    @Schema(description = "科目选择类型", example = "物理类")
    private String subjectSelection;

    @Schema(description = "高考分数", example = "673")
    private String examinationScore;

    @Schema(description = "该分数考生人数", example = "25")
    private Integer candidateCount;

    @Schema(description = "累计考生人数", example = "100")
    private Integer totalCandidates;

    @Schema(description = "位次范围", example = "1-21")
    private String rankingRange;

    @Schema(description = "录取批次名称", example = "本科批")
    private String admissionBatchName;

    @Schema(description = "最低录取控制分数线", example = "462")
    private String minimumAdmissionScore;

    @Schema(description = "位次", example = "673")
    private String ranking;

    @Schema(description = "历史分数数据", example = "[{\"AcademicYear\": 2023, \"ExaminationScore\": \"687-750\", \"RankingRange\": \"1-116\"}]")
    private String historicalScores;
}
