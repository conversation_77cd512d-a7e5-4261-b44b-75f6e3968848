package cn.iocoder.yudao.module.system.controller.admin.answer.vo;

import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;

import java.util.List;

/**
 * @date 2025年03月31日 上午 10:51
 */
@Data
public class CommitQuestionDO {

    /**
     * 用户Id
     */
    private Integer userId;
    /**
     * 问题id
     */
    private Integer questionId;
    /**
     * 用户手写内容
     */
    private String writeContent;
    /**
     * 用户所选择答案Id集合，用逗号连接
     */
    private List<Integer> answerChoices;
    /**
     * 用户第多少次答题
     */
    private Integer answerNo;
}
