package cn.iocoder.yudao.module.system.controller.admin.gugu.gugu;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * 专业推荐响应 VO
 */
@Schema(description = "专业推荐响应结果")
@Data
public class MajorRecommendationRespVO {

    @Schema(description = "提取的用户信息")
    private UserProfileInfo userProfile;

    @Schema(description = "分数比用户高出3-5分的专业列表")
    private List<MajorAdmissionInfo> higherScoreMajors;

    @Schema(description = "分数与用户上下不超过3分的专业列表")
    private List<MajorAdmissionInfo> equalScoreMajors;

    @Schema(description = "分数比用户低5-10分的专业列表")
    private List<MajorAdmissionInfo> lowerScoreMajors;

    @Schema(description = "总数据量")
    private Integer totalCount;

    @Schema(description = "推荐理由")
    private String recommendationReason;
}
