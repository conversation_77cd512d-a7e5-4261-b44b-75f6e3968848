package cn.iocoder.yudao.module.system.dal.mysql.question;


import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.module.system.dal.dataobject.question.QuestionDO;
import org.apache.ibatis.annotations.Mapper;
import cn.iocoder.yudao.module.system.controller.admin.question.vo.*;

/**
 * 问题 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface QuestionMapper extends BaseMapperX<QuestionDO> {

    default PageResult<QuestionDO> selectPage(QuestionPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<QuestionDO>()
                .eqIfPresent(QuestionDO::getContent, reqVO.getContent())
                .eqIfPresent(QuestionDO::getType, reqVO.getType())
                .eqIfPresent(QuestionDO::getIsNecessary, reqVO.getIsNecessary())
                .eqIfPresent(QuestionDO::getStatus, reqVO.getStatus())
                .eqIfPresent(QuestionDO::getSort, reqVO.getSort())
                .betweenIfPresent(QuestionDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(QuestionDO::getId));
    }

}