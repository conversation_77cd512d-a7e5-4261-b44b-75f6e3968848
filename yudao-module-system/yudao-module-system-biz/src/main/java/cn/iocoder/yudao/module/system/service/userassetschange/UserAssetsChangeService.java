package cn.iocoder.yudao.module.system.service.userassetschange;

import java.util.*;
import javax.validation.*;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.system.controller.admin.userassetschange.vo.UserAssetsChangePageReqVO;
import cn.iocoder.yudao.module.system.controller.admin.userassetschange.vo.UserAssetsChangeSaveReqVO;
import cn.iocoder.yudao.module.system.dal.dataobject.userassetschange.UserAssetsChangeDO;

/**
 * 用户资源兑换套餐 Service 接口
 *
 * <AUTHOR>
 */
public interface UserAssetsChangeService {

    /**
     * 创建用户资源兑换套餐
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Integer createUserAssetsChange(@Valid UserAssetsChangeSaveReqVO createReqVO);

    /**
     * 更新用户资源兑换套餐
     *
     * @param updateReqVO 更新信息
     */
    void updateUserAssetsChange(@Valid UserAssetsChangeSaveReqVO updateReqVO);

    /**
     * 删除用户资源兑换套餐
     *
     * @param id 编号
     */
    void deleteUserAssetsChange(Integer id);

    /**
     * 获得用户资源兑换套餐
     *
     * @param id 编号
     * @return 用户资源兑换套餐
     */
    UserAssetsChangeDO getUserAssetsChange(Integer id);

    /**
     * 获得用户资源兑换套餐分页
     *
     * @param pageReqVO 分页查询
     * @return 用户资源兑换套餐分页
     */
    PageResult<UserAssetsChangeDO> getUserAssetsChangePage(UserAssetsChangePageReqVO pageReqVO);

}