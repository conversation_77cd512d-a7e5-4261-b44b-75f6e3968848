package cn.iocoder.yudao.module.system.dal.mysql.school;

import java.util.*;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.module.system.controller.admin.school.vo.SchoolPageReqVO;
import cn.iocoder.yudao.module.system.dal.dataobject.school.SchoolDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 院校 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface SchoolMapper extends BaseMapperX<SchoolDO> {

    default PageResult<SchoolDO> selectPage(SchoolPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<SchoolDO>()
                .likeIfPresent(SchoolDO::getName, reqVO.getName())
                .eqIfPresent(SchoolDO::getProvince, reqVO.getProvince())
                .eqIfPresent(SchoolDO::getProvinceName, reqVO.getProvinceName())
                .eqIfPresent(SchoolDO::getSort, reqVO.getSort())
                .betweenIfPresent(SchoolDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(SchoolDO::getId));
    }

}