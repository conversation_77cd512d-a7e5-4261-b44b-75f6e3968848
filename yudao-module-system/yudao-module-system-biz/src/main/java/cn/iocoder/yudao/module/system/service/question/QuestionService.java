package cn.iocoder.yudao.module.system.service.question;

import java.util.*;

import cn.iocoder.yudao.module.system.controller.admin.question.vo.*;
import cn.iocoder.yudao.module.system.dal.dataobject.question.QuestionChoiceDO;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.system.dal.dataobject.question.QuestionDO;
import cn.iocoder.yudao.module.system.dal.dataobject.question.QuestionVo;


import javax.validation.Valid;

/**
 * 问题 Service 接口
 *
 * <AUTHOR>
 */
public interface QuestionService {

    /**
     * 创建问题
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Integer createQuestion(@Valid QuestionSaveReqVO createReqVO);

    /**
     * 更新问题
     *
     * @param updateReqVO 更新信息
     */
    void updateQuestion(@Valid QuestionSaveReqVO updateReqVO);

    /**
     * 删除问题
     *
     * @param id 编号
     */
    void deleteQuestion(Integer id);



    /**
     * 获得问题分页
     *
     * @param pageReqVO 分页查询
     * @return 问题分页
     */
    PageResult<QuestionDO> getQuestionPage(QuestionPageReqVO pageReqVO);

    // ==================== 子表（问题选项） ====================

    /**
     * 获得问题选项列表
     *
     * @param questionId 问题id
     * @return 问题选项列表
     */
    List<QuestionChoiceDO> getChoiceListByQuestionId(Integer questionId);

    List<QuestionVo> loadQuestionByType(Integer type);

    QuestionVo answerQuestion(Integer questionId,Integer answerNo) ;

    QuestionDO getQuestion(Integer id);

    Integer getBiggestQuestionIdByType(Integer type);
}