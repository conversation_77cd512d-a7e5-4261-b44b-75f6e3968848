package cn.iocoder.yudao.module.system.dal.dataobject.gugu;

import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * 历年高考专业录取数据 DO
 */
@TableName("system_major_admission")
@KeySequence("system_major_admission_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MajorAdmissionDO extends BaseDO {

    /**
     * 主键ID
     */
    @TableId
    private Long id;

    /**
     * 招生省份
     */
    private String provinceName;

    /**
     * 高校唯一ID
     */
    private String schoolUuid;

    /**
     * 高校名称
     */
    private String schoolName;

    /**
     * 专业名称
     */
    private String majorName;

    /**
     * 专业代码
     */
    private Integer majorCode;

    /**
     * 录取年份
     */
    private Integer year;

    /**
     * 最高分
     */
    private String highScore;

    /**
     * 平均分
     */
    private String averageScore;

    /**
     * 最低分
     */
    private String lowestScore;

    /**
     * 最低位次
     */
    private String lowestSection;

    /**
     * 录取批次
     */
    private String batchName;

    /**
     * 专业类型
     */
    private String typeName;

    /**
     * 专业分数
     */
    private String proScore;

    /**
     * 选科要求
     */
    private String subjectSelection;

    /**
     * 专业标准代码
     */
    private String majorStandardCode;
}
