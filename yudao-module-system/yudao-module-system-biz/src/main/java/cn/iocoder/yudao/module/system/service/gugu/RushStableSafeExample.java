package cn.iocoder.yudao.module.system.service.gugu;

import cn.iocoder.yudao.module.system.controller.admin.gugu.gugu.MajorAdmissionInfo;
import cn.iocoder.yudao.module.system.controller.admin.gugu.gugu.UserProfileInfo;
import cn.iocoder.yudao.module.system.controller.admin.gugu.vo.StructuredUserDataReqVO;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 冲稳保分类功能示例
 * 展示如何使用新的冲稳保分类功能
 */
public class RushStableSafeExample {

    /**
     * 示例：展示冲稳保分类逻辑
     */
    public static void demonstrateRushStableSafeClassification() {
        System.out.println("=== 冲稳保分类功能示例 ===");
        
        // 模拟用户信息
        int userScore = 538;
        System.out.println("用户分数: " + userScore + "分");
        System.out.println();
        
        // 模拟专业数据
        List<MajorAdmissionInfo> majors = createSampleMajors();
        
        // 展示分类逻辑
        System.out.println("分类标准:");
        System.out.println("• 冲刺专业: 录取分数 > " + (userScore + 10) + "分");
        System.out.println("• 稳妥专业: 录取分数在 " + (userScore - 10) + "-" + (userScore + 10) + "分范围内");
        System.out.println("• 保底专业: 录取分数 < " + (userScore - 10) + "分");
        System.out.println();
        
        // 模拟分类结果
        Map<String, List<MajorAdmissionInfo>> classified = classifyMajors(majors, userScore);
        
        System.out.println("分类结果:");
        System.out.println("冲刺专业 (" + classified.get("rush").size() + "个):");
        for (MajorAdmissionInfo major : classified.get("rush")) {
            System.out.println("  • " + major.getSchoolName() + " - " + major.getMajorName() + " (" + major.getLowestScore() + "分)");
        }
        
        System.out.println("\n稳妥专业 (" + classified.get("stable").size() + "个):");
        for (MajorAdmissionInfo major : classified.get("stable")) {
            System.out.println("  • " + major.getSchoolName() + " - " + major.getMajorName() + " (" + major.getLowestScore() + "分)");
        }
        
        System.out.println("\n保底专业 (" + classified.get("safe").size() + "个):");
        for (MajorAdmissionInfo major : classified.get("safe")) {
            System.out.println("  • " + major.getSchoolName() + " - " + major.getMajorName() + " (" + major.getLowestScore() + "分)");
        }
    }

    /**
     * 创建示例专业数据
     */
    private static List<MajorAdmissionInfo> createSampleMajors() {
        List<MajorAdmissionInfo> majors = new ArrayList<>();
        
        // 冲刺专业（分数高于548）
        majors.add(createMajor("中国医科大学", "护理学", "550"));
        majors.add(createMajor("大连理工大学", "护理学", "555"));
        
        // 稳妥专业（分数在528-548之间）
        majors.add(createMajor("大连医科大学", "护理学", "535"));
        majors.add(createMajor("辽宁中医药大学", "护理学", "540"));
        majors.add(createMajor("沈阳药科大学", "护理学", "545"));
        
        // 保底专业（分数低于528）
        majors.add(createMajor("锦州医科大学", "护理学", "520"));
        majors.add(createMajor("沈阳医学院", "护理学", "525"));
        majors.add(createMajor("辽宁何氏医学院", "护理学", "515"));
        
        return majors;
    }

    /**
     * 创建专业信息
     */
    private static MajorAdmissionInfo createMajor(String schoolName, String majorName, String score) {
        MajorAdmissionInfo major = new MajorAdmissionInfo();
        major.setSchoolName(schoolName);
        major.setMajorName(majorName);
        major.setLowestScore(score);
        major.setProvinceName("辽宁");
        major.setYear(2024);
        major.setTypeName("历史类");
        return major;
    }

    /**
     * 模拟分类逻辑
     */
    private static Map<String, List<MajorAdmissionInfo>> classifyMajors(List<MajorAdmissionInfo> majors, int userScore) {
        Map<String, List<MajorAdmissionInfo>> result = new HashMap<>();
        List<MajorAdmissionInfo> rushMajors = new ArrayList<>();
        List<MajorAdmissionInfo> stableMajors = new ArrayList<>();
        List<MajorAdmissionInfo> safeMajors = new ArrayList<>();

        for (MajorAdmissionInfo major : majors) {
            int majorScore = Integer.parseInt(major.getLowestScore());
            int scoreDiff = majorScore - userScore;

            if (scoreDiff > 10) {
                rushMajors.add(major);
            } else if (scoreDiff >= -10) {
                stableMajors.add(major);
            } else {
                safeMajors.add(major);
            }
        }

        result.put("rush", rushMajors);
        result.put("stable", stableMajors);
        result.put("safe", safeMajors);
        return result;
    }

    /**
     * 示例：API响应结构
     */
    public static void demonstrateApiResponse() {
        System.out.println("\n=== API 响应结构示例 ===");
        System.out.println("POST /system/metadata/ceemajor/all-suitable");
        System.out.println();
        System.out.println("响应数据结构:");
        System.out.println("{");
        System.out.println("  \"code\": 0,");
        System.out.println("  \"data\": {");
        System.out.println("    \"userProfile\": { ... },");
        System.out.println("    \"rushMajors\": [ ... ],      // 冲刺专业列表");
        System.out.println("    \"stableMajors\": [ ... ],    // 稳妥专业列表");
        System.out.println("    \"safeMajors\": [ ... ],      // 保底专业列表");
        System.out.println("    \"rushCount\": 2,             // 冲刺专业数量");
        System.out.println("    \"stableCount\": 3,           // 稳妥专业数量");
        System.out.println("    \"safeCount\": 3,             // 保底专业数量");
        System.out.println("    \"totalCount\": 8,            // 专业总数");
        System.out.println("    \"queryInfo\": \"...\"         // 查询信息说明");
        System.out.println("  },");
        System.out.println("  \"msg\": \"操作成功\"");
        System.out.println("}");
    }

    /**
     * 示例：志愿填报建议
     */
    public static void demonstrateVolunteerAdvice() {
        System.out.println("\n=== 志愿填报建议 ===");
        System.out.println("基于冲稳保分类的志愿填报策略:");
        System.out.println();
        System.out.println("1. 冲刺专业 (20-30%):");
        System.out.println("   • 选择分数略高于自己的专业");
        System.out.println("   • 有一定挑战性，但仍有录取可能");
        System.out.println("   • 建议填报2-3个志愿");
        System.out.println();
        System.out.println("2. 稳妥专业 (40-50%):");
        System.out.println("   • 选择分数接近自己的专业");
        System.out.println("   • 录取概率较高，是主要选择");
        System.out.println("   • 建议填报4-5个志愿");
        System.out.println();
        System.out.println("3. 保底专业 (20-30%):");
        System.out.println("   • 选择分数低于自己的专业");
        System.out.println("   • 确保有学可上，避免滑档");
        System.out.println("   • 建议填报2-3个志愿");
        System.out.println();
        System.out.println("注意事项:");
        System.out.println("• 具体比例可根据个人风险偏好调整");
        System.out.println("• 建议结合专业兴趣和就业前景综合考虑");
        System.out.println("• 关注各专业的选科要求和其他限制条件");
    }

    public static void main(String[] args) {
        demonstrateRushStableSafeClassification();
        demonstrateApiResponse();
        demonstrateVolunteerAdvice();
    }
}
