package cn.iocoder.yudao.module.system.dal.mysql.normalquestioncontent;

import java.util.*;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.module.system.controller.admin.normalquestioncontent.vo.NormalQuestionContentPageReqVO;
import cn.iocoder.yudao.module.system.dal.dataobject.normalquestioncontent.NormalQuestionContentDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * AI 报告 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface NormalQuestionContentMapper extends BaseMapperX<NormalQuestionContentDO> {

    default PageResult<NormalQuestionContentDO> selectPage(NormalQuestionContentPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<NormalQuestionContentDO>()
                .eqIfPresent(NormalQuestionContentDO::getQuestionId, reqVO.getQuestionId())
                .eqIfPresent(NormalQuestionContentDO::getContent, reqVO.getContent())
                .eqIfPresent(NormalQuestionContentDO::getType, reqVO.getType())
                .eqIfPresent(NormalQuestionContentDO::getParentType, reqVO.getParentType())
                .eqIfPresent(NormalQuestionContentDO::getQuestion, reqVO.getQuestion())
                .eqIfPresent(NormalQuestionContentDO::getVersion, reqVO.getVersion())
                .betweenIfPresent(NormalQuestionContentDO::getCreateTime, reqVO.getCreateTime())
                .eqIfPresent(NormalQuestionContentDO::getStar, reqVO.getStar())
                .eqIfPresent(NormalQuestionContentDO::getDs, reqVO.getDs())
                .eqIfPresent(NormalQuestionContentDO::getDsCus, reqVO.getDsCus())
                .orderByDesc(NormalQuestionContentDO::getId));
    }

}