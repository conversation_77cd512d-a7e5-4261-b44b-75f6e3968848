package cn.iocoder.yudao.module.system.controller.admin.userassetscode.vo;

import lombok.*;
import java.util.*;
import io.swagger.v3.oas.annotations.media.Schema;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 用户资源兑换分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class UserAssetsCodePageReqVO extends PageParam {

    @Schema(description = "用户id", example = "11804")
    private Long userId;

    @Schema(description = "用户名称", example = "赵六")
    private String userName;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

    @Schema(description = "套餐id", example = "27009")
    private Integer changeId;

    @Schema(description = "套餐名称", example = "赵六")
    private String changeName;

    @Schema(description = "兑换码")
    private String code;

    @Schema(description = "客户名称", example = "赵六")
    private String customerName;

    @Schema(description = "状态", example = "2")
    private Integer status;

    @Schema(description = "过期时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] expireDate;

    @Schema(description = "客户id", example = "30352")
    private Integer customerId;

    @Schema(description = "使用日期")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] usedDate;

}