package cn.iocoder.yudao.module.system.controller.admin.useranalysis.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;


import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

@Schema(description = "管理后台 - 用户分析新增/修改 Request VO")
@Data
public class UserAnalysisSaveReqVO {

    @Schema(description = "主键id,即分析Id", requiredMode = Schema.RequiredMode.REQUIRED, example = "17306")
    private Integer id;

    @Schema(description = "用户Id", requiredMode = Schema.RequiredMode.REQUIRED, example = "8553")
    @NotNull(message = "用户Id不能为空")
    private Integer userId;

    @Schema(description = "答题次序号", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "答题次序号不能为空")
    private Integer answerNo;

    @Schema(description = "问题内容", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "问题内容不能为空")
    private String analysisContent;

}