package cn.iocoder.yudao.module.system.service.gugu;

import cn.iocoder.yudao.module.system.controller.admin.gugu.gugu.MajorAdmissionInfo;
import cn.iocoder.yudao.module.system.controller.admin.gugu.gugu.MajorAdmissionQueryReqVO;
import cn.iocoder.yudao.module.system.dal.dataobject.gugu.MajorAdmissionDO;

import java.util.List;
import java.util.Map;

/**
 * 历年高考专业录取数据 Service 接口
 */
public interface MajorAdmissionService {

    /**
     * 保存专业录取数据
     *
     * @param admissionInfoList 专业录取数据列表
     * @return 保存成功的数量
     */
    int saveMajorAdmissionList(List<MajorAdmissionInfo> admissionInfoList);

    /**
     * 查询历年高考专业录取数据
     *
     * @param reqVO 查询参数
     * @return 查询结果
     */
    Map<String, Object> getMajorAdmissionInfo(MajorAdmissionQueryReqVO reqVO);

    /**
     * 查询所有历年高考专业录取数据
     *
     * @param reqVO 查询参数
     * @return 查询结果
     */
    Map<String, Object> getMajorAdmissionInfoAll(MajorAdmissionQueryReqVO reqVO);

    /**
     * 根据高校ID、专业名称和年份查询专业录取数据
     *
     * @param schoolUUID 高校唯一ID
     * @param majorName 专业名称
     * @param year 年份
     * @return 专业录取数据列表
     */
    List<MajorAdmissionDO> getMajorAdmissionBySchoolMajorYearTypeNameprovinceName(String schoolUUID, String majorName, Integer year, String typeName, String provinceName);

    /**
     * 根据省份和年份查询专业录取数据
     *
     * @param provinceName 省份名称
     * @param year 年份
     * @return 专业录取数据列表
     */
    List<MajorAdmissionDO> getMajorAdmissionByProvinceAndYear(String provinceName, Integer year);

    /**
     * 根据高校名称查询专业录取数据
     *
     * @param schoolName 高校名称
     * @return 专业录取数据列表
     */
    List<MajorAdmissionDO> getMajorAdmissionBySchoolName(String schoolName);

    /**
     * 根据专业名称查询专业录取数据
     *
     * @param majorName 专业名称
     * @return 专业录取数据列表
     */
    List<MajorAdmissionDO> getMajorAdmissionByMajorName(String majorName);

    /**
     * 根据年份查询专业录取数据
     *
     * @param year 年份
     * @return 专业录取数据列表
     */
    List<MajorAdmissionDO> getMajorAdmissionByYear(Integer year);

    /**
     * 根据省份、年份和分数范围查询专业录取数据
     *
     * @param provinceName 省份名称
     * @param year 年份
     * @param minScore 最低分数
     * @param maxScore 最高分数
     * @return 专业录取数据列表
     */
    List<MajorAdmissionDO> getMajorAdmissionByProvinceYearAndScoreRange(String provinceName, Integer year, Integer minScore, Integer maxScore);

    /**
     * 根据省份、年份、分数范围和类型查询专业录取数据
     *
     * @param provinceName 省份名称
     * @param year 年份
     * @param minScore 最低分数
     * @param maxScore 最高分数
     * @param typeName 类型名称，如物理类、历史类等
     * @return 专业录取数据列表
     */
    List<MajorAdmissionDO> getMajorAdmissionByProvinceYearScoreRangeAndType(String provinceName, Integer year, Integer minScore, Integer maxScore, String typeName);

    /**
     * 根据省份、年份、分数范围和专业名称查询专业录取数据
     *
     * @param provinceName 省份名称
     * @param year 年份
     * @param minScore 最低分数
     * @param maxScore 最高分数
     * @param majorName 专业名称（模糊查询）
     * @return 专业录取数据列表
     */
    List<MajorAdmissionDO> getMajorAdmissionByProvinceYearScoreRangeAndMajorName(String provinceName, Integer year, Integer minScore, Integer maxScore, String majorName);

    /**
     * 根据省份、年份、分数范围和专业名称关键词查询专业录取数据
     * 将专业名称拆分为关键词，任何包含这些关键词的专业都会被匹配
     *
     * @param provinceName 省份名称
     * @param year 年份
     * @param minScore 最低分数
     * @param maxScore 最高分数
     * @param majorName 专业名称
     * @return 专业录取数据列表
     */
    List<MajorAdmissionDO> getMajorAdmissionByProvinceYearScoreRangeAndMajorKeywords(String provinceName, Integer year, Integer minScore, Integer maxScore, String majorName);
}
