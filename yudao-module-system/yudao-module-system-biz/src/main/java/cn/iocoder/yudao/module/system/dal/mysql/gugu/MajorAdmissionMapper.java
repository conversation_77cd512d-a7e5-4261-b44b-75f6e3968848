package cn.iocoder.yudao.module.system.dal.mysql.gugu;

import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.module.system.dal.dataobject.gugu.MajorAdmissionDO;
import cn.iocoder.yudao.module.system.util.ChineseSegmentationUtil;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.regex.Pattern;

/**
 * 历年高考专业录取数据 Mapper
 */
@Mapper
public interface MajorAdmissionMapper extends BaseMapperX<MajorAdmissionDO> {

    /**
     * 去除专业名称中括号及其内容的正则表达式
     */
    Pattern MAJOR_NAME_PATTERN = Pattern.compile("\\([^)]*\\)");

    /**
     * 去除专业名称中的括号及其内容
     *
     * @param majorName 原始专业名称
     * @return 处理后的专业名称
     */
    default String removeBrackets(String majorName) {
        if (StringUtils.hasText(majorName)) {
            return MAJOR_NAME_PATTERN.matcher(majorName).replaceAll("").trim();
        }
        return majorName;
    }

    /**
     * 比较两个分数字符串
     *
     * @param score1 分数字符串1
     * @param score2 分数字符串2
     * @return 如果 score1 > score2 返回正数，如果 score1 < score2 返回负数，如果相等返回0
     */
    default int compareScores(String score1, String score2) {
        if (score1 == null && score2 == null) {
            return 0;
        }
        if (score1 == null) {
            return -1;
        }
        if (score2 == null) {
            return 1;
        }

        try {
            // 尝试直接转换为整数进行比较
            int s1 = Integer.parseInt(score1);
            int s2 = Integer.parseInt(score2);
            return Integer.compare(s1, s2);
        } catch (NumberFormatException e) {
            // 如果转换失败，尝试处理分数区间的情况
            try {
                // 如果是区间，取区间的最小值进行比较
                int s1Value = extractMinScore(score1);
                int s2Value = extractMinScore(score2);
                return Integer.compare(s1Value, s2Value);
            } catch (Exception ex) {
                // 如果仍然无法比较，则按字符串比较
                return score1.compareTo(score2);
            }
        }
    }

    /**
     * 从分数字符串中提取最小分数
     *
     * @param score 分数字符串，可能是单一分数或区间（如"673-750"）
     * @return 最小分数值
     */
    default int extractMinScore(String score) {
        if (score.contains("-")) {
            // 如果是区间，取区间的最小值
            String[] parts = score.split("-");
            return Integer.parseInt(parts[0].trim());
        } else {
            // 如果是单一分数，直接转换
            return Integer.parseInt(score.trim());
        }
    }

    /**
     * 根据高校ID、专业名称和年份查询专业录取数据
     *
     * @param schoolUUID 高校唯一ID
     * @param majorName 专业名称
     * @param year 年份
     * @return 专业录取数据列表
     */
    default List<MajorAdmissionDO> selectBySchoolMajorYearTypeNameProvinceName(String schoolUUID, String majorName, Integer year, String typeName, String provinceName) {
        // 去除专业名称中的括号及其内容
        String cleanMajorName = removeBrackets(majorName);
        if (StringUtils.hasText(cleanMajorName)) {
            // 使用自定义SQL条件进行查询，忽略括号内容
            LambdaQueryWrapperX<MajorAdmissionDO> wrapper = new LambdaQueryWrapperX<MajorAdmissionDO>()
                    .eq(MajorAdmissionDO::getSchoolUuid, schoolUUID)
                    .eq(MajorAdmissionDO::getYear, year).eq(MajorAdmissionDO::getTypeName, typeName).eq(MajorAdmissionDO::getProvinceName, provinceName);
            wrapper.apply("SUBSTRING_INDEX(major_name, '(', 1) = {0}", cleanMajorName);
            return selectList(wrapper);
        } else {
            return selectList(new LambdaQueryWrapperX<MajorAdmissionDO>()
                    .eq(MajorAdmissionDO::getSchoolUuid, schoolUUID)
                    .eq(MajorAdmissionDO::getYear, year));
        }
    }

    /**
     * 根据省份和年份查询专业录取数据
     *
     * @param provinceName 省份名称
     * @param year 年份
     * @return 专业录取数据列表
     */
    default List<MajorAdmissionDO> selectByProvinceAndYear(String provinceName, Integer year) {
        return selectList(new LambdaQueryWrapperX<MajorAdmissionDO>()
                .eq(MajorAdmissionDO::getProvinceName, provinceName)
                .eq(MajorAdmissionDO::getYear, year));
    }

    /**
     * 根据高校名称查询专业录取数据
     *
     * @param schoolName 高校名称
     * @return 专业录取数据列表
     */
    default List<MajorAdmissionDO> selectBySchoolName(String schoolName) {
        return selectList(new LambdaQueryWrapperX<MajorAdmissionDO>()
                .eq(MajorAdmissionDO::getSchoolName, schoolName));
    }

    /**
     * 根据专业名称查询专业录取数据
     *
     * @param majorName 专业名称
     * @return 专业录取数据列表
     */
    default List<MajorAdmissionDO> selectByMajorName(String majorName) {
        // 去除专业名称中的括号及其内容
        String cleanMajorName = removeBrackets(majorName);
        if (StringUtils.hasText(cleanMajorName)) {
            // 使用自定义SQL条件进行查询，忽略括号内容
            LambdaQueryWrapperX<MajorAdmissionDO> wrapper = new LambdaQueryWrapperX<MajorAdmissionDO>();
            wrapper.apply("SUBSTRING_INDEX(major_name, '(', 1) = {0}", cleanMajorName);
            return selectList(wrapper);
        } else {
            return selectList();
        }
    }

    /**
     * 根据年份查询专业录取数据
     *
     * @param year 年份
     * @return 专业录取数据列表
     */
    default List<MajorAdmissionDO> selectByYear(Integer year) {
        return selectList(new LambdaQueryWrapperX<MajorAdmissionDO>()
                .eq(MajorAdmissionDO::getYear, year));
    }

    /**
     * 根据省份、年份和分数范围查询专业录取数据
     *
     * @param provinceName 省份名称
     * @param year 年份
     * @param minScore 最低分数
     * @param maxScore 最高分数
     * @return 专业录取数据列表
     */
    default List<MajorAdmissionDO> selectByProvinceYearAndScoreRange(String provinceName, Integer year, Integer minScore, Integer maxScore) {
        return selectList(new LambdaQueryWrapperX<MajorAdmissionDO>()
                .eq(MajorAdmissionDO::getProvinceName, provinceName)
                .eq(MajorAdmissionDO::getYear, year)
                .apply("CAST(lowest_score AS SIGNED) >= {0}", minScore)
                .apply("CAST(lowest_score AS SIGNED) <= {0}", maxScore));
    }

    /**
     * 根据省份、年份、分数范围和类型查询专业录取数据，并根据专业名称去重
     *
     * @param provinceName 省份名称
     * @param year 年份
     * @param minScore 最低分数
     * @param maxScore 最高分数
     * @param typeName 类型名称，如物理类、历史类等
     * @return 专业录取数据列表
     */
    default List<MajorAdmissionDO> selectByProvinceYearScoreRangeAndType(String provinceName, Integer year, Integer minScore, Integer maxScore, String typeName) {
        // 获取所有符合条件的数据
        List<MajorAdmissionDO> allResults = selectList(new LambdaQueryWrapperX<MajorAdmissionDO>()
                .eq(MajorAdmissionDO::getProvinceName, provinceName)
                .eq(MajorAdmissionDO::getYear, year)
                .eq(StringUtils.hasText(typeName), MajorAdmissionDO::getTypeName, typeName)
                .apply("CAST(lowest_score AS SIGNED) >= {0}", minScore)
                .apply("CAST(lowest_score AS SIGNED) <= {0}", maxScore)
                .last("ORDER BY CAST(lowest_score AS SIGNED) DESC"));

        // 如果没有结果，直接返回空列表
        if (allResults.isEmpty()) {
            return allResults;
        }

        // 使用 Java 代码进行去重
//        Map<String, MajorAdmissionDO> uniqueResults = new LinkedHashMap<>(); // 使用 LinkedHashMap 保持排序
//
//        for (MajorAdmissionDO result : allResults) {
//            // 去除专业名称中的括号及其内容
//            String majorName = result.getMajorName();
//            String cleanMajorName = removeBrackets(majorName);
//
//            // 如果这个专业还没有加入结果集，或者当前结果的最低分比已有结果高，则替换
//            MajorAdmissionDO existingResult = uniqueResults.get(cleanMajorName);
//            if (existingResult == null || compareScores(result.getLowestScore(), existingResult.getLowestScore()) > 0) {
//                uniqueResults.put(cleanMajorName, result);
//            }
//        }

        // 返回去重后的结果
        return new ArrayList<>(allResults);
    }

    /**
     * 根据省份、年份、分数范围和专业名称查询专业录取数据
     *
     * @param provinceName 省份名称
     * @param year 年份
     * @param minScore 最低分数
     * @param maxScore 最高分数
     * @param majorName 专业名称（模糊查询）
     * @return 专业录取数据列表
     */
    default List<MajorAdmissionDO> selectByProvinceYearScoreRangeAndMajorName(String provinceName, Integer year, Integer minScore, Integer maxScore, String majorName) {
        // 去除专业名称中的括号及其内容
        String cleanMajorName = removeBrackets(majorName);
        LambdaQueryWrapperX<MajorAdmissionDO> wrapper = new LambdaQueryWrapperX<MajorAdmissionDO>()
                .eq(MajorAdmissionDO::getProvinceName, provinceName)
                .eq(MajorAdmissionDO::getYear, year);

        // 添加分数范围条件
        wrapper.apply("CAST(lowest_score AS SIGNED) >= {0}", minScore);
        wrapper.apply("CAST(lowest_score AS SIGNED) <= {0}", maxScore);

        // 如果有专业名称，添加模糊查询条件
        if (StringUtils.hasText(cleanMajorName)) {
            wrapper.apply("SUBSTRING_INDEX(major_name, '(', 1) LIKE {0}", "%" + cleanMajorName + "%");
        }

        return selectList(wrapper);
    }

    /**
     * 根据省份、年份、分数范围和专业名称关键词列表查询专业录取数据
     * 将专业名称拆分为关键词，任何包含这些关键词的专业都会被匹配
     *
     * @param provinceName 省份名称
     * @param year 年份
     * @param minScore 最低分数
     * @param maxScore 最高分数
     * @param majorName 专业名称
     * @return 专业录取数据列表
     */
    default List<MajorAdmissionDO> selectByProvinceYearScoreRangeAndMajorKeywords(String provinceName, Integer year, Integer minScore, Integer maxScore, String majorName) {
        if (!StringUtils.hasText(majorName)) {
            return new ArrayList<>();
        }

        // 去除专业名称中的括号及其内容
        String cleanMajorName = removeBrackets(majorName);

        // 将专业名称拆分为关键词
        List<String> keywords = splitMajorNameToKeywords(cleanMajorName);
        if (keywords.isEmpty()) {
            return new ArrayList<>();
        }

        LambdaQueryWrapperX<MajorAdmissionDO> wrapper = new LambdaQueryWrapperX<MajorAdmissionDO>()
                .eq(MajorAdmissionDO::getProvinceName, provinceName)
                .eq(MajorAdmissionDO::getYear, year);

        // 添加分数范围条件
        wrapper.apply("CAST(lowest_score AS SIGNED) >= {0}", minScore);
        wrapper.apply("CAST(lowest_score AS SIGNED) <= {0}", maxScore);

        // 构建关键词OR条件
        wrapper.and(w -> {
            for (String keyword : keywords) {
                if (StringUtils.hasText(keyword)) {
                    w.or().apply("SUBSTRING_INDEX(major_name, '(', 1) LIKE {0}", "%" + keyword + "%");
                }
            }
        });

        return selectList(wrapper);
    }

    /**
     * 将专业名称拆分为关键词
     *
     * @param majorName 专业名称
     * @return 关键词列表
     */
    default List<String> splitMajorNameToKeywords(String majorName) {
        if (!StringUtils.hasText(majorName)) {
            return new ArrayList<>();
        }

        // 使用中文分词工具类进行分词
        return ChineseSegmentationUtil.segment(majorName);
    }
}
