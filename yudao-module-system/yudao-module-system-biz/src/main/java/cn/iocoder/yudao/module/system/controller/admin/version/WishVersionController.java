package cn.iocoder.yudao.module.system.controller.admin.version;

import cn.hutool.core.collection.CollectionUtil;
import cn.iocoder.yudao.module.system.controller.admin.question.vo.QuestionGroupIntroduceVo;
import cn.iocoder.yudao.module.system.controller.admin.version.vo.WishVersionPageReqVO;
import cn.iocoder.yudao.module.system.controller.admin.version.vo.WishVersionRespVO;
import cn.iocoder.yudao.module.system.controller.admin.version.vo.WishVersionSaveReqVO;
import cn.iocoder.yudao.module.system.dal.dataobject.oauth2.OAuth2AccessTokenDO;
import cn.iocoder.yudao.module.system.dal.dataobject.user.AdminUserDO;
import cn.iocoder.yudao.module.system.dal.dataobject.version.WishVersionDO;
import cn.iocoder.yudao.module.system.dal.mysql.oauth2.OAuth2AccessTokenMapper;
import cn.iocoder.yudao.module.system.dal.mysql.version.WishVersionMapper;
import cn.iocoder.yudao.module.system.service.user.AdminUserService;
import cn.iocoder.yudao.module.system.service.version.WishVersionService;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;


import java.util.*;
import java.io.IOException;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;

import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

import cn.iocoder.yudao.framework.excel.core.util.ExcelUtils;

import cn.iocoder.yudao.framework.apilog.core.annotation.ApiAccessLog;

import static cn.iocoder.yudao.framework.apilog.core.enums.OperateTypeEnum.*;
import static cn.iocoder.yudao.framework.web.core.util.WebFrameworkUtils.getLoginUserId;


import javax.annotation.Resource;
import javax.annotation.security.PermitAll;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;

@Tag(name = "管理后台 - 志愿咨询题库版本表	")
@RestController
@RequestMapping("/version/wish-version")
@Validated
public class WishVersionController {

    @Resource
    private WishVersionService wishVersionService;
    @Resource
    private AdminUserService userService;
    @Resource
    private WishVersionMapper wishVersionMapper;
    @Resource
    private OAuth2AccessTokenMapper oAuth2AccessTokenMapper;

    @PostMapping("/create")
    @Operation(summary = "创建志愿咨询题库版本表	")
    @PreAuthorize("@ss.hasPermission('version:wish-version:create')")
    public CommonResult<Integer> createWishVersion(@Valid @RequestBody WishVersionSaveReqVO createReqVO) {
        return success(wishVersionService.createWishVersion(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新志愿咨询题库版本表	")
    @PreAuthorize("@ss.hasPermission('version:wish-version:update')")
    public CommonResult<Boolean> updateWishVersion(@Valid @RequestBody WishVersionSaveReqVO updateReqVO) {
        wishVersionService.updateWishVersion(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除志愿咨询题库版本表	")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('version:wish-version:delete')")
    public CommonResult<Boolean> deleteWishVersion(@RequestParam("id") Integer id) {
        wishVersionService.deleteWishVersion(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得志愿咨询题库版本表	")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('version:wish-version:query')")
    public CommonResult<WishVersionRespVO> getWishVersion(@RequestParam("id") Integer id) {
        WishVersionDO wishVersion = wishVersionService.getWishVersion(id);
        return success(BeanUtils.toBean(wishVersion, WishVersionRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得志愿咨询题库版本表	分页")
    @PreAuthorize("@ss.hasPermission('version:wish-version:query')")
    public CommonResult<PageResult<WishVersionRespVO>> getWishVersionPage(@Valid WishVersionPageReqVO pageReqVO) {
        PageResult<WishVersionDO> pageResult = wishVersionService.getWishVersionPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, WishVersionRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出志愿咨询题库版本表	 Excel")
    @PreAuthorize("@ss.hasPermission('version:wish-version:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportWishVersionExcel(@Valid WishVersionPageReqVO pageReqVO,
                                       HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<WishVersionDO> list = wishVersionService.getWishVersionPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "志愿咨询题库版本表	.xls", "数据", WishVersionRespVO.class,
                BeanUtils.toBean(list, WishVersionRespVO.class));
    }

    @GetMapping("/getWishIntroduce")
    @PermitAll
    public CommonResult<List<QuestionGroupIntroduceVo>> getWishIntroduce(HttpServletRequest request) {
        Long userId = getLoginUserId();
        System.out.println("查询到的用户Id: " + userId);
        if(userId==null){
            return CommonResult.success(null);
        }
        AdminUserDO user = userService.getUser(userId);

        List<WishVersionDO> wishVersionDOS = wishVersionMapper.selectList();
        System.out.println("查询到的记录: "+wishVersionDOS);
        List<QuestionGroupIntroduceVo> questionGroupIntroduceVos = new ArrayList<>();

        if (CollectionUtil.isNotEmpty(wishVersionDOS)) {
            for (WishVersionDO wishVersionDO : wishVersionDOS) {
                QuestionGroupIntroduceVo vo = new QuestionGroupIntroduceVo();
                BeanUtils.copyProperties(wishVersionDO, vo);
                if (wishVersionDO.getId() == 1) {
                    vo.setTotalTimes(user.getTestTotalTimes());
                    vo.setLeftTimes(user.getTestLeftTimes());
                }
                if (wishVersionDO.getId() == 2) {
                    vo.setTotalTimes(user.getProTotalTimes());
                    vo.setLeftTimes(user.getProLeftTimes());
                }
                questionGroupIntroduceVos.add(vo);
            }
        }
        return success(questionGroupIntroduceVos);

    }
}