package cn.iocoder.yudao.module.system.controller.admin.gugu;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.module.system.controller.admin.gugu.gugu.ExaminationResult;
import cn.iocoder.yudao.module.system.controller.admin.gugu.gugu.UserRankingReqVO;
import cn.iocoder.yudao.module.system.controller.admin.gugu.gugu.UserRankingRespVO;
import cn.iocoder.yudao.module.system.util.gugu.GuGuDataUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Map;

/**
 * 考试排名查询控制器
 */
@Tag(name = "管理后台 - 考试排名查询")
@RestController
@RequestMapping("/system/examination/ranking")
@Validated
public class ExaminationRankingController {

    @PostMapping("/query")
    @Operation(summary = "查询用户排名信息(使用POST请求)")
    public CommonResult<UserRankingRespVO> queryUserRanking(@Valid @RequestBody UserRankingReqVO reqVO) {
        // 调用工具类获取排名信息
        Map<String, Object> rankingInfo = GuGuDataUtils.getUserRankingInfo(
                reqVO.getYear(),
                reqVO.getProvinceName(),
                reqVO.getSubjectSelection(),
                reqVO.getScore()
        );

        return processRankingResult(rankingInfo);
    }

    @GetMapping("/get")
    @Operation(summary = "查询用户排名信息(使用GET请求)")
    public CommonResult<UserRankingRespVO> getUserRanking(
            @RequestParam("year") @NotBlank(message = "年份不能为空") String year,
            @RequestParam("provinceName") @NotBlank(message = "省份名称不能为空") String provinceName,
            @RequestParam("subjectSelection") @NotBlank(message = "选科类型不能为空") String subjectSelection,
            @RequestParam("score") @NotNull(message = "用户分数不能为空") Integer score) {

        // 调用工具类获取排名信息
        Map<String, Object> rankingInfo = GuGuDataUtils.getUserRankingInfo(
                year, provinceName, subjectSelection, score);

        return processRankingResult(rankingInfo);
    }

    /**
     * 处理排名查询结果
     * @param rankingInfo 排名信息
     * @return 响应结果
     */
    private CommonResult<UserRankingRespVO> processRankingResult(Map<String, Object> rankingInfo) {
        // 判断是否查询成功
        Boolean success = (Boolean) rankingInfo.get("success");
        if (Boolean.TRUE.equals(success)) {
            // 构建响应VO
            UserRankingRespVO respVO = new UserRankingRespVO();
            respVO.setRanking((String) rankingInfo.get("ranking"));
            respVO.setRankingRange((String) rankingInfo.get("rankingRange"));
            respVO.setTotalCandidates((Integer) rankingInfo.get("totalCandidates"));
            respVO.setAdmissionBatchName((String) rankingInfo.get("admissionBatchName"));
            respVO.setMinimumAdmissionScore((String) rankingInfo.get("minimumAdmissionScore"));
            respVO.setHistoricalScores((java.util.List<ExaminationResult.HistoricalScore>) rankingInfo.get("historicalScores"));

            return CommonResult.success(respVO);
        } else {
            String message = (String) rankingInfo.get("message");
            return CommonResult.error(400, message);
        }
    }
}
