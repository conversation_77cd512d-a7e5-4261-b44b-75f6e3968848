package cn.iocoder.yudao.module.system.dal.mysql.userassetscode;

import java.util.*;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.module.system.controller.admin.userassetscode.vo.UserAssetsCodePageReqVO;
import cn.iocoder.yudao.module.system.dal.dataobject.userassetscode.UserAssetsCodeDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 用户资源兑换 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface UserAssetsCodeMapper extends BaseMapperX<UserAssetsCodeDO> {

    default PageResult<UserAssetsCodeDO> selectPage(UserAssetsCodePageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<UserAssetsCodeDO>()
                .eqIfPresent(UserAssetsCodeDO::getUserId, reqVO.getUserId())
                .betweenIfPresent(UserAssetsCodeDO::getCreateTime, reqVO.getCreateTime())
                .eqIfPresent(UserAssetsCodeDO::getChangeId, reqVO.getChangeId())
                .eqIfPresent(UserAssetsCodeDO::getCode, reqVO.getCode())
                .likeIfPresent(UserAssetsCodeDO::getCustomerName, reqVO.getCustomerName())
                .eqIfPresent(UserAssetsCodeDO::getStatus, reqVO.getStatus())
                .betweenIfPresent(UserAssetsCodeDO::getExpireDate, reqVO.getExpireDate())
                .eqIfPresent(UserAssetsCodeDO::getCustomerId, reqVO.getCustomerId())
                .betweenIfPresent(UserAssetsCodeDO::getUsedDate, reqVO.getUsedDate())
                .orderByDesc(UserAssetsCodeDO::getId));
    }

}