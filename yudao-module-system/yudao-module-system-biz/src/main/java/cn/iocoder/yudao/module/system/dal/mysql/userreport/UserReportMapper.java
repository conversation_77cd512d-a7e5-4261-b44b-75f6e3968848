package cn.iocoder.yudao.module.system.dal.mysql.userreport;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.module.system.controller.admin.ai.vo.UserReportPageReqVO;
import cn.iocoder.yudao.module.system.dal.dataobject.ai.UserReportDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * AI 报告 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface UserReportMapper extends BaseMapperX<UserReportDO> {

    default PageResult<UserReportDO> selectPage(UserReportPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<UserReportDO>()
                .eqIfPresent(UserReportDO::getUserId, reqVO.getUserId())
                .eqIfPresent(UserReportDO::getContent, reqVO.getContent())
                .eqIfPresent(UserReportDO::getAnalysisType, reqVO.getAnalysisType())
                .likeIfPresent(UserReportDO::getName, reqVO.getName())
                .orderByDesc(UserReportDO::getId));
    }

}
