package cn.iocoder.yudao.module.system.controller.admin.conversation.vo;

import lombok.*;
import io.swagger.v3.oas.annotations.media.Schema;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - AI 聊天对话分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class ChatConversationPageReqVO extends PageParam {

    @Schema(description = "用户编号", example = "25513")
    private Long userId;

    @Schema(description = "聊天角色", example = "21425")
    private Long roleId;

    @Schema(description = "对话标题")
    private String title;

    @Schema(description = "模型编号", example = "23853")
    private Long modelId;

    @Schema(description = "模型标识")
    private String model;

    @Schema(description = "是否置顶")
    private Boolean pinned;

    @Schema(description = "置顶时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] pinnedTime;

    @Schema(description = "角色设定")
    private String systemMessage;

    @Schema(description = "温度参数")
    private Double temperature;

    @Schema(description = "单条回复的最大 Token 数量")
    private Integer maxTokens;

    @Schema(description = "上下文的最大 Message 数量")
    private Integer maxContexts;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}