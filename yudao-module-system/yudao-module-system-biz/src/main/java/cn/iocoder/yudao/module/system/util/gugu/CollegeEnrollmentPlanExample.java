package cn.iocoder.yudao.module.system.util.gugu;

import cn.iocoder.yudao.module.system.controller.admin.gugu.gugu.CollegeEnrollmentPlanInfo;
import cn.iocoder.yudao.module.system.controller.admin.gugu.gugu.CollegeEnrollmentPlanQueryReqVO;

import java.util.List;
import java.util.Map;

/**
 * 历年高校招生计划查询示例
 */
public class CollegeEnrollmentPlanExample {

    /**
     * 示例：查询历年高校招生计划数据
     */
    public static void example() {
        // 创建查询请求参数
        CollegeEnrollmentPlanQueryReqVO reqVO = new CollegeEnrollmentPlanQueryReqVO();
        reqVO.setPageIndex(1);
        reqVO.setPageSize(10);
        reqVO.setCollegeMajorName("计算机科学与技术");
        reqVO.setYear(2024);
        reqVO.setSchoolName("北京大学");
        reqVO.setProvinceName("北京");
        reqVO.setClassOne("工学");
        reqVO.setClassTwo("计算机类");
        reqVO.setBatchName("本科一批");
        reqVO.setType("理科");

        try {
            // 调用查询方法
            Map<String, Object> result = GuGuDataUtils.getCollegeEnrollmentPlanInfo(reqVO);

            if (Boolean.TRUE.equals(result.get("success"))) {
                // 获取查询结果
                List<CollegeEnrollmentPlanInfo> enrollmentPlanList = 
                    (List<CollegeEnrollmentPlanInfo>) result.get("enrollmentPlanList");
                Integer totalCount = (Integer) result.get("totalCount");
                Integer totalPages = (Integer) result.get("totalPages");

                System.out.println("查询成功！");
                System.out.println("总数据量: " + totalCount);
                System.out.println("总页数: " + totalPages);
                System.out.println("当前页数据量: " + enrollmentPlanList.size());

                // 打印部分数据
                for (int i = 0; i < Math.min(3, enrollmentPlanList.size()); i++) {
                    CollegeEnrollmentPlanInfo info = enrollmentPlanList.get(i);
                    System.out.println("招生计划 " + (i + 1) + ":");
                    System.out.println("  高校名称: " + info.getSchoolName());
                    System.out.println("  专业名称: " + info.getCollegeMajorName());
                    System.out.println("  招生人数: " + info.getEnrollmentNumbers());
                    System.out.println("  招生年份: " + info.getYear());
                    System.out.println("  招生省份: " + info.getProvinceName());
                    System.out.println("  录取批次: " + info.getBatchName());
                    System.out.println("  文理类别: " + info.getType());
                    System.out.println("  选科要求: " + info.getCourseSelectionRequirements());
                    System.out.println();
                }
            } else {
                System.out.println("查询失败: " + result.get("message"));
            }
        } catch (Exception e) {
            System.out.println("查询异常: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 示例：验证API Key
     */
    public static void validateApiKeyExample() {
        String testApiKey = "8N9BFH4DPAXWL4JJB5WAJYBJ3QCL77FM";
        boolean isValid = GuGuDataUtils.validateCollegeEnrollmentPlanApiKey(testApiKey);
        System.out.println("API Key 验证结果: " + (isValid ? "有效" : "无效"));
    }

    public static void main(String[] args) {
        System.out.println("=== 历年高校招生计划查询示例 ===");
        
        // 验证API Key
        validateApiKeyExample();
        
        // 查询示例
        example();
    }
}
