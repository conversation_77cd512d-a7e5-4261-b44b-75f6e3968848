package cn.iocoder.yudao.module.system.framework.cache;

import org.springframework.boot.autoconfigure.cache.CacheProperties;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.cache.RedisCacheConfiguration;
import org.springframework.data.redis.serializer.RedisSerializationContext;
import org.springframework.data.redis.serializer.RedisSerializer;

import java.time.Duration;

import static cn.iocoder.yudao.framework.redis.config.YudaoRedisAutoConfiguration.buildRedisSerializer;

/**
 * 专业数据缓存配置类
 */
@Configuration
@EnableCaching
public class CeeMajorCacheConfig {

    /**
     * 专业数据缓存的过期时间：1天
     * 由于专业数据不经常变化，所以可以设置较长的过期时间
     */
    private static final Duration CACHE_TTL = Duration.ofDays(1);

    /**
     * 专业数据缓存的Key前缀
     */
    public static final String CACHE_NAME_PREFIX = "ceeMajor";

    /**
     * 根据专业名称和教育级别查询专业信息的缓存名称
     */
    public static final String CACHE_NAME_BY_NAME_AND_LEVEL = CACHE_NAME_PREFIX + ":byNameAndLevel";

    /**
     * 根据专业名称模糊搜索专业的缓存名称
     */
    public static final String CACHE_NAME_SEARCH_BY_NAME = CACHE_NAME_PREFIX + ":searchByName";

    /**
     * 学科门类专业数量统计的缓存名称
     */
    public static final String CACHE_NAME_CATEGORY_COUNTS = CACHE_NAME_PREFIX + ":categoryCounts";

    /**
     * 配置专业数据的缓存配置
     */
    @Bean
    public RedisCacheConfiguration ceeMajorCacheConfiguration(CacheProperties cacheProperties) {
        // 使用默认配置
        RedisCacheConfiguration config = RedisCacheConfiguration.defaultCacheConfig();
        // 设置使用 JSON 序列化方式
        config = config.serializeValuesWith(
                RedisSerializationContext.SerializationPair.fromSerializer(buildRedisSerializer()));
        // 设置过期时间
        config = config.entryTtl(CACHE_TTL);
        // 不缓存空值
        config = config.disableCachingNullValues();
        return config;
    }
}
