package cn.iocoder.yudao.module.system.controller.admin.question.vo;

import lombok.*;
import java.util.*;
import io.swagger.v3.oas.annotations.media.Schema;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 问题分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class QuestionPageReqVO extends PageParam {

    @Schema(description = "问题内容")
    private String content;

    @Schema(description = "问题类型 1单选题 2多选题 3问答题", example = "1")
    private Integer type;

    @Schema(description = "是否必答题 1必答 0 可不回答")
    private Integer isNecessary;

    @Schema(description = "状态 1可用 0 不可用", example = "1")
    private Integer status;

    @Schema(description = "问题顺序")
    private Integer sort;

    @Schema(description = "问题创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}