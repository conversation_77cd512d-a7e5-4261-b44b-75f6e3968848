package cn.iocoder.yudao.module.system.service.user;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.util.StrUtil;
import cn.iocoder.yudao.framework.common.enums.CommonStatusEnum;
import cn.iocoder.yudao.framework.common.exception.ErrorCode;
import cn.iocoder.yudao.framework.common.exception.ServiceException;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.collection.CollectionUtils;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.framework.common.util.validation.ValidationUtils;
import cn.iocoder.yudao.framework.datapermission.core.util.DataPermissionUtils;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.module.infra.api.config.ConfigApi;
import cn.iocoder.yudao.module.infra.api.file.FileApi;
import cn.iocoder.yudao.module.system.controller.admin.auth.vo.AuthRegisterReqVO;
import cn.iocoder.yudao.module.system.controller.admin.gugu.gugu.CollegeEnrollmentPlanInfo;
import cn.iocoder.yudao.module.system.controller.admin.gugu.gugu.MajorAdmissionInfo;
import cn.iocoder.yudao.module.system.controller.admin.gugu.gugu.MajorAdmissionQueryReqVO;
import cn.iocoder.yudao.module.system.controller.admin.gugu.vo.CeeMajorTreeVO;
import cn.iocoder.yudao.module.system.dal.dataobject.gugu.CeeMajorDO;
import cn.iocoder.yudao.module.system.dal.dataobject.gugu.MajorAdmissionDO;
import cn.iocoder.yudao.module.system.dal.dataobject.gugu.ScoreSegmentDO;
import cn.iocoder.yudao.module.system.service.gugu.CeeMajorService;
import cn.iocoder.yudao.module.system.service.gugu.CollegeEnrollmentPlanService;

import java.util.stream.Collectors;

import cn.iocoder.yudao.module.system.controller.admin.user.vo.profile.UserProfileUpdatePasswordReqVO;
import cn.iocoder.yudao.module.system.controller.admin.user.vo.profile.UserProfileUpdateReqVO;
import cn.iocoder.yudao.module.system.controller.admin.user.vo.user.UserImportExcelVO;
import cn.iocoder.yudao.module.system.controller.admin.user.vo.user.UserImportRespVO;
import cn.iocoder.yudao.module.system.controller.admin.user.vo.user.UserPageReqVO;
import cn.iocoder.yudao.module.system.controller.admin.user.vo.user.UserSaveReqVO;
import cn.iocoder.yudao.module.system.dal.dataobject.answerRecord.AnswerRecordDO;
import cn.iocoder.yudao.module.system.dal.dataobject.dept.DeptDO;
import cn.iocoder.yudao.module.system.dal.dataobject.dept.UserPostDO;
import cn.iocoder.yudao.module.system.dal.dataobject.user.AdminUserDO;
import cn.iocoder.yudao.module.system.dal.mysql.answerRecord.AnswerRecordMapper;
import cn.iocoder.yudao.module.system.dal.mysql.dept.UserPostMapper;
import cn.iocoder.yudao.module.system.dal.mysql.user.AdminUserMapper;
import cn.iocoder.yudao.module.system.service.dept.DeptService;
import cn.iocoder.yudao.module.system.service.dept.PostService;
import cn.iocoder.yudao.module.system.service.gugu.MajorAdmissionService;
import cn.iocoder.yudao.module.system.service.gugu.ScoreSegmentService;
import cn.iocoder.yudao.module.system.service.permission.PermissionService;
import cn.iocoder.yudao.module.system.service.tenant.TenantService;
import cn.iocoder.yudao.module.system.util.CollectionUtils8;
import cn.iocoder.yudao.module.system.util.PerformanceMonitor;
import cn.iocoder.yudao.module.system.util.SubjectSelectionUtils;
import cn.iocoder.yudao.module.system.util.baidu.BaiduAiUtils;
import cn.iocoder.yudao.module.system.util.gugu.GuGuDataUtils;
import com.baidubce.appbuilder.base.exception.AppBuilderServerException;
import com.baidubce.appbuilder.model.appbuilderclient.AppBuilderClientIterator;
import com.baidubce.appbuilder.model.appbuilderclient.AppBuilderClientResult;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.google.common.annotations.VisibleForTesting;
import com.mzt.logapi.context.LogRecordContext;
import com.mzt.logapi.service.impl.DiffParseFunction;
import com.mzt.logapi.starter.annotation.LogRecord;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.PreDestroy;

import javax.annotation.Resource;
import javax.validation.ConstraintViolationException;
import java.io.IOException;
import java.io.InputStream;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.framework.common.util.collection.CollectionUtils.*;
import static cn.iocoder.yudao.module.system.enums.ErrorCodeConstants.*;
import static cn.iocoder.yudao.module.system.enums.LogRecordConstants.*;
import static cn.iocoder.yudao.module.system.util.baidu.BaiduAiUtils.APP_BUILDER_TOKEN;

/**
 * 后台用户 Service 实现类
 *
 * <AUTHOR>
 */
@Service("adminUserService")
@Slf4j
public class AdminUserServiceImpl implements AdminUserService {

    /**
     * 专业信息类，用于构建树形结构
     */
    @Data
    private static class MajorInfo {
        private Long id;
        private String name;
        private String code;
        private String educationLevel; // 教育级别
        private Boolean isRecommended; // 是否推荐专业
        private String careerDirection; // 就业方向
        private String majorIntroduction; // 专业介绍
        private String graduateScale; // 毕业规模
        private String maleFemaleRatio; // 男女比例
        private List<String> recommendSchools; // 推荐院校
        private List<CeeMajorDO.Course> courses; // 开设课程
    }

    static final String USER_INIT_PASSWORD_KEY = "system.user.init-password";

    /**
     * 用于异步获取排名信息的线程池
     */
    private static final ExecutorService RANKING_EXECUTOR = Executors.newCachedThreadPool(r -> {
        Thread thread = new Thread(r, "ranking-thread");
        thread.setDaemon(true); // 设置为守护线程，不阻止JVM退出
        return thread;
    });

    /**
     * 用户ID到排名查询任务的映射，用于取消正在进行的任务
     */
    private static final ConcurrentHashMap<Long, Future<?>> RANKING_TASKS = new ConcurrentHashMap<>();

    /**
     * 用于异步获取专业推荐的线程池
     */
    private static final ThreadPoolExecutor MAJOR_RECOMMENDATION_EXECUTOR;

    /**
     * 用户ID到专业推荐任务的映射，用于取消正在进行的任务
     */
    private static final ConcurrentHashMap<Long, Future<?>> MAJOR_RECOMMENDATION_TASKS = new ConcurrentHashMap<>();

    static {
        // 获取CPU核心数
        int cpuCores = Runtime.getRuntime().availableProcessors();

        // 配置专业推荐线程池
        int corePoolSize = Math.max(2, cpuCores / 2);
        int maximumPoolSize = Math.max(4, cpuCores);

        MAJOR_RECOMMENDATION_EXECUTOR = new ThreadPoolExecutor(
                corePoolSize,
                maximumPoolSize,
                60L,
                TimeUnit.SECONDS,
                new LinkedBlockingQueue<>(100),
                r -> {
                    Thread t = new Thread(r, "major-recommendation-" + r.hashCode());
                    t.setDaemon(true);
                    t.setUncaughtExceptionHandler((thread, ex) -> {
                        log.error("专业推荐线程[{}]发生未捕获异常: {}", thread.getName(), ex.getMessage(), ex);
                    });
                    return t;
                },
                new ThreadPoolExecutor.CallerRunsPolicy()
        );

        log.info("初始化专业推荐线程池完成: 核心线程数[{}], 最大线程数[{}]", corePoolSize, maximumPoolSize);
    }

    @Resource
    private AdminUserMapper userMapper;

    @Resource
    private DeptService deptService;
    @Resource
    private PostService postService;
    @Resource
    private PermissionService permissionService;
    @Resource
    private PasswordEncoder passwordEncoder;
    @Resource
    @Lazy // 延迟，避免循环依赖报错
    private TenantService tenantService;

    @Resource
    private UserPostMapper userPostMapper;

    @Resource
    private FileApi fileApi;
    @Resource
    private ConfigApi configApi;

    @Resource
    private CeeMajorService ceeMajorService;

    @Resource
    private MajorAdmissionService majorAdmissionService;

    @Resource
    private ScoreSegmentService scoreSegmentService;

    @Resource
    private AnswerRecordMapper answerRecordMapper;

    @Resource
    private CollegeEnrollmentPlanService collegeEnrollmentPlanService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(type = SYSTEM_USER_TYPE, subType = SYSTEM_USER_CREATE_SUB_TYPE, bizNo = "{{#user.id}}",
            success = SYSTEM_USER_CREATE_SUCCESS)
    public Long createUser(UserSaveReqVO createReqVO) {
        // 1.1 校验账户配合
        tenantService.handleTenantInfo(tenant -> {
            long count = userMapper.selectCount();
            if (count >= tenant.getAccountCount()) {
                throw exception(USER_COUNT_MAX, tenant.getAccountCount());
            }
        });
        // 1.2 校验正确性
        validateUserForCreateOrUpdate(null, createReqVO.getUsername(),
                createReqVO.getMobile(), createReqVO.getEmail(), createReqVO.getDeptId(), createReqVO.getPostIds());
        // 2.1 插入用户
        AdminUserDO user = BeanUtils.toBean(createReqVO, AdminUserDO.class);
        user.setStatus(CommonStatusEnum.ENABLE.getStatus()); // 默认开启
        user.setPassword(encodePassword(createReqVO.getPassword())); // 加密密码
        userMapper.insert(user);
        // 2.2 插入关联岗位
        if (CollectionUtil.isNotEmpty(user.getPostIds())) {
            userPostMapper.insertBatch(convertList(user.getPostIds(),
                    postId -> new UserPostDO().setUserId(user.getId()).setPostId(postId)));
        }

        // 3. 记录操作日志上下文
        LogRecordContext.putVariable("user", user);
        return user.getId();
    }

    @Override
    public Long registerUser(AuthRegisterReqVO registerReqVO) {
        // 1.1 校验账户配合
        tenantService.handleTenantInfo(tenant -> {
            long count = userMapper.selectCount();
            if (count >= tenant.getAccountCount()) {
                throw exception(USER_COUNT_MAX, tenant.getAccountCount());
            }
        });
        // 1.2 校验正确性
        validateUserForCreateOrUpdate(null, registerReqVO.getUsername(), null, null, null, null);

        // 2. 插入用户
        AdminUserDO user = BeanUtils.toBean(registerReqVO, AdminUserDO.class);
        user.setStatus(CommonStatusEnum.ENABLE.getStatus()); // 默认开启
        user.setPassword(encodePassword(registerReqVO.getPassword())); // 加密密码
        userMapper.insert(user);
        return user.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(type = SYSTEM_USER_TYPE, subType = SYSTEM_USER_UPDATE_SUB_TYPE, bizNo = "{{#updateReqVO.id}}",
            success = SYSTEM_USER_UPDATE_SUCCESS)
    public void updateUser(UserSaveReqVO updateReqVO) {
        updateReqVO.setPassword(null); // 特殊：此处不更新密码
        // 1. 校验正确性
        AdminUserDO oldUser = validateUserForCreateOrUpdate(updateReqVO.getId(), updateReqVO.getUsername(),
                updateReqVO.getMobile(), updateReqVO.getEmail(), updateReqVO.getDeptId(), updateReqVO.getPostIds());

        // 检查分数是否发生变化
        boolean scoreChanged = false;
        if (updateReqVO.getScore() != null && oldUser.getScore() != null) {
            scoreChanged = !updateReqVO.getScore().equals(oldUser.getScore());
        } else if (updateReqVO.getScore() != null || oldUser.getScore() != null) {
            // 一个为null，一个不为null，也视为变化
            scoreChanged = true;
        }

        // 如果分数变化，清空推荐专业
        if (scoreChanged) {
            log.info("User ID {} score changed from {} to {}, clearing recommended majors",
                    updateReqVO.getId(), oldUser.getScore(), updateReqVO.getScore());
            updateReqVO.setRecommendedMajors("");
        }

        // 检查选科是否发生变化
        boolean subjectChanged = false;
        if ((updateReqVO.getFirstSubject() != null && !updateReqVO.getFirstSubject().equals(oldUser.getFirstSubject())) ||
            (updateReqVO.getSecondSubject() != null && !updateReqVO.getSecondSubject().equals(oldUser.getSecondSubject()))) {
            subjectChanged = true;
        }

        //当还有正在生成中的报告时，不允许修改选科及分数
        if ((scoreChanged || subjectChanged) && hasGeneratingReport(updateReqVO.getId())) {
            throw exception(new ErrorCode(400, "当前有正在答题中的报告，请继续完成答题再修改分数和选科"));
        }

        // 2.1 更新用户
        AdminUserDO updateObj = BeanUtils.toBean(updateReqVO, AdminUserDO.class);
        userMapper.updateById(updateObj);

        // 2.2 更新岗位
        updateUserPost(updateReqVO, updateObj);

        // 2.3 异步获取适合的专业并存储
        if (scoreChanged) {
            //异步根据分数获取当前位次区间赋值和位次  位次区间新增字段ranking_range 并赋值，位次使用rank 并将rank改为ranking 进行赋值
            // 同步获取排名信息
            getUserRanking(updateObj);

            if (updateObj.getProvince() != null && updateObj.getScore() != null) {
                // 使用优化后的异步专业推荐方法
                asyncGetMajorRecommendations(updateObj);
            }
        }

        // 3. 记录操作日志上下文
        LogRecordContext.putVariable(DiffParseFunction.OLD_OBJECT, BeanUtils.toBean(oldUser, UserSaveReqVO.class));
        LogRecordContext.putVariable("user", oldUser);
    }

    /**
     * 优化后的异步获取专业推荐方法
     */
    private void asyncGetMajorRecommendations(AdminUserDO user) {
        final Long userId = user.getId();
        final String province = user.getProvince();
        final Double userScore = user.getScore();
        final String secondSubject = user.getSecondSubject();

        // 取消之前的任务（如果存在）
        Future<?> previousTask = MAJOR_RECOMMENDATION_TASKS.get(userId);
        if (previousTask != null && !previousTask.isDone()) {
            log.info("取消用户ID {}的上一个专业推荐任务", userId);
            previousTask.cancel(true);
        }

        // 创建新的任务
        Future<?> task = MAJOR_RECOMMENDATION_EXECUTOR.submit(() -> {
            try {
                // 检查任务是否被取消
                if (Thread.currentThread().isInterrupted()) {
                    log.info("用户ID {}的专业推荐任务在开始前被取消", userId);
                    return;
                }

                long startTime = System.currentTimeMillis();
                log.info("开始为用户ID {}异步获取专业推荐", userId);

                // 获取用户的选科信息
                final Set<String> userSubjects = parseUserSubjects(secondSubject);
                log.info("用户ID {}的选科信息: {}", userId, userSubjects);

                // 设置分数范围
                int score = userScore.intValue();
                int minScore = score - 50;
                int maxScore = score + 10;

                // 获取用户的选科类型
                String typeName = user.getTypeName();
                if (typeName == null || typeName.isEmpty()) {
                    typeName = determineSubjectSelection(province, secondSubject);
                    log.info("用户ID {}未设置选科类型，根据省份{}和选科{}确定为{}", userId, province, secondSubject, typeName);

                    // 更新用户的选科类型字段
                    AdminUserDO typeUpdateObj = new AdminUserDO();
                    typeUpdateObj.setId(userId);
                    typeUpdateObj.setTypeName(typeName);
                    userMapper.updateById(typeUpdateObj);
                }

                // 检查任务是否被取消
                if (Thread.currentThread().isInterrupted()) {
                    log.info("用户ID {}的专业推荐任务在数据库查询前被取消", userId);
                    return;
                }

                // 一次性查询所有符合条件的专业录取数据
                List<MajorAdmissionDO> admissionDOList = majorAdmissionService.getMajorAdmissionByProvinceYearScoreRangeAndType(
                        province, 2024, minScore, maxScore, typeName);

                log.info("为用户ID {}查询到{}条专业录取记录", userId, admissionDOList.size());

                if (admissionDOList.isEmpty()) {
                    log.warn("用户ID {}未找到符合条件的专业", userId);
                    return;
                }

                // 转换为Info对象
                List<MajorAdmissionInfo> admissionList = convertToMajorAdmissionInfoList(admissionDOList);

                // 检查任务是否被取消
                if (Thread.currentThread().isInterrupted()) {
                    log.info("用户ID {}的专业推荐任务在数据处理前被取消", userId);
                    return;
                }

                // 优化1：批量过滤选科要求
                List<MajorAdmissionInfo> filteredBySubjects = batchFilterBySubjects(admissionList, userSubjects);
                log.info("选科过滤后，用户ID {}剩余{}个专业", userId, filteredBySubjects.size());

                if (filteredBySubjects.isEmpty()) {
                    log.warn("用户ID {}经过选科过滤后无符合条件的专业", userId);
                    return;
                }

                // 检查任务是否被取消
                if (Thread.currentThread().isInterrupted()) {
                    log.info("用户ID {}的专业推荐任务在专业查询前被取消", userId);
                    return;
                }

                // 优化2：批量查询专业信息
                Map<String, CeeMajorDO> majorCache = batchQueryMajorInfo(filteredBySubjects);
                log.info("为用户ID {}批量查询到{}个专业信息", userId, majorCache.size());

                // 检查任务是否被取消
                if (Thread.currentThread().isInterrupted()) {
                    log.info("用户ID {}的专业推荐任务在招生计划过滤前被取消", userId);
                    return;
                }

                // 优化3：批量过滤招生计划数据
//                List<MajorAdmissionInfo> majorsWithEnrollmentPlan = batchFilterEnrollmentPlans(filteredBySubjects, province, typeName);
//                log.info("招生计划过滤后，用户ID {}剩余{}个专业", userId, majorsWithEnrollmentPlan.size());
//
//                if (majorsWithEnrollmentPlan.isEmpty()) {
//                    log.warn("用户ID {}经过招生计划过滤后无符合条件的专业", userId);
//                    return;
//                }

                // 检查任务是否被取消
                if (Thread.currentThread().isInterrupted()) {
                    log.info("用户ID {}的专业推荐任务在构建树形结构前被取消", userId);
                    return;
                }

                // 优化4：构建专业树形结构
                Map<String, Map<String, List<MajorInfo>>> groupedMajors = buildMajorTree(filteredBySubjects, majorCache);
                log.info("为用户ID {}构建了{}个学科门类的专业树", userId, groupedMajors.size());

                if (groupedMajors.isEmpty()) {
                    log.warn("用户ID {}未找到匹配的专业信息", userId);
                    return;
                }

                // 检查任务是否被取消
                if (Thread.currentThread().isInterrupted()) {
                    log.info("用户ID {}的专业推荐任务在保存结果前被取消", userId);
                    return;
                }

                // 优化5：保存推荐结果
                saveRecommendationResult(userId, groupedMajors);

                long endTime = System.currentTimeMillis();
                log.info("用户ID {}的专业推荐任务完成，耗时{}ms", userId, endTime - startTime);

            } catch (Exception e) {
                log.error("用户ID {}的专业推荐任务执行失败", userId, e);
            } finally {
                // 清理任务映射
                MAJOR_RECOMMENDATION_TASKS.remove(userId);
            }
        });

        // 保存任务映射
        MAJOR_RECOMMENDATION_TASKS.put(userId, task);
        log.info("为用户ID {}启动异步专业推荐任务", userId);
    }

    private void updateUserPost(UserSaveReqVO reqVO, AdminUserDO updateObj) {
        Long userId = reqVO.getId();
        Set<Long> dbPostIds = convertSet(userPostMapper.selectListByUserId(userId), UserPostDO::getPostId);
        // 计算新增和删除的岗位编号
        Set<Long> postIds = CollUtil.emptyIfNull(updateObj.getPostIds());
        Collection<Long> createPostIds = CollUtil.subtract(postIds, dbPostIds);
        Collection<Long> deletePostIds = CollUtil.subtract(dbPostIds, postIds);
        // 执行新增和删除。对于已经授权的岗位，不用做任何处理
        if (!CollectionUtil.isEmpty(createPostIds)) {
            userPostMapper.insertBatch(convertList(createPostIds,
                    postId -> new UserPostDO().setUserId(userId).setPostId(postId)));
        }
        if (!CollectionUtil.isEmpty(deletePostIds)) {
            userPostMapper.deleteByUserIdAndPostId(userId, deletePostIds);
        }
    }

    @Override
    public void updateUserLogin(Long id, String loginIp) {
        userMapper.updateById(new AdminUserDO().setId(id).setLoginIp(loginIp).setLoginDate(LocalDateTime.now()));
    }

    @Override
    public void updateUserProfile(Long id, UserProfileUpdateReqVO reqVO) {
        // 校验正确性
        validateUserExists(id);
        validateEmailUnique(id, reqVO.getEmail());
        validateMobileUnique(id, reqVO.getMobile());
        // 执行更新
        userMapper.updateById(BeanUtils.toBean(reqVO, AdminUserDO.class).setId(id));
    }

    @Override
    public void updateUserPassword(Long id, UserProfileUpdatePasswordReqVO reqVO) {
        // 校验旧密码密码
        validateOldPassword(id, reqVO.getOldPassword());
        // 执行更新
        AdminUserDO updateObj = new AdminUserDO().setId(id);
        updateObj.setPassword(encodePassword(reqVO.getNewPassword())); // 加密密码
        userMapper.updateById(updateObj);
    }

    @Override
    public String updateUserAvatar(Long id, InputStream avatarFile) {
        validateUserExists(id);
        // 存储文件
        String avatar = fileApi.createFile(IoUtil.readBytes(avatarFile));
        // 更新路径
        AdminUserDO sysUserDO = new AdminUserDO();
        sysUserDO.setId(id);
        sysUserDO.setAvatar(avatar);
        userMapper.updateById(sysUserDO);
        return avatar;
    }

    @Override
    @LogRecord(type = SYSTEM_USER_TYPE, subType = SYSTEM_USER_UPDATE_PASSWORD_SUB_TYPE, bizNo = "{{#id}}",
            success = SYSTEM_USER_UPDATE_PASSWORD_SUCCESS)
    public void updateUserPassword(Long id, String password) {
        // 1. 校验用户存在
        AdminUserDO user = validateUserExists(id);

        // 2. 更新密码
        AdminUserDO updateObj = new AdminUserDO();
        updateObj.setId(id);
        updateObj.setPassword(encodePassword(password)); // 加密密码
        userMapper.updateById(updateObj);

        // 3. 记录操作日志上下文
        LogRecordContext.putVariable("user", user);
        LogRecordContext.putVariable("newPassword", updateObj.getPassword());
    }

    @Override
    public void updateUserStatus(Long id, Integer status) {
        // 校验用户存在
        validateUserExists(id);
        // 更新状态
        AdminUserDO updateObj = new AdminUserDO();
        updateObj.setId(id);
        updateObj.setStatus(status);
        userMapper.updateById(updateObj);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(type = SYSTEM_USER_TYPE, subType = SYSTEM_USER_DELETE_SUB_TYPE, bizNo = "{{#id}}",
            success = SYSTEM_USER_DELETE_SUCCESS)
    public void deleteUser(Long id) {
        // 1. 校验用户存在
        AdminUserDO user = validateUserExists(id);

        // 2.1 删除用户
        userMapper.deleteById(id);
        // 2.2 删除用户关联数据
        permissionService.processUserDeleted(id);
        // 2.2 删除用户岗位
        userPostMapper.deleteByUserId(id);

        // 3. 记录操作日志上下文
        LogRecordContext.putVariable("user", user);
    }

    @Override
    public AdminUserDO getUserByUsername(String username) {
        return userMapper.selectByUsername(username);
    }

    @Override
    public AdminUserDO getUserByMobile(String mobile) {
        return userMapper.selectByMobile(mobile);
    }

    @Override
    public PageResult<AdminUserDO> getUserPage(UserPageReqVO reqVO) {
        // 如果有角色编号，查询角色对应的用户编号
        Set<Long> userIds = reqVO.getRoleId() != null ?
                permissionService.getUserRoleIdListByRoleId(singleton(reqVO.getRoleId())) : null;

        // 分页查询
        return userMapper.selectPage(reqVO, getDeptCondition(reqVO.getDeptId()), userIds);
    }

    @Override
    public AdminUserDO getUser(Long id) {
        return userMapper.selectById(id);
    }

    @Override
    public List<AdminUserDO> getUserListByDeptIds(Collection<Long> deptIds) {
        if (CollUtil.isEmpty(deptIds)) {
            return Collections.emptyList();
        }
        return userMapper.selectListByDeptIds(deptIds);
    }

    @Override
    public List<AdminUserDO> getUserListByPostIds(Collection<Long> postIds) {
        if (CollUtil.isEmpty(postIds)) {
            return Collections.emptyList();
        }
        Set<Long> userIds = convertSet(userPostMapper.selectListByPostIds(postIds), UserPostDO::getUserId);
        if (CollUtil.isEmpty(userIds)) {
            return Collections.emptyList();
        }
        return userMapper.selectBatchIds(userIds);
    }

    @Override
    public List<AdminUserDO> getUserList(Collection<Long> ids) {
        if (CollUtil.isEmpty(ids)) {
            return Collections.emptyList();
        }
        return userMapper.selectBatchIds(ids);
    }

    @Override
    public void validateUserList(Collection<Long> ids) {
        if (CollUtil.isEmpty(ids)) {
            return;
        }
        // 获得岗位信息
        List<AdminUserDO> users = userMapper.selectBatchIds(ids);
        Map<Long, AdminUserDO> userMap = CollectionUtils.convertMap(users, AdminUserDO::getId);
        // 校验
        ids.forEach(id -> {
            AdminUserDO user = userMap.get(id);
            if (user == null) {
                throw exception(USER_NOT_EXISTS);
            }
            if (!CommonStatusEnum.ENABLE.getStatus().equals(user.getStatus())) {
                throw exception(USER_IS_DISABLE, user.getNickname());
            }
        });
    }

    @Override
    public List<AdminUserDO> getUserListByNickname(String nickname) {
        return userMapper.selectListByNickname(nickname);
    }

    /**
     * 获得部门条件：查询指定部门的子部门编号们，包括自身
     *
     * @param deptId 部门编号
     * @return 部门编号集合
     */
    private Set<Long> getDeptCondition(Long deptId) {
        if (deptId == null) {
            return Collections.emptySet();
        }
        Set<Long> deptIds = convertSet(deptService.getChildDeptList(deptId), DeptDO::getId);
        deptIds.add(deptId); // 包括自身
        return deptIds;
    }

    private AdminUserDO validateUserForCreateOrUpdate(Long id, String username, String mobile, String email,
                                                      Long deptId, Set<Long> postIds) {
        // 关闭数据权限，避免因为没有数据权限，查询不到数据，进而导致唯一校验不正确
        return DataPermissionUtils.executeIgnore(() -> {
            // 校验用户存在
            AdminUserDO user = validateUserExists(id);
            // 校验用户名唯一
            validateUsernameUnique(id, username);
            // 校验手机号唯一
            validateMobileUnique(id, mobile);
            // 校验邮箱唯一
            validateEmailUnique(id, email);
            // 校验部门处于开启状态
            deptService.validateDeptList(CollectionUtils.singleton(deptId));
            // 校验岗位处于开启状态
            postService.validatePostList(postIds);
            return user;
        });
    }

    @VisibleForTesting
    AdminUserDO validateUserExists(Long id) {
        if (id == null) {
            return null;
        }
        AdminUserDO user = userMapper.selectById(id);
        if (user == null) {
            throw exception(USER_NOT_EXISTS);
        }
        return user;
    }

    @VisibleForTesting
    void validateUsernameUnique(Long id, String username) {
        if (StrUtil.isBlank(username)) {
            return;
        }
        AdminUserDO user = userMapper.selectByUsername(username);
        if (user == null) {
            return;
        }
        // 如果 id 为空，说明不用比较是否为相同 id 的用户
        if (id == null) {
            throw exception(USER_USERNAME_EXISTS);
        }
        if (!user.getId().equals(id)) {
            throw exception(USER_USERNAME_EXISTS);
        }
    }

    @VisibleForTesting
    void validateEmailUnique(Long id, String email) {
        if (StrUtil.isBlank(email)) {
            return;
        }
        AdminUserDO user = userMapper.selectByEmail(email);
        if (user == null) {
            return;
        }
        // 如果 id 为空，说明不用比较是否为相同 id 的用户
        if (id == null) {
            throw exception(USER_EMAIL_EXISTS);
        }
        if (!user.getId().equals(id)) {
            throw exception(USER_EMAIL_EXISTS);
        }
    }

    @VisibleForTesting
    void validateMobileUnique(Long id, String mobile) {
        if (StrUtil.isBlank(mobile)) {
            return;
        }
        AdminUserDO user = userMapper.selectByMobile(mobile);
        if (user == null) {
            return;
        }
        // 如果 id 为空，说明不用比较是否为相同 id 的用户
        if (id == null) {
            throw exception(USER_MOBILE_EXISTS);
        }
        if (!user.getId().equals(id)) {
            throw exception(USER_MOBILE_EXISTS);
        }
    }

    /**
     * 校验旧密码
     *
     * @param id          用户 id
     * @param oldPassword 旧密码
     */
    @VisibleForTesting
    void validateOldPassword(Long id, String oldPassword) {
        AdminUserDO user = userMapper.selectById(id);
        if (user == null) {
            throw exception(USER_NOT_EXISTS);
        }
        if (!isPasswordMatch(oldPassword, user.getPassword())) {
            throw exception(USER_PASSWORD_FAILED);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class) // 添加事务，异常则回滚所有导入
    public UserImportRespVO importUserList(List<UserImportExcelVO> importUsers, boolean isUpdateSupport) {
        // 1.1 参数校验
        if (CollUtil.isEmpty(importUsers)) {
            throw exception(USER_IMPORT_LIST_IS_EMPTY);
        }
        // 1.2 初始化密码不能为空
        String initPassword = configApi.getConfigValueByKey(USER_INIT_PASSWORD_KEY);
        if (StrUtil.isEmpty(initPassword)) {
            throw exception(USER_IMPORT_INIT_PASSWORD);
        }

        // 2. 遍历，逐个创建 or 更新
        UserImportRespVO respVO = UserImportRespVO.builder().createUsernames(new ArrayList<>())
                .updateUsernames(new ArrayList<>()).failureUsernames(new LinkedHashMap<>()).build();
        importUsers.forEach(importUser -> {
            // 2.1.1 校验字段是否符合要求
            try {
                ValidationUtils.validate(BeanUtils.toBean(importUser, UserSaveReqVO.class).setPassword(initPassword));
            } catch (ConstraintViolationException ex) {
                respVO.getFailureUsernames().put(importUser.getUsername(), ex.getMessage());
                return;
            }
            // 2.1.2 校验，判断是否有不符合的原因
            try {
                validateUserForCreateOrUpdate(null, null, importUser.getMobile(), importUser.getEmail(),
                        importUser.getDeptId(), null);
            } catch (ServiceException ex) {
                respVO.getFailureUsernames().put(importUser.getUsername(), ex.getMessage());
                return;
            }

            // 2.2.1 判断如果不存在，在进行插入
            AdminUserDO existUser = userMapper.selectByUsername(importUser.getUsername());
            if (existUser == null) {
                userMapper.insert(BeanUtils.toBean(importUser, AdminUserDO.class)
                        .setPassword(encodePassword(initPassword)).setPostIds(new HashSet<>())); // 设置默认密码及空岗位编号数组
                respVO.getCreateUsernames().add(importUser.getUsername());
                return;
            }
            // 2.2.2 如果存在，判断是否允许更新
            if (!isUpdateSupport) {
                respVO.getFailureUsernames().put(importUser.getUsername(), USER_USERNAME_EXISTS.getMsg());
                return;
            }
            AdminUserDO updateUser = BeanUtils.toBean(importUser, AdminUserDO.class);
            updateUser.setId(existUser.getId());
            userMapper.updateById(updateUser);
            respVO.getUpdateUsernames().add(importUser.getUsername());
        });
        return respVO;
    }

    @Override
    public List<AdminUserDO> getUserListByStatus(Integer status) {
        return userMapper.selectListByStatus(status);
    }

    @Override
    public boolean isPasswordMatch(String rawPassword, String encodedPassword) {
        return passwordEncoder.matches(rawPassword, encodedPassword);
    }


    /**
     * 对密码进行加密
     *
     * @param password 密码
     * @return 加密后的密码
     */
    private String encodePassword(String password) {
        return passwordEncoder.encode(password);
    }

    /**
     * 解析选科要求字符串，例如“首选物理，再选化学”
     *
     * @param subjectSelection 选科要求字符串
     * @return 选科要求集合
     */
    private Set<String> parseSubjectSelection(String subjectSelection) {
        Set<String> subjects = new HashSet<>();
        if (subjectSelection == null || subjectSelection.isEmpty()) {
            return subjects;
        }

        // 处理首选科目
        if (subjectSelection.contains("首选")) {
            int firstStart = subjectSelection.indexOf("首选") + 2; // “首选”长度为2
            int firstEnd = subjectSelection.indexOf("，", firstStart); // 逗号分隔首选和再选
            if (firstEnd == -1) {
                // 如果没有逗号，可能只有首选科目
                firstEnd = subjectSelection.length();
            }
            String firstSubject = subjectSelection.substring(firstStart, firstEnd).trim();
            subjects.add(firstSubject);
        }

        // 处理再选科目
        if (subjectSelection.contains("再选")) {
            int secondStart = subjectSelection.indexOf("再选") + 2; // “再选”长度为2
            String secondPart = subjectSelection.substring(secondStart).trim();
            // 再选科目可能有多个，用逗号分隔
            String[] secondSubjects = secondPart.split(",");
            for (String subject : secondSubjects) {
                subjects.add(subject.trim());
            }
        }

        return subjects;
    }

    /**
     * 将MajorAdmissionDO列表转换为MajorAdmissionInfo列表
     *
     * @param admissionDOList MajorAdmissionDO列表
     * @return MajorAdmissionInfo列表
     */
    private List<MajorAdmissionInfo> convertToMajorAdmissionInfoList(List<MajorAdmissionDO> admissionDOList) {
        if (admissionDOList == null || admissionDOList.isEmpty()) {
            return new ArrayList<>();
        }

        List<MajorAdmissionInfo> result = new ArrayList<>(admissionDOList.size());
        for (MajorAdmissionDO admissionDO : admissionDOList) {
            MajorAdmissionInfo info = new MajorAdmissionInfo();
            info.setProvinceName(admissionDO.getProvinceName());
            info.setSchoolUUID(admissionDO.getSchoolUuid());
            info.setSchoolName(admissionDO.getSchoolName());
            info.setMajorName(admissionDO.getMajorName());
            info.setMajorCode(admissionDO.getMajorCode());
            info.setYear(admissionDO.getYear());
            info.setHighScore(admissionDO.getHighScore());
            info.setAverageScore(admissionDO.getAverageScore());
            info.setLowestScore(admissionDO.getLowestScore());
            info.setLowestSection(admissionDO.getLowestSection());
            info.setBatchName(admissionDO.getBatchName());
            info.setTypeName(admissionDO.getTypeName());
            info.setProScore(admissionDO.getProScore());
            info.setSubjectSelection(admissionDO.getSubjectSelection());
            info.setMajorStandardCode(admissionDO.getMajorStandardCode());
            result.add(info);
        }

        return result;
    }

    /**
     * 同步获取用户排名信息
     *
     * @param user 用户对象
     */
    private void getUserRanking(AdminUserDO user) {
        if (user == null || user.getProvince() == null || user.getScore() == null) {
            log.warn("Cannot get ranking for user with null province or score");
            return;
        }

        final Long userId = user.getId();
        final String province = user.getProvince();
        final Double userScore = user.getScore();
        final String secondSubject = user.getSecondSubject();

        try {
            log.info("Starting synchronous ranking retrieval for user ID {}", userId);

            // 使用2024年的数据，因为2025年数据尚未更新
            String year = "2024";

            // 确定科目选择类型
            String subjectSelection = SubjectSelectionUtils.getSubjectSelectionType(province, year, secondSubject);

            // 将Double转换为String以便于查询
            String scoreStr = String.valueOf(userScore.intValue());

            // 从数据库查询分数段数据
            List<ScoreSegmentDO> scoreSegments = scoreSegmentService.getScoreSegmentListByCondition(
                    year, province, subjectSelection, user.getScore()+"", user.getScore()+"", null);

            if (scoreSegments.isEmpty()) {
                log.warn("No score segment data found for year: {}, province: {}, subjectSelection: {}",
                        year, province, subjectSelection);
                return;
            }

            // 查找匹配的分数段
            ScoreSegmentDO matchedSegment = findMatchingScoreSegment(scoreSegments, scoreStr);

            if (matchedSegment != null) {
                // 更新用户的排名信息
                AdminUserDO updateObj = new AdminUserDO();
                updateObj.setId(userId);
                updateObj.setRanking(matchedSegment.getRanking());
                updateObj.setRankingRange(matchedSegment.getRankingRange());
                userMapper.updateById(updateObj);

                log.info("User ID {} updated with ranking: {}, rankingRange: {}",
                        userId, matchedSegment.getRanking(), matchedSegment.getRankingRange());
            } else {
                log.warn("No matching score segment found for user ID {} with score {}", userId, scoreStr);
            }
        } catch (Exception e) {
            log.error("Error while retrieving ranking information for user ID " + userId, e);
        }
    }

    /**
     * 异步获取用户排名信息
     *
     * @param user 用户对象
     */
    private void asyncGetUserRanking(AdminUserDO user) {
        if (user == null || user.getProvince() == null || user.getScore() == null) {
            log.warn("Cannot get ranking for user with null province or score");
            return;
        }

        final Long userId = user.getId();
        final String province = user.getProvince();
        final Double userScore = user.getScore();
        final String secondSubject = user.getSecondSubject();

        // 取消之前的任务（如果存在）
        Future<?> previousTask = RANKING_TASKS.get(userId);
        if (previousTask != null && !previousTask.isDone()) {
            log.info("Cancelling previous ranking task for user ID {}", userId);
            previousTask.cancel(true);
        }

        // 创建新的任务
        Future<?> task = RANKING_EXECUTOR.submit(() -> {
            try {
                // 检查任务是否被取消
                if (Thread.currentThread().isInterrupted()) {
                    log.info("Ranking task for user ID {} was cancelled before starting", userId);
                    return;
                }

                log.info("Starting async ranking retrieval for user ID {}", userId);

                // 使用2024年的数据，因为2025年数据尚未更新
                String year = "2024";

                // 确定科目选择类型
                String subjectSelection = SubjectSelectionUtils.getSubjectSelectionType(province, year, secondSubject);

                // 将Double转换为String以便于查询
                String scoreStr = String.valueOf(userScore.intValue());

                // 检查任务是否被取消
                if (Thread.currentThread().isInterrupted()) {
                    log.info("Ranking task for user ID {} was cancelled before database query", userId);
                    return;
                }

                // 从数据库查询分数段数据
                List<ScoreSegmentDO> scoreSegments = scoreSegmentService.getScoreSegmentListByCondition(
                        year, province, subjectSelection, user.getScore()+"", user.getScore()+"", null);

                if (scoreSegments.isEmpty()) {
                    log.warn("No score segment data found for year: {}, province: {}, subjectSelection: {}",
                            year, province, subjectSelection);
                    return;
                }

                // 检查任务是否被取消
                if (Thread.currentThread().isInterrupted()) {
                    log.info("Ranking task for user ID {} was cancelled after database query", userId);
                    return;
                }

                // 查找匹配的分数段
                ScoreSegmentDO matchedSegment = findMatchingScoreSegment(scoreSegments, scoreStr);

                if (matchedSegment != null) {
                    // 检查任务是否被取消
                    if (Thread.currentThread().isInterrupted()) {
                        log.info("Ranking task for user ID {} was cancelled before database update", userId);
                        return;
                    }

                    // 更新用户的排名信息
                    AdminUserDO updateObj = new AdminUserDO();
                    updateObj.setId(userId);
                    updateObj.setRanking(matchedSegment.getRanking());
                    updateObj.setRankingRange(matchedSegment.getRankingRange());
                    userMapper.updateById(updateObj);

                    log.info("User ID {} updated with ranking: {}, rankingRange: {}",
                            userId, matchedSegment.getRanking(), matchedSegment.getRankingRange());

                    // 任务完成，从映射中移除
                    RANKING_TASKS.remove(userId);
                } else {
                    log.warn("No matching score segment found for user ID {} with score {}", userId, scoreStr);
                }
            } catch (Exception e) {
                if (e instanceof InterruptedException || Thread.currentThread().isInterrupted()) {
                    log.info("Ranking task for user ID {} was interrupted", userId);
                } else {
                    log.error("Error while retrieving ranking information for user ID " + userId, e);
                }
            }
        });

        // 将任务添加到映射中
        RANKING_TASKS.put(userId, task);
        log.info("Started async ranking retrieval process for user ID {}", userId);
    }

    /**
     * 根据省份和首选科目确定科目选择类型
     *
     * @param province     省份
     * @param firstSubject 首选科目
     * @return 科目选择类型
     */
    private String determineSubjectSelection(String province, String firstSubject) {
        // 获取当前年份
        String year = String.valueOf(java.time.Year.now().getValue());

        // 使用SubjectSelectionUtils工具类获取科目选择类型
        return SubjectSelectionUtils.getSubjectSelectionType(province, year, firstSubject);
    }

    /**
     * 根据批次名称确定教育级别
     *
     * @param batchName 批次名称
     * @return 教育级别（本科/专科）
     */
    private String determineEducationLevel(String batchName) {
        if (batchName == null || batchName.isEmpty()) {
            return "本科"; // 默认为本科
        }

        // 判断批次名称是否包含"专科"关键词
        if (batchName.contains("专科")) {
            return "专科";
        } else {
            return "本科";
        }
    }

    /**
     * 在分数段列表中查找匹配的分数段
     *
     * @param scoreSegments 分数段列表
     * @param userScore     用户分数
     * @return 匹配的分数段
     */
    private ScoreSegmentDO findMatchingScoreSegment(List<ScoreSegmentDO> scoreSegments, String userScore) {
        for (ScoreSegmentDO segment : scoreSegments) {
            String examScore = segment.getExaminationScore();

            // 如果是精确匹配
            if (examScore.equals(userScore)) {
                return segment;
            }

            // 如果是区间匹配（例如 "673-750"）
            if (examScore.contains("-")) {
                String[] range = examScore.split("-");
                if (range.length == 2) {
                    try {
                        int min = Integer.parseInt(range[0]);
                        int max = Integer.parseInt(range[1]);
                        int score = Integer.parseInt(userScore);

                        if (score >= min && score <= max) {
                            return segment;
                        }
                    } catch (NumberFormatException e) {
                        // 忽略解析错误，继续检查下一个分数段
                    }
                }
            }
        }
        return null;
    }

    /**
     * 根据openId获取用户
     *
     * @param openId
     * @return
     */
    @Override
    public AdminUserDO getUserByOpenId(String openId) {
        QueryWrapper<AdminUserDO> wrapper = new QueryWrapper<>();
        wrapper.eq("open_id", openId);
        return userMapper.selectOne(wrapper);
    }

    /**
     * 在应用程序关闭时优雅地关闭线程池
     */
    @PreDestroy
    public void shutdown() {
        log.info("开始关闭线程池...");

        // 关闭排名查询线程池
        log.info("关闭排名查询线程池...");
        for (Map.Entry<Long, Future<?>> entry : RANKING_TASKS.entrySet()) {
            Long userId = entry.getKey();
            Future<?> task = entry.getValue();
            if (!task.isDone()) {
                log.info("取消用户ID {}的排名查询任务", userId);
                task.cancel(true);
            }
        }
        RANKING_TASKS.clear();

        RANKING_EXECUTOR.shutdown();
        try {
            if (!RANKING_EXECUTOR.awaitTermination(5, TimeUnit.SECONDS)) {
                log.warn("排名查询线程池未在指定时间内关闭");
                RANKING_EXECUTOR.shutdownNow();
            }
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            RANKING_EXECUTOR.shutdownNow();
            log.error("排名查询线程池关闭被中断", e);
        }
        log.info("排名查询线程池已关闭");

        // 关闭专业推荐线程池
        log.info("关闭专业推荐线程池...");
        for (Map.Entry<Long, Future<?>> entry : MAJOR_RECOMMENDATION_TASKS.entrySet()) {
            Long userId = entry.getKey();
            Future<?> task = entry.getValue();
            if (!task.isDone()) {
                log.info("取消用户ID {}的专业推荐任务", userId);
                task.cancel(true);
            }
        }
        MAJOR_RECOMMENDATION_TASKS.clear();

        MAJOR_RECOMMENDATION_EXECUTOR.shutdown();
        try {
            if (!MAJOR_RECOMMENDATION_EXECUTOR.awaitTermination(10, TimeUnit.SECONDS)) {
                log.warn("专业推荐线程池未在指定时间内关闭");
                MAJOR_RECOMMENDATION_EXECUTOR.shutdownNow();
            }
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            MAJOR_RECOMMENDATION_EXECUTOR.shutdownNow();
            log.error("专业推荐线程池关闭被中断", e);
        }
        log.info("专业推荐线程池已关闭");

        log.info("所有线程池已关闭");
    }

    /**
     * 检查用户是否有正在生成中的报告
     *
     * @param userId 用户ID
     * @return 如果有正在生成中的报告，返回true；否则返回false
     */
    private boolean hasGeneratingReport(Long userId) {
        if (userId == null) {
            return false;
        }

        // 查询用户是否有状态为2（生成报告中）的报告记录
        return answerRecordMapper.selectCount(new LambdaQueryWrapperX<AnswerRecordDO>()
                .eq(AnswerRecordDO::getUserId, userId.intValue()) // 将Long转为Integer
                .eq(AnswerRecordDO::getStatus, 1)) > 0;
    }

    // ==================== 优化后的辅助方法 ====================

    /**
     * 解析用户选科信息
     */
    private Set<String> parseUserSubjects(String secondSubject) {
        final Set<String> userSubjects = new HashSet<>();
        if (secondSubject != null && !secondSubject.isEmpty()) {
            String[] subjects = secondSubject.split(",");
            for (String subject : subjects) {
                userSubjects.add(subject.trim());
            }
        }
        return userSubjects;
    }

    /**
     * 批量过滤选科要求
     */
    private List<MajorAdmissionInfo> batchFilterBySubjects(List<MajorAdmissionInfo> admissionList, Set<String> userSubjects) {
        if (userSubjects.isEmpty()) {
            return admissionList; // 如果用户没有选科信息，不进行过滤
        }

        List<MajorAdmissionInfo> filteredBySubjects = new ArrayList<>();
        for (MajorAdmissionInfo info : admissionList) {
            // 如果用户有选科信息，判断是否符合选科要求
            if (info.getSubjectSelection() != null) {
                // 解析选科要求，例如"首选物理，再选化学"
                Set<String> requiredSubjects = parseSubjectSelection(info.getSubjectSelection());

                // 放宽选科要求过滤条件：如果没有任何匹配的科目，才跳过该专业
                // 计算用户选科与专业要求的交集
                Set<String> intersection = new HashSet<>(userSubjects);
                intersection.retainAll(requiredSubjects);

                if (intersection.isEmpty() && !requiredSubjects.isEmpty()) {
                    // 如果交集为空且专业有选科要求，跳过该专业
                    continue;
                }
            }
            filteredBySubjects.add(info);
        }
        return filteredBySubjects;
    }

    /**
     * 批量查询专业信息
     */
    private Map<String, CeeMajorDO> batchQueryMajorInfo(List<MajorAdmissionInfo> filteredBySubjects) {
        // 收集所有需要查询的专业名称
        Map<String, String> originalToCleanedMajorNames = new HashMap<>();
        Map<String, String> majorToEducationLevel = new HashMap<>();

        for (MajorAdmissionInfo info : filteredBySubjects) {
            if (info.getMajorName() != null && !info.getMajorName().isEmpty()) {
                // 去除括号及其中的内容
                String majorName = info.getMajorName()
                        .replaceAll("\\([^)]*\\)", "")
                        .replaceAll("（[^）]*）", "")
                        .trim();

                if (!majorName.isEmpty()) {
                    originalToCleanedMajorNames.put(info.getMajorName(), majorName);
                    // 根据批次名称确定教育级别
                    String educationLevel = determineEducationLevel(info.getBatchName());
                    majorToEducationLevel.put(majorName, educationLevel);
                }
            }
        }

        Map<String, CeeMajorDO> majorCache = new HashMap<>();

        // 批量精确匹配查询
        Set<String> uniqueMajorNames = new HashSet<>(originalToCleanedMajorNames.values());
        for (String majorName : uniqueMajorNames) {
            String educationLevel = majorToEducationLevel.getOrDefault(majorName, "本科");
            CeeMajorDO major = ceeMajorService.getMajorByNameAndEducationLevel(majorName, educationLevel);
            if (major != null) {
                majorCache.put(majorName, major);
            }
        }

        // 对于未找到的专业，进行模糊查询（限制数量）
        Set<String> remainingMajors = new HashSet<>(uniqueMajorNames);
        remainingMajors.removeAll(majorCache.keySet());

        if (!remainingMajors.isEmpty()) {
            // 限制模糊查询的数量，避免性能问题
            List<String> limitedRemainingMajors = new ArrayList<>(remainingMajors);
            if (limitedRemainingMajors.size() > 50) { // 减少到50个
                limitedRemainingMajors = limitedRemainingMajors.subList(0, 50);
            }

            for (String majorName : limitedRemainingMajors) {
                String educationLevel = majorToEducationLevel.getOrDefault(majorName, "本科");
                List<CeeMajorDO> majors = ceeMajorService.searchMajorsByName(majorName, educationLevel);
                if (!majors.isEmpty()) {
                    majorCache.put(majorName, majors.get(0));
                } else {
                    // 如果还是没有找到，尝试不指定教育级别
                    majors = ceeMajorService.searchMajorsByName(majorName, null);
                    if (!majors.isEmpty()) {
                        majorCache.put(majorName, majors.get(0));
                    }
                }
            }
        }

        return majorCache;
    }

    /**
     * 构建专业树形结构
     */
    private Map<String, Map<String, List<MajorInfo>>> buildMajorTree(List<MajorAdmissionInfo> filteredBySubjects,
                                                                     Map<String, CeeMajorDO> majorCache) {
        Map<String, Map<String, List<MajorInfo>>> groupedMajors = new HashMap<>();

        // 创建专业名称映射
        Map<String, String> originalToCleanedMajorNames = new HashMap<>();
        for (MajorAdmissionInfo info : filteredBySubjects) {
            if (info.getMajorName() != null && !info.getMajorName().isEmpty()) {
                String majorName = info.getMajorName()
                        .replaceAll("\\([^)]*\\)", "")
                        .replaceAll("（[^）]*）", "")
                        .trim();
                if (!majorName.isEmpty()) {
                    originalToCleanedMajorNames.put(info.getMajorName(), majorName);
                }
            }
        }

        // 使用缓存的专业信息处理每个专业
        for (MajorAdmissionInfo info : filteredBySubjects) {
            if (info.getMajorName() != null && !info.getMajorName().isEmpty()) {
                String majorName = originalToCleanedMajorNames.get(info.getMajorName());

                if (majorName != null && !majorName.isEmpty()) {
                    CeeMajorDO major = majorCache.get(majorName);
                    if (major != null) {
                        String category = major.getDisciplinaryCategory();
                        String subCategory = major.getDisciplinarySubCategory();

                        // 确保学科门类存在
                        groupedMajors.putIfAbsent(category, new HashMap<>());

                        // 确保学科子类存在
                        Map<String, List<MajorInfo>> subCategoryMap = groupedMajors.get(category);
                        subCategoryMap.putIfAbsent(subCategory, new ArrayList<>());

                        // 创建专业信息对象
                        MajorInfo majorInfo = new MajorInfo();
                        majorInfo.setId(major.getId());
                        majorInfo.setName(major.getMajorName());
                        majorInfo.setCode(major.getMajorCode());
                        majorInfo.setEducationLevel(major.getEducationLevel());
                        majorInfo.setIsRecommended(major.getIsRecommended());
                        majorInfo.setCareerDirection(major.getCareerDirection());
                        majorInfo.setMajorIntroduction(major.getMajorIntroduction());
                        majorInfo.setGraduateScale(major.getGraduateScale());
                        majorInfo.setMaleFemaleRatio(major.getMaleFemaleRatio());
                        majorInfo.setRecommendSchools(major.getRecommendSchools());
                        majorInfo.setCourses(major.getCourses());

                        // 添加专业信息到对应的学科子类下
                        List<MajorInfo> majors = subCategoryMap.get(subCategory);
                        // 检查是否已存在相同ID的专业，避免重复
                        boolean exists = majors.stream().anyMatch(m -> m.getId().equals(majorInfo.getId()));
                        if (!exists) {
                            majors.add(majorInfo);
                        }
                    }
                }
            }
        }

        return groupedMajors;
    }

    /**
     * 批量过滤招生计划数据（高性能版本）
     * 只保留有招生计划的专业
     */
    private List<MajorAdmissionInfo> batchFilterEnrollmentPlans(List<MajorAdmissionInfo> admissionList,
                                                               String province, String typeName) {
        if (admissionList == null || admissionList.isEmpty()) {
            return new ArrayList<>();
        }

        long startTime = System.currentTimeMillis();
        PerformanceMonitor.PerformanceTimer timer = PerformanceMonitor.startTimer("batchFilterEnrollmentPlans");
        log.info("开始批量过滤招生计划，总数量: {}", admissionList.size());

        // 配置参数
        final int BATCH_SIZE = 50;              // 每批处理数量
        final int MAX_CONCURRENT_BATCHES = 4;   // 最大并发批次数
        final int TARGET_RESULT_COUNT = 200;    // 目标结果数量，达到后可提前退出
        final int MAX_PROCESS_COUNT = 1000;     // 最大处理数量，避免处理过多数据

        // 限制处理数量
        List<MajorAdmissionInfo> processedList = admissionList.size() > MAX_PROCESS_COUNT
            ? admissionList.subList(0, MAX_PROCESS_COUNT)
            : admissionList;

        // 对专业进行优先级排序（热门专业优先）
        List<MajorAdmissionInfo> sortedList = prioritizeMajors(processedList);

        // 分批处理
        List<List<MajorAdmissionInfo>> batches = partitionList(sortedList, BATCH_SIZE);
        log.info("分成 {} 个批次进行处理，每批 {} 个专业", batches.size(), BATCH_SIZE);

        // 使用线程安全的集合存储结果
        List<MajorAdmissionInfo> filteredList = Collections.synchronizedList(new ArrayList<>());
        AtomicInteger processedCount = new AtomicInteger(0);
        AtomicBoolean shouldStop = new AtomicBoolean(false);

        // 创建线程池
        ThreadPoolExecutor executor = new ThreadPoolExecutor(
            MAX_CONCURRENT_BATCHES,
            MAX_CONCURRENT_BATCHES,
            60L,
            TimeUnit.SECONDS,
            new LinkedBlockingQueue<>(batches.size()),
            r -> new Thread(r, "enrollment-filter-" + r.hashCode()),
            new ThreadPoolExecutor.CallerRunsPolicy()
        );

        try {
            // 提交批处理任务
            List<Future<Void>> futures = new ArrayList<>();

            for (int i = 0; i < batches.size(); i++) {
                final List<MajorAdmissionInfo> batch = batches.get(i);
                final int batchIndex = i;

                Future<Void> future = executor.submit(() -> {
                    try {
                        // 检查是否应该停止
                        if (shouldStop.get()) {
                            return null;
                        }

                        log.debug("开始处理第 {} 批，包含 {} 个专业", batchIndex + 1, batch.size());
                        List<MajorAdmissionInfo> batchResults = processBatch(batch, province, typeName, shouldStop);

                        // 添加结果
                        filteredList.addAll(batchResults);
                        int currentCount = processedCount.addAndGet(batch.size());

                        log.debug("第 {} 批处理完成，找到 {} 个有招生计划的专业，总进度: {}/{}",
                                batchIndex + 1, batchResults.size(), currentCount, processedList.size());

                        // 检查是否达到目标数量
                        if (filteredList.size() >= TARGET_RESULT_COUNT) {
                            log.info("已找到足够的专业 ({})，提前结束处理", filteredList.size());
                            shouldStop.set(true);
                        }

                    } catch (Exception e) {
                        log.error("处理第 {} 批时发生异常", batchIndex + 1, e);
                    }
                    return null;
                });

                futures.add(future);
            }

            // 等待所有任务完成，设置超时时间
            for (Future<Void> future : futures) {
                try {
                    future.get(30, TimeUnit.SECONDS); // 每个批次最多等待30秒
                } catch (TimeoutException e) {
                    log.warn("批处理任务超时，取消任务");
                    future.cancel(true);
                } catch (Exception e) {
                    log.error("等待批处理任务完成时发生异常", e);
                }
            }

        } finally {
            executor.shutdown();
            try {
                if (!executor.awaitTermination(10, TimeUnit.SECONDS)) {
                    executor.shutdownNow();
                }
            } catch (InterruptedException e) {
                executor.shutdownNow();
                Thread.currentThread().interrupt();
            }
        }

        long endTime = System.currentTimeMillis();
        long totalDuration = endTime - startTime;

        // 记录性能监控
        timer.recordSuccess();
        PerformanceMonitor.recordBatchFilter(processedCount.get(), filteredList.size(), totalDuration);

        log.info("批量过滤完成，原始数量: {}, 处理数量: {}, 过滤后数量: {}, 耗时: {}ms, 过滤率: {:.1f}%",
                admissionList.size(), processedCount.get(), filteredList.size(),
                totalDuration, (double)(processedCount.get() - filteredList.size()) / processedCount.get() * 100);

        return filteredList;
    }

    /**
     * 对专业进行优先级排序
     * 热门专业和知名学校优先处理，提高缓存命中率
     */
    private List<MajorAdmissionInfo> prioritizeMajors(List<MajorAdmissionInfo> majors) {
        // 定义热门专业关键词
        Set<String> hotMajors = CollectionUtils8.mutableSetOf(
            "计算机", "软件", "人工智能", "数据科学", "网络工程", "信息安全",
            "临床医学", "口腔医学", "中医学", "护理学",
            "金融", "经济", "会计", "工商管理", "市场营销",
            "电子信息", "通信工程", "自动化", "电气工程",
            "机械工程", "车辆工程", "土木工程", "建筑学"
        );

        // 定义知名学校关键词
        Set<String> topSchools = CollectionUtils8.mutableSetOf(
            "北京大学", "清华大学", "复旦大学", "上海交通大学", "浙江大学",
            "中国科学技术大学", "南京大学", "华中科技大学", "中山大学", "西安交通大学",
            "哈尔滨工业大学", "北京航空航天大学", "同济大学", "东南大学", "北京理工大学"
        );

        return majors.stream()
            .sorted((a, b) -> {
                // 计算优先级分数
                int scoreA = calculatePriority(a, hotMajors, topSchools);
                int scoreB = calculatePriority(b, hotMajors, topSchools);
                return Integer.compare(scoreB, scoreA); // 降序排列
            })
            .collect(Collectors.toList());
    }

    /**
     * 计算专业的优先级分数
     */
    private int calculatePriority(MajorAdmissionInfo major, Set<String> hotMajors, Set<String> topSchools) {
        int score = 0;

        // 热门专业加分
        String majorName = major.getMajorName();
        if (majorName != null) {
            for (String hotMajor : hotMajors) {
                if (majorName.contains(hotMajor)) {
                    score += 10;
                    break;
                }
            }
        }

        // 知名学校加分
        String schoolName = major.getSchoolName();
        if (schoolName != null) {
            if (topSchools.contains(schoolName)) {
                score += 20;
            } else if (schoolName.contains("大学")) {
                score += 5;
            }
        }

        // 分数越高的专业优先级越高
        if (major.getAverageScore() != null) {
            double avgScore = parseScoreSafely(major.getAverageScore());
            score += (int) (avgScore / 100); // 分数除以100作为加分
        }

        return score;
    }

    /**
     * 将列表分割成指定大小的批次
     */
    private <T> List<List<T>> partitionList(List<T> list, int batchSize) {
        List<List<T>> batches = new ArrayList<>();
        for (int i = 0; i < list.size(); i += batchSize) {
            int end = Math.min(i + batchSize, list.size());
            batches.add(list.subList(i, end));
        }
        return batches;
    }

    /**
     * 处理单个批次的专业
     */
    private List<MajorAdmissionInfo> processBatch(List<MajorAdmissionInfo> batch,
                                                 String province, String typeName,
                                                 AtomicBoolean shouldStop) {
        List<MajorAdmissionInfo> batchResults = new ArrayList<>();

        for (MajorAdmissionInfo admission : batch) {
            // 检查是否应该停止
            if (shouldStop.get()) {
                break;
            }

            try {
                // 使用缓存服务查询招生计划数据
                List<CollegeEnrollmentPlanInfo> enrollmentPlanList = collegeEnrollmentPlanService.getEnrollmentPlanForMajor(
                        admission.getSchoolName(),
                        admission.getMajorName(),
                        province,
                        typeName,
                        2024 // 查询2024年的招生计划数据
                );

                // 如果有招生计划数据，则保留该专业
                if (enrollmentPlanList != null && !enrollmentPlanList.isEmpty()) {
                    batchResults.add(admission);
                    log.debug("专业 {} - {} 有招生计划数据，保留", admission.getMajorName(), admission.getSchoolName());
                }

            } catch (Exception e) {
                log.debug("查询专业 {} - {} 的招生计划数据失败: {}",
                        admission.getMajorName(), admission.getSchoolName(), e.getMessage());
            }
        }

        return batchResults;
    }

    /**
     * 安全地将字符串转换为数值
     * 处理可能的格式问题，如区间分数、非数值字符等
     */
    private double parseScoreSafely(String scoreStr) {
        if (scoreStr == null || scoreStr.trim().isEmpty()) {
            return 0.0;
        }

        try {
            // 去除空格
            String cleanScore = scoreStr.trim();

            // 处理区间分数（如"600-650"），取平均值
            if (cleanScore.contains("-")) {
                String[] parts = cleanScore.split("-");
                if (parts.length == 2) {
                    double min = Double.parseDouble(parts[0].trim());
                    double max = Double.parseDouble(parts[1].trim());
                    return (min + max) / 2.0;
                }
            }

            // 处理可能包含非数字字符的情况
            // 提取数字部分
            String numericPart = cleanScore.replaceAll("[^0-9.]", "");
            if (!numericPart.isEmpty()) {
                return Double.parseDouble(numericPart);
            }

            return 0.0;
        } catch (NumberFormatException e) {
            log.debug("无法解析分数字符串: {}", scoreStr);
            return 0.0;
        }
    }

    /**
     * 保存推荐结果
     */
    private void saveRecommendationResult(Long userId, Map<String, Map<String, List<MajorInfo>>> groupedMajors) {
        try {
            // 构建树形结构
            CeeMajorTreeVO treeVO = new CeeMajorTreeVO();
            List<CeeMajorTreeVO.CategoryNode> categoryNodes = new ArrayList<>();

            // 遍历学科门类
            for (Map.Entry<String, Map<String, List<MajorInfo>>> categoryEntry : groupedMajors.entrySet()) {
                String category = categoryEntry.getKey();
                Map<String, List<MajorInfo>> subCategoryMap = categoryEntry.getValue();

                // 检查该学科门类下是否有包含专业的子类
                boolean hasData = subCategoryMap.values().stream().anyMatch(majors -> !majors.isEmpty());
                if (!hasData) {
                    continue;
                }

                CeeMajorTreeVO.CategoryNode categoryNode = new CeeMajorTreeVO.CategoryNode();
                categoryNode.setId(category);
                categoryNode.setName(category);
                categoryNode.setLoaded(true);

                List<CeeMajorTreeVO.SubCategoryNode> subCategoryNodes = new ArrayList<>();

                // 构建子类节点
                for (Map.Entry<String, List<MajorInfo>> subCategoryEntry : subCategoryMap.entrySet()) {
                    String subCategory = subCategoryEntry.getKey();
                    List<MajorInfo> majors = subCategoryEntry.getValue();

                    if (majors.isEmpty()) {
                        continue;
                    }

                    CeeMajorTreeVO.SubCategoryNode subCategoryNode = new CeeMajorTreeVO.SubCategoryNode();
                    subCategoryNode.setId(subCategory);
                    subCategoryNode.setName(subCategory);

                    List<CeeMajorTreeVO.MajorNode> majorNodes = new ArrayList<>();

                    // 构建专业节点
                    for (MajorInfo major : majors) {
                        CeeMajorTreeVO.MajorNode majorNode = new CeeMajorTreeVO.MajorNode();
                        majorNode.setId(major.getId());
                        majorNode.setName(major.getName());
                        majorNode.setCode(major.getCode());
                        majorNode.setEducationLevel(major.getEducationLevel());
                        majorNode.setIsRecommended(major.getIsRecommended());
                        majorNode.setCareerDirection(major.getCareerDirection());
                        majorNode.setMajorIntroduction(major.getMajorIntroduction());
                        majorNode.setGraduateScale(major.getGraduateScale());
                        majorNode.setMaleFemaleRatio(major.getMaleFemaleRatio());
                        majorNode.setRecommendSchools(major.getRecommendSchools());
                        majorNode.setCourses(major.getCourses());
                        majorNodes.add(majorNode);
                    }

                    subCategoryNode.setChildren(majorNodes);
                    subCategoryNodes.add(subCategoryNode);
                }

                categoryNode.setChildren(subCategoryNodes);
                categoryNodes.add(categoryNode);
            }

            treeVO.setCategories(categoryNodes);

            // 将树形结构转换为JSON字符串
            ObjectMapper objectMapper = new ObjectMapper();
            String recommendedMajorsJson = objectMapper.writeValueAsString(treeVO);

            // 更新用户的推荐专业字段
            AdminUserDO majorUpdateObj = new AdminUserDO();
            majorUpdateObj.setId(userId);
            majorUpdateObj.setRecommendedMajors(recommendedMajorsJson);
            userMapper.updateById(majorUpdateObj);

            int totalMajors = categoryNodes.stream()
                    .flatMap(c -> c.getChildren().stream())
                    .mapToInt(s -> s.getChildren().size())
                    .sum();

            log.info("用户ID {}更新了{}个推荐专业", userId, totalMajors);
        } catch (Exception e) {
            log.error("保存用户ID {}的推荐结果失败", userId, e);
        }
    }

}
