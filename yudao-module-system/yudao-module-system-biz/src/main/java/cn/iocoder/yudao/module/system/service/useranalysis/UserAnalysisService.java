package cn.iocoder.yudao.module.system.service.useranalysis;

import cn.iocoder.yudao.module.system.dal.dataobject.useranalysis.UserAnalysisDO;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.system.controller.admin.useranalysis.vo.UserAnalysisPageReqVO;
import cn.iocoder.yudao.module.system.controller.admin.useranalysis.vo.UserAnalysisSaveReqVO;

import javax.validation.Valid;

/**
 * 用户分析 Service 接口
 *
 * <AUTHOR>
 */
public interface UserAnalysisService {

    /**
     * 创建用户分析
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Integer createUserAnalysis(@Valid UserAnalysisSaveReqVO createReqVO);

    /**
     * 更新用户分析
     *
     * @param updateReqVO 更新信息
     */
    void updateUserAnalysis(@Valid UserAnalysisSaveReqVO updateReqVO);

    /**
     * 删除用户分析
     *
     * @param id 编号
     */
    void deleteUserAnalysis(Integer id);

    /**
     * 获得用户分析
     *
     * @param id 编号
     * @return 用户分析
     */
    UserAnalysisDO getUserAnalysis(Integer id);

    /**
     * 获得用户分析分页
     *
     * @param pageReqVO 分页查询
     * @return 用户分析分页
     */
    PageResult<UserAnalysisDO> getUserAnalysisPage(UserAnalysisPageReqVO pageReqVO);

}