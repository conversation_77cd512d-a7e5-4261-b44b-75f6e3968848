package cn.iocoder.yudao.module.system.controller.admin.gugu.gugu;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

/**
 * 历年高考专业录取数据查询请求 VO
 */
@Schema(description = "历年高考专业录取数据查询请求参数")
@Data
public class MajorAdmissionQueryReqVO {

    @Schema(description = "页码", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "页码不能为空")
    @Min(value = 1, message = "页码不能小于1")
    private Integer pageindex;

    @Schema(description = "每页数据量", requiredMode = Schema.RequiredMode.REQUIRED, example = "10")
    @NotNull(message = "每页数据量不能为空")
    private Integer pagesize;

    @Schema(description = "招生省份", example = "江苏")
    private String enrollprovince;

    @Schema(description = "高校名称", example = "北京大学")
    private String schoolname;

    @Schema(description = "专业名称", example = "计算机科学与技术")
    private String majorname;

    @Schema(description = "是否严格匹配专业名称", example = "false")
    private Boolean majornamestrict = false;

    @Schema(description = "专业ID", example = "22748")
    private Integer specialid;

    @Schema(description = "录取年份", example = "2023")
    private Integer year = 0;

    @Schema(description = "录取最低分查询条件", example = "600")
    private Integer min = 0;

    @Schema(description = "专业所属的录取批次", example = "本科一批")
    private String batchname;

    @Schema(description = "专业所属的类型", example = "理科")
    private String typename;

    @Schema(description = "高校唯一ID", example = "c24a67f87405b82bec08a5638c32f282")
    private String schooluuid;

    @Schema(description = "录取最低分区间查询条件", example = "500,700")
    private String minrange;

    @Schema(description = "选科要求筛选条件", example = "首选物理，再选化学")
    private String subjectselection;

    @Schema(description = "起始页码，用于继续从指定页码开始获取数据", example = "10")
    private Integer startPageIndex;

    @Schema(description = "结束页码，用于指定获取数据的结束页码", example = "20")
    private Integer endPageIndex;
}
