package cn.iocoder.yudao.module.system.controller.admin.gugu.gugu;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * 历年高考专业录取数据查询响应 VO
 */
@Schema(description = "历年高考专业录取数据查询响应结果")
@Data
public class MajorAdmissionQueryRespVO {

    @Schema(description = "专业录取数据列表")
    private List<MajorAdmissionInfo> admissionList;

    @Schema(description = "总数据量")
    private Integer totalCount;

    @Schema(description = "符合条件的高校数量")
    private Integer distinctSchoolCount;

    @Schema(description = "当前页码")
    private Integer pageIndex;

    @Schema(description = "每页数据量")
    private Integer pageSize;

    @Schema(description = "总页数")
    private Integer totalPages;
}
