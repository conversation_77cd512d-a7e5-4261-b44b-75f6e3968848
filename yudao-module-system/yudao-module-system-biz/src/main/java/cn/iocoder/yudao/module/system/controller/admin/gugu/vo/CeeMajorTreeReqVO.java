package cn.iocoder.yudao.module.system.controller.admin.gugu.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 大学高校专业树形结构请求 VO
 */
@Schema(description = "管理后台 - 大学高校专业树形结构请求 VO")
@Data
public class CeeMajorTreeReqVO {

    @Schema(description = "教育级别", example = "本科")
    private String educationLevel;

    @Schema(description = "学科门类", example = "工学")
    private String disciplinaryCategory;

    @Schema(description = "学科门类 ID，当传递时只加载该门类下的数据")
    private String categoryId;

    @Schema(description = "专业名称，模糊搜索", example = "计算机")
    private String majorName;
}
