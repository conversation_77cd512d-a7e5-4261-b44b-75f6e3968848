package cn.iocoder.yudao.module.system.controller.admin.employmentdir;

import cn.iocoder.yudao.module.system.controller.admin.employmentdir.vo.EmploymentDirPageReqVO;
import cn.iocoder.yudao.module.system.controller.admin.employmentdir.vo.EmploymentDirRespVO;
import cn.iocoder.yudao.module.system.controller.admin.employmentdir.vo.EmploymentDirSaveReqVO;
import cn.iocoder.yudao.module.system.dal.dataobject.employmentdir.EmploymentDirDO;
import cn.iocoder.yudao.module.system.service.employmentdir.EmploymentDirService;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import javax.annotation.security.PermitAll;
import javax.validation.constraints.*;
import javax.validation.*;
import javax.servlet.http.*;
import java.util.*;
import java.io.IOException;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

import cn.iocoder.yudao.framework.excel.core.util.ExcelUtils;

import cn.iocoder.yudao.framework.apilog.core.annotation.ApiAccessLog;
import static cn.iocoder.yudao.framework.apilog.core.enums.OperateTypeEnum.*;


@Tag(name = "管理后台 - 就业方向")
@RestController
@RequestMapping("/kf/employment-dir")
@Validated
public class EmploymentDirController {

    @Resource
    private EmploymentDirService employmentDirService;

    @PostMapping("/create")
    @Operation(summary = "创建就业方向")
    @PermitAll
    public CommonResult<Long> createEmploymentDir(@Valid @RequestBody EmploymentDirSaveReqVO createReqVO) {
        return success(employmentDirService.createEmploymentDir(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新就业方向")
    @PermitAll
    public CommonResult<Boolean> updateEmploymentDir(@Valid @RequestBody EmploymentDirSaveReqVO updateReqVO) {
        employmentDirService.updateEmploymentDir(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除就业方向")
    @Parameter(name = "id", description = "编号", required = true)
    @PermitAll
    public CommonResult<Boolean> deleteEmploymentDir(@RequestParam("id") Long id) {
        employmentDirService.deleteEmploymentDir(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得就业方向")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PermitAll
    public CommonResult<EmploymentDirRespVO> getEmploymentDir(@RequestParam("id") Long id) {
        EmploymentDirDO employmentDir = employmentDirService.getEmploymentDir(id);
        return success(BeanUtils.toBean(employmentDir, EmploymentDirRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得就业方向分页")
    @PermitAll
    public CommonResult<PageResult<EmploymentDirRespVO>> getEmploymentDirPage(@Valid EmploymentDirPageReqVO pageReqVO) {
        PageResult<EmploymentDirDO> pageResult = employmentDirService.getEmploymentDirPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, EmploymentDirRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出就业方向 Excel")
    @PermitAll
    @ApiAccessLog(operateType = EXPORT)
    public void exportEmploymentDirExcel(@Valid EmploymentDirPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<EmploymentDirDO> list = employmentDirService.getEmploymentDirPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "就业方向.xls", "数据", EmploymentDirRespVO.class,
                        BeanUtils.toBean(list, EmploymentDirRespVO.class));
    }

}