package cn.iocoder.yudao.module.system.service.employmentdir;

import java.util.*;
import javax.validation.*;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.system.controller.admin.employmentdir.vo.EmploymentDirPageReqVO;
import cn.iocoder.yudao.module.system.controller.admin.employmentdir.vo.EmploymentDirSaveReqVO;
import cn.iocoder.yudao.module.system.dal.dataobject.employmentdir.EmploymentDirDO;

/**
 * 就业方向 Service 接口
 *
 * <AUTHOR>
 */
public interface EmploymentDirService {

    /**
     * 创建就业方向
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createEmploymentDir(@Valid EmploymentDirSaveReqVO createReqVO);

    /**
     * 更新就业方向
     *
     * @param updateReqVO 更新信息
     */
    void updateEmploymentDir(@Valid EmploymentDirSaveReqVO updateReqVO);

    /**
     * 删除就业方向
     *
     * @param id 编号
     */
    void deleteEmploymentDir(Long id);

    /**
     * 获得就业方向
     *
     * @param id 编号
     * @return 就业方向
     */
    EmploymentDirDO getEmploymentDir(Long id);

    /**
     * 获得就业方向分页
     *
     * @param pageReqVO 分页查询
     * @return 就业方向分页
     */
    PageResult<EmploymentDirDO> getEmploymentDirPage(EmploymentDirPageReqVO pageReqVO);

}