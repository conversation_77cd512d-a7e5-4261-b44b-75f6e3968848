package cn.iocoder.yudao.module.system.controller.admin.employmentessay.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import javax.validation.constraints.*;

@Schema(description = "管理后台 - 就业文章新增/修改 Request VO")
@Data
public class EmploymentEssaySaveReqVO {

    @Schema(description = "主键id", requiredMode = Schema.RequiredMode.REQUIRED, example = "31498")
    private Long id;

    @Schema(description = "就业方向名称", example = "王五")
    private String dirName;

    @Schema(description = "就业方向id", example = "3401")
    private Integer dirId;

    @Schema(description = "名称", example = "芋艿")
    private String name;

    @Schema(description = "对口院校id", example = "23884")
    private String schoolId;

    @Schema(description = "对口院校名称", example = "王五")
    private String schoolName;

    @Schema(description = "文章内容")
    private String content;

    @Schema(description = "状态", example = "2")
    private Integer status;

    @Schema(description = "排序")
    private Integer sort;

}