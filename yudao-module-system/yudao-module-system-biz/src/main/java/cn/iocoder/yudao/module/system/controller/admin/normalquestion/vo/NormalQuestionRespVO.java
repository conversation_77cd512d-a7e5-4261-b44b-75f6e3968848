package cn.iocoder.yudao.module.system.controller.admin.normalquestion.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;
import com.alibaba.excel.annotation.*;

@Schema(description = "管理后台 - AI 报告 Response VO")
@Data
@ExcelIgnoreUnannotated
public class NormalQuestionRespVO {

    @Schema(description = "主键id", requiredMode = Schema.RequiredMode.REQUIRED, example = "7770")
    @ExcelProperty("主键id")
    private Long id;

    @Schema(description = "内容")
    @ExcelProperty("内容")
    private String content;

    @Schema(description = "子类型", example = "2")
    @ExcelProperty("子类型")
    private String type;

    @Schema(description = "类型 1- 行业趋势 2-志愿100问 3-热门专业 4-慎选专业", example = "2")
    @ExcelProperty("类型 1- 行业趋势 2-志愿100问 3-热门专业 4-慎选专业")
    private String parentType;

    @Schema(description = "问题")
    @ExcelProperty("问题")
    private String question;

    @Schema(description = "版本")
    @ExcelProperty("版本")
    private Integer version;

    @Schema(description = "创建时间")
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

    @Schema(description = "星星")
    @ExcelProperty("星星")
    private Integer star;

    @Schema(description = "指令")
    @ExcelProperty("指令")
    private String ds;

}