package cn.iocoder.yudao.module.system.controller.admin.school.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;
import com.alibaba.excel.annotation.*;
import cn.iocoder.yudao.framework.excel.core.annotations.DictFormat;
import cn.iocoder.yudao.framework.excel.core.convert.DictConvert;

@Schema(description = "管理后台 - 院校 Response VO")
@Data
@ExcelIgnoreUnannotated
public class SchoolRespVO {

    @Schema(description = "主键id", requiredMode = Schema.RequiredMode.REQUIRED, example = "882")
    @ExcelProperty("主键id")
    private Long id;

    @Schema(description = "名称", example = "李四")
    @ExcelProperty("名称")
    private String name;

    @Schema(description = "省份")
    @ExcelProperty(value = "省份", converter = DictConvert.class)
    @DictFormat("province") // TODO 代码优化：建议设置到对应的 DictTypeConstants 枚举类中
    private String province;

    @Schema(description = "省份名")
    private String provinceName;

    @Schema(description = "排序")
    @ExcelProperty("排序")
    private Integer sort;

    @Schema(description = "创建时间")
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}