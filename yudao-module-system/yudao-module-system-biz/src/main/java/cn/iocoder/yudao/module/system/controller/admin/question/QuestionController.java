package cn.iocoder.yudao.module.system.controller.admin.question;

import cn.iocoder.yudao.module.system.dal.dataobject.oauth2.OAuth2AccessTokenDO;
import cn.iocoder.yudao.module.system.dal.dataobject.question.*;
import cn.iocoder.yudao.module.system.dal.dataobject.user.AdminUserDO;
import cn.iocoder.yudao.module.system.dal.mysql.oauth2.OAuth2AccessTokenMapper;
import cn.iocoder.yudao.module.system.dal.mysql.question.QuestionChoiceMapper;
import cn.iocoder.yudao.module.system.service.answer.UserAnswerService;
import cn.iocoder.yudao.module.system.service.user.AdminUserService;
import org.springframework.web.bind.annotation.*;

import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import java.util.*;
import java.io.IOException;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;

import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

import cn.iocoder.yudao.framework.excel.core.util.ExcelUtils;

import cn.iocoder.yudao.framework.apilog.core.annotation.ApiAccessLog;

import static cn.iocoder.yudao.framework.apilog.core.enums.OperateTypeEnum.*;

import cn.iocoder.yudao.module.system.controller.admin.question.vo.*;
import cn.iocoder.yudao.module.system.service.question.QuestionService;

import javax.annotation.Resource;
import javax.annotation.security.PermitAll;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;

@Tag(name = "管理后台 - 问题")
@RestController
@RequestMapping("/system/question")
@Validated
public class QuestionController {

    @Resource
    private QuestionService questionService;
    @Resource
    private UserAnswerService userAnswerService;
    @Resource
    private QuestionChoiceMapper questionChoiceMapper;
    @Resource
    private AdminUserService userService;


    @PostMapping("/create")
    @Operation(summary = "创建问题")
    @PreAuthorize("@ss.hasPermission('system:question:create')")
    public CommonResult<Integer> createQuestion(@Valid @RequestBody QuestionSaveReqVO createReqVO) {
        return success(questionService.createQuestion(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新问题")
    @PreAuthorize("@ss.hasPermission('system:question:update')")
    public CommonResult<Boolean> updateQuestion(@Valid @RequestBody QuestionSaveReqVO updateReqVO) {
        questionService.updateQuestion(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除问题")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('system:question:delete')")
    public CommonResult<Boolean> deleteQuestion(@RequestParam("id") Integer id) {
        questionService.deleteQuestion(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得问题")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PermitAll
    public CommonResult<QuestionRespVO> getQuestion(@RequestParam("id") Integer id) {
        QuestionDO question = questionService.getQuestion(id);
        return success(BeanUtils.toBean(question, QuestionRespVO.class));

    }

    @GetMapping("/page")
    @Operation(summary = "获得问题分页")
    @PreAuthorize("@ss.hasPermission('system:question:query')")
    public CommonResult<PageResult<QuestionRespVO>> getQuestionPage(@Valid QuestionPageReqVO pageReqVO) {
        PageResult<QuestionDO> pageResult = questionService.getQuestionPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, QuestionRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出问题 Excel")
    @PreAuthorize("@ss.hasPermission('system:question:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportQuestionExcel(@Valid QuestionPageReqVO pageReqVO,
                                    HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<QuestionDO> list = questionService.getQuestionPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "问题.xls", "数据", QuestionRespVO.class,
                BeanUtils.toBean(list, QuestionRespVO.class));
    }

    // ==================== 子表（问题选项） ====================
    @GetMapping("/choice/list-by-question-id")
    @Operation(summary = "获得问题选项列表")
    @Parameter(name = "questionId", description = "问题id")
    @PreAuthorize("@ss.hasPermission('system:question:query')")
    public CommonResult<List<QuestionChoiceDO>> getChoiceListByQuestionId(@RequestParam("questionId") Integer questionId) {
        return success(questionService.getChoiceListByQuestionId(questionId));
    }

    //查询问题
    @Resource
    private OAuth2AccessTokenMapper oAuth2AccessTokenMapper;

    /**
     * 重新开始答题
     * 无论何种情况 返回全部的题目
     */
    @GetMapping("/restartAnswer")
    public CommonResult<List<QuestionVo>> restartAnswer(HttpServletRequest request, @RequestParam("type") Integer type) {
        return success(questionService.loadQuestionByType(type));
    }


}