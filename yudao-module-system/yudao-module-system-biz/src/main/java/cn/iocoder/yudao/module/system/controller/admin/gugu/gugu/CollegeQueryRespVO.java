package cn.iocoder.yudao.module.system.controller.admin.gugu.gugu;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * 高校查询响应 VO
 */
@Schema(description = "高校查询响应结果")
@Data
public class CollegeQueryRespVO {

    @Schema(description = "高校列表")
    private List<CollegeInfo> collegeList;

    @Schema(description = "总数据量")
    private Integer totalCount;

    @Schema(description = "当前页码")
    private Integer pageIndex;

    @Schema(description = "每页数据量")
    private Integer pageSize;

    @Schema(description = "总页数")
    private Integer totalPages;
}
