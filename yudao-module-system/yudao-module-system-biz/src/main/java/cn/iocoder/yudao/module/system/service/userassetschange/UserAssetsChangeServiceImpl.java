package cn.iocoder.yudao.module.system.service.userassetschange;

import cn.iocoder.yudao.framework.common.exception.ErrorCode;
import cn.iocoder.yudao.module.system.controller.admin.userassetschange.vo.UserAssetsChangePageReqVO;
import cn.iocoder.yudao.module.system.controller.admin.userassetschange.vo.UserAssetsChangeSaveReqVO;
import cn.iocoder.yudao.module.system.dal.dataobject.userassetschange.UserAssetsChangeDO;
import cn.iocoder.yudao.module.system.dal.mysql.userassetschange.UserAssetsChangeMapper;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;

import java.util.*;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;


import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;

/**
 * 用户资源兑换套餐 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class UserAssetsChangeServiceImpl implements UserAssetsChangeService {

    @Resource
    private UserAssetsChangeMapper userAssetsChangeMapper;

    @Override
    public Integer createUserAssetsChange(UserAssetsChangeSaveReqVO createReqVO) {
        // 插入
        UserAssetsChangeDO userAssetsChange = BeanUtils.toBean(createReqVO, UserAssetsChangeDO.class);
        userAssetsChangeMapper.insert(userAssetsChange);
        // 返回
        return userAssetsChange.getId();
    }

    @Override
    public void updateUserAssetsChange(UserAssetsChangeSaveReqVO updateReqVO) {
        // 校验存在
        validateUserAssetsChangeExists(updateReqVO.getId());
        // 更新
        UserAssetsChangeDO updateObj = BeanUtils.toBean(updateReqVO, UserAssetsChangeDO.class);
        userAssetsChangeMapper.updateById(updateObj);
    }

    @Override
    public void deleteUserAssetsChange(Integer id) {
        // 校验存在
        validateUserAssetsChangeExists(id);
        // 删除
        userAssetsChangeMapper.deleteById(id);
    }

    private void validateUserAssetsChangeExists(Integer id) {
        if (userAssetsChangeMapper.selectById(id) == null) {
            throw exception(new ErrorCode(404,"用户资源不存在"));
        }
    }

    @Override
    public UserAssetsChangeDO getUserAssetsChange(Integer id) {
        return userAssetsChangeMapper.selectById(id);
    }

    @Override
    public PageResult<UserAssetsChangeDO> getUserAssetsChangePage(UserAssetsChangePageReqVO pageReqVO) {
        return userAssetsChangeMapper.selectPage(pageReqVO);
    }

}