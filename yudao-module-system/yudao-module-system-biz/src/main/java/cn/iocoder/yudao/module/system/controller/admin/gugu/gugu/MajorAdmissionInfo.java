package cn.iocoder.yudao.module.system.controller.admin.gugu.gugu;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * 历年高考专业录取数据实体类
 */
@Schema(description = "历年高考专业录取数据")
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class MajorAdmissionInfo {

    @JsonProperty("ProvinceName")
    private String provinceName;

    @JsonProperty("SchoolUUID")
    private String schoolUUID;

    @JsonProperty("SchoolName")
    private String schoolName;

    @JsonProperty("MajorName")
    private String majorName;

    @JsonProperty("MajorCode")
    private Integer majorCode;

    @JsonProperty("Year")
    private Integer year;

    @JsonProperty("HighSocre")
    private String highScore;

    @JsonProperty("AverageScore")
    private String averageScore;

    @JsonProperty("LowestScore")
    private String lowestScore;

    @JsonProperty("LowestSection")
    private String lowestSection;

    @JsonProperty("BatchName")
    private String batchName;

    @JsonProperty("TypeName")
    private String typeName;

    @JsonProperty("ProScore")
    private String proScore;

    @JsonProperty("subjectSelection")
    private String subjectSelection;

    @JsonProperty("MajorStandardCode")
    private String majorStandardCode;

    /**
     * 历史年份数据（2021-2023年）
     * 不从API直接获取，而是通过代码查询添加
     */
    @Schema(description = "历史年份数据（2021-2023年）")
    private List<HistoricalYearData> historicalData;

    /**
     * 历史数据是否正在异步加载中
     * 当该字段为true时，表示历史数据正在后台加载，前端可以展示加载中的状态
     */
    @Schema(description = "历史数据是否正在异步加载中")
    private Boolean historicalDataLoading = false;

    /**
     * 历史数据加载标识，用于异步加载时标识不同的专业
     */
    @Schema(description = "历史数据加载标识")
    private String historicalDataLoadId;

    /**
     * 招生计划数据
     * 包含该专业的招生计划信息，如招生人数、选科要求等
     */
    @Schema(description = "招生计划数据")
    private List<CollegeEnrollmentPlanInfo> enrollmentPlanData;

    /**
     * 招生计划数据是否正在异步加载中
     */
    @Schema(description = "招生计划数据是否正在异步加载中")
    private Boolean enrollmentPlanDataLoading = false;

    /**
     * 历史年份数据内部类
     */
    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class HistoricalYearData {
        /**
         * 年份
         */
        @Schema(description = "年份")
        private Integer year;

        /**
         * 最高分
         */
        @Schema(description = "最高分")
        private String highScore;

        /**
         * 平均分
         */
        @Schema(description = "平均分")
        private String averageScore;

        /**
         * 最低分
         */
        @Schema(description = "最低分")
        private String lowestScore;

        /**
         * 最低位次
         */
        @Schema(description = "最低位次")
        private String lowestSection;

        @Override
        public boolean equals(Object o) {
            if (this == o) return true;
            if (o == null || getClass() != o.getClass()) return false;
            HistoricalYearData that = (HistoricalYearData) o;

            // 比较年份、最低分和最低位次
            if (year == null || that.year == null) return false;
            if (!year.equals(that.year)) return false;

            if (lowestScore == null || that.lowestScore == null) return false;
            if (!lowestScore.equals(that.lowestScore)) return false;

            if (lowestSection == null || that.lowestSection == null) return false;
            return lowestSection.equals(that.lowestSection);
        }

        @Override
        public int hashCode() {
            int result = year != null ? year.hashCode() : 0;
            result = 31 * result + (lowestScore != null ? lowestScore.hashCode() : 0);
            result = 31 * result + (lowestSection != null ? lowestSection.hashCode() : 0);
            return result;
        }
    }
}
