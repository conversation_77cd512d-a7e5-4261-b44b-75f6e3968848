package cn.iocoder.yudao.module.system.controller.admin.employmentessay.vo;

import lombok.*;
import java.util.*;
import io.swagger.v3.oas.annotations.media.Schema;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 就业文章分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class EmploymentEssayPageReqVO extends PageParam {

    @Schema(description = "用户ID")
    private Long userId;

    @Schema(description = "就业方向名称", example = "王五")
    private String dirName;

    @Schema(description = "就业方向id", example = "3401")
    private Integer dirId;

    @Schema(description = "名称", example = "芋艿")
    private String name;

    @Schema(description = "对口院校id", example = "23884")
    private String schoolId;

    @Schema(description = "对口院校名称", example = "王五")
    private String schoolName;

    @Schema(description = "文章内容")
    private String content;

    @Schema(description = "状态", example = "2")
    private Integer status;

    @Schema(description = "排序")
    private Integer sort;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}