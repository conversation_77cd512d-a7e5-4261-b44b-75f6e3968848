package cn.iocoder.yudao.module.system.dal.mysql.employmentessay;

import java.util.*;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.module.system.controller.admin.employmentessay.vo.EmploymentEssayPageReqVO;
import cn.iocoder.yudao.module.system.dal.dataobject.employmentessay.EmploymentEssayDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 就业文章 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface EmploymentEssayMapper extends BaseMapperX<EmploymentEssayDO> {

    default PageResult<EmploymentEssayDO> selectPage(EmploymentEssayPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<EmploymentEssayDO>()
                .eqIfPresent(EmploymentEssayDO::getName, reqVO.getName())
                .eqIfPresent(EmploymentEssayDO::getSchoolId, reqVO.getSchoolId())
                .eqIfPresent(EmploymentEssayDO::getSchoolName, reqVO.getSchoolName())
                .eqIfPresent(EmploymentEssayDO::getDirName, reqVO.getDirName())
                .eqIfPresent(EmploymentEssayDO::getContent, reqVO.getContent())
                .eqIfPresent(EmploymentEssayDO::getStatus, reqVO.getStatus())
                .eqIfPresent(EmploymentEssayDO::getSort, reqVO.getSort())
                .betweenIfPresent(EmploymentEssayDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(EmploymentEssayDO::getId));
    }

}