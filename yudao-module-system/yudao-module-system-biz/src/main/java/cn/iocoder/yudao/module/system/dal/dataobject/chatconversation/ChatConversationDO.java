package cn.iocoder.yudao.module.system.dal.dataobject.chatconversation;

import lombok.*;
import java.util.*;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.*;
import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;

/**
 * AI 聊天对话 DO
 *
 * <AUTHOR>
 */
@TableName("ai_chat_conversation")
@KeySequence("ai_chat_conversation_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ChatConversationDO extends BaseDO {

    /**
     * 对话编号
     */
    @TableId
    private Long id;
    /**
     * 用户编号
     */
    private Long userId;
    /**
     * 聊天角色
     */
    private Long roleId;
    /**
     * 对话标题
     */
    private String title;
    /**
     * 模型编号
     */
    private Long modelId;
    /**
     * 模型标识
     */
    private String model;
    /**
     * 是否置顶
     */
    private Boolean pinned;
    /**
     * 置顶时间
     */
    private LocalDateTime pinnedTime;
    /**
     * 角色设定
     */
    private String systemMessage;
    /**
     * 温度参数
     */
    private Double temperature;
    /**
     * 单条回复的最大 Token 数量
     */
    private Integer maxTokens;
    /**
     * 上下文的最大 Message 数量
     */
    private Integer maxContexts;

}