package cn.iocoder.yudao.module.system.controller.admin.answer.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;
import com.alibaba.excel.annotation.*;

@Schema(description = "管理后台 - 用户答案 Response VO")
@Data
@ExcelIgnoreUnannotated
public class UserAnswerRespVO {

    @Schema(description = "主键id,即答案Id", requiredMode = Schema.RequiredMode.REQUIRED, example = "6707")
    @ExcelProperty("主键id,即答案Id")
    private Integer id;

    @Schema(description = "用户Id", requiredMode = Schema.RequiredMode.REQUIRED, example = "32599")
    @ExcelProperty("用户Id")
    private Integer userId;

    @Schema(description = "问题id", requiredMode = Schema.RequiredMode.REQUIRED, example = "17396")
    @ExcelProperty("问题id")
    private Integer questionId;

    @Schema(description = "用户手写内容")
    @ExcelProperty("用户手写内容")
    private String writeContent;

    @Schema(description = "用户所选择答案Id集合，用逗号连接")
    @ExcelProperty("用户所选择答案Id集合，用逗号连接")
    private String answerChoices;

    @Schema(description = "用户第多少次答题")
    @ExcelProperty("用户第多少次答题")
    private Integer answerNo;

    @Schema(description = "选项创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("选项创建时间")
    private LocalDateTime createTime;

}