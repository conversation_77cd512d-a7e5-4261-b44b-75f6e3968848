package cn.iocoder.yudao.module.system.service.gugu;

import cn.iocoder.yudao.module.system.controller.admin.gugu.gugu.CollegeEnrollmentPlanInfo;
import cn.iocoder.yudao.module.system.controller.admin.gugu.gugu.MajorAdmissionInfo;
import cn.iocoder.yudao.module.system.controller.admin.gugu.gugu.UserProfileInfo;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * UserProfileService增强功能示例
 * 展示如何使用包含招生计划数据的专业推荐功能
 */
public class UserProfileServiceEnhancedExample {

    /**
     * 示例：使用增强后的专业推荐功能
     */
    public static void demonstrateEnhancedRecommendation() {
        // 创建用户个人信息
        UserProfileInfo profile = new UserProfileInfo();
        profile.setProvince("北京");
        profile.setYear(2024);
        profile.setGender("男");
        profile.setSubjects(Arrays.asList("物理", "化学", "生物"));
        profile.setTotalScore(650);
        profile.setPersonalityTraits(Arrays.asList("学习能力强", "逻辑思维强"));
        profile.setGraduationPlan("就业");
        profile.setInterestedMajorCategories(Arrays.asList("计算机", "软件工程"));
        profile.setPreferredLocation("北京");
        profile.setTypeName("理科");

        // 注意：这里只是示例代码，实际使用时需要注入UserProfileService
        // UserProfileService userProfileService = ...; // 通过Spring注入获取
        
        System.out.println("=== 增强后的专业推荐功能示例 ===");
        System.out.println("用户信息:");
        System.out.println("  省份: " + profile.getProvince());
        System.out.println("  年份: " + profile.getYear());
        System.out.println("  选科: " + String.join("、", profile.getSubjects()));
        System.out.println("  总分: " + profile.getTotalScore());
        System.out.println("  感兴趣专业: " + String.join("、", profile.getInterestedMajorCategories()));
        System.out.println();

        // 模拟调用推荐方法
        // Map<String, Object> result = userProfileService.recommendMajors(profile);
        
        // 以下是模拟的结果展示
        System.out.println("推荐结果包含以下信息:");
        System.out.println("1. 历史录取数据（2021-2023年）");
        System.out.println("2. 招生计划数据（2024年）");
        System.out.println("3. 分类推荐（高分、同分、低分专业）");
        System.out.println();

        // 模拟展示专业信息结构
        demonstrateMajorInfoStructure();
    }

    /**
     * 展示专业信息的数据结构
     */
    private static void demonstrateMajorInfoStructure() {
        System.out.println("=== 专业信息数据结构示例 ===");
        
        // 创建示例专业信息
        MajorAdmissionInfo majorInfo = new MajorAdmissionInfo();
        majorInfo.setSchoolName("北京大学");
        majorInfo.setMajorName("计算机科学与技术");
        majorInfo.setLowestScore("645");
        majorInfo.setProvinceName("北京");
        majorInfo.setYear(2024);
        majorInfo.setBatchName("本科一批");
        majorInfo.setTypeName("理科");

        // 模拟历史数据
        MajorAdmissionInfo.HistoricalYearData history2023 = new MajorAdmissionInfo.HistoricalYearData();
        history2023.setYear(2023);
        history2023.setLowestScore("642");
        history2023.setLowestSection("1200");

        MajorAdmissionInfo.HistoricalYearData history2022 = new MajorAdmissionInfo.HistoricalYearData();
        history2022.setYear(2022);
        history2022.setLowestScore("638");
        history2022.setLowestSection("1350");

        majorInfo.setHistoricalData(Arrays.asList(history2023, history2022));

        // 模拟招生计划数据
        CollegeEnrollmentPlanInfo enrollmentPlan1 = new CollegeEnrollmentPlanInfo();
        enrollmentPlan1.setSchoolName("北京大学");
        enrollmentPlan1.setCollegeMajorName("计算机科学与技术");
        enrollmentPlan1.setEnrollmentNumbers(30);
        enrollmentPlan1.setYear(2024);
        enrollmentPlan1.setProvinceName("北京");
        enrollmentPlan1.setBatchName("本科一批");
        enrollmentPlan1.setType("理科");
        enrollmentPlan1.setCourseSelectionRequirements("首选物理，再选化学");
        enrollmentPlan1.setClassOne("工学");
        enrollmentPlan1.setClassTwo("计算机类");

        CollegeEnrollmentPlanInfo enrollmentPlan2 = new CollegeEnrollmentPlanInfo();
        enrollmentPlan2.setSchoolName("北京大学");
        enrollmentPlan2.setCollegeMajorName("软件工程");
        enrollmentPlan2.setEnrollmentNumbers(25);
        enrollmentPlan2.setYear(2024);
        enrollmentPlan2.setProvinceName("北京");
        enrollmentPlan2.setBatchName("本科一批");
        enrollmentPlan2.setType("理科");
        enrollmentPlan2.setCourseSelectionRequirements("首选物理，再选化学/生物(2选1)");
        enrollmentPlan2.setClassOne("工学");
        enrollmentPlan2.setClassTwo("计算机类");

        majorInfo.setEnrollmentPlanData(Arrays.asList(enrollmentPlan1, enrollmentPlan2));

        // 展示数据结构
        System.out.println("专业基本信息:");
        System.out.println("  学校: " + majorInfo.getSchoolName());
        System.out.println("  专业: " + majorInfo.getMajorName());
        System.out.println("  最低分: " + majorInfo.getLowestScore());
        System.out.println("  批次: " + majorInfo.getBatchName());
        System.out.println();

        System.out.println("历史录取数据:");
        if (majorInfo.getHistoricalData() != null) {
            for (MajorAdmissionInfo.HistoricalYearData data : majorInfo.getHistoricalData()) {
                System.out.println("  " + data.getYear() + "年: 最低分=" + data.getLowestScore() + 
                                 ", 位次=" + data.getLowestSection());
            }
        }
        System.out.println();

        System.out.println("招生计划数据:");
        if (majorInfo.getEnrollmentPlanData() != null) {
            for (CollegeEnrollmentPlanInfo plan : majorInfo.getEnrollmentPlanData()) {
                System.out.println("  专业: " + plan.getCollegeMajorName());
                System.out.println("    招生人数: " + plan.getEnrollmentNumbers());
                System.out.println("    选科要求: " + plan.getCourseSelectionRequirements());
                System.out.println("    专业大类: " + plan.getClassOne());
                System.out.println("    专业小类: " + plan.getClassTwo());
                System.out.println();
            }
        }
    }

    /**
     * 展示API调用的好处
     */
    public static void demonstrateBenefits() {
        System.out.println("=== 增强功能的优势 ===");
        System.out.println("1. 完整信息: 每个推荐专业都包含历史录取数据和最新招生计划");
        System.out.println("2. 招生人数: 用户可以了解每个专业的具体招生人数");
        System.out.println("3. 选科匹配: 详细的选科要求帮助用户确认是否符合条件");
        System.out.println("4. 专业分类: 提供专业大类和小类信息，便于理解专业归属");
        System.out.println("5. 实时数据: 获取最新的招生计划信息，确保数据时效性");
        System.out.println("6. 决策支持: 综合历史趋势和当年计划，提供更好的决策依据");
        System.out.println();
        
        System.out.println("=== 使用场景 ===");
        System.out.println("• 志愿填报: 了解目标专业的招生人数和竞争情况");
        System.out.println("• 专业选择: 比较不同专业的招生规模和要求");
        System.out.println("• 风险评估: 结合历史数据和招生计划评估录取概率");
        System.out.println("• 备选方案: 根据招生人数制定多层次的志愿方案");
    }

    public static void main(String[] args) {
        demonstrateEnhancedRecommendation();
        System.out.println();
        demonstrateBenefits();
    }
}
