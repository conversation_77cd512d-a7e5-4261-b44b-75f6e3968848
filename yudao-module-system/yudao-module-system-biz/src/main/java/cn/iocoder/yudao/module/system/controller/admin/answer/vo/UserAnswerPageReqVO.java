package cn.iocoder.yudao.module.system.controller.admin.answer.vo;

import lombok.*;
import java.util.*;
import io.swagger.v3.oas.annotations.media.Schema;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 用户答案分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class UserAnswerPageReqVO extends PageParam {

    @Schema(description = "用户Id", example = "32599")
    private Integer userId;

    @Schema(description = "问题id", example = "17396")
    private Integer questionId;

    @Schema(description = "用户手写内容")
    private String writeContent;

    @Schema(description = "用户所选择答案Id集合，用逗号连接")
    private String answerChoices;

    @Schema(description = "用户第多少次答题")
    private Integer answerNo;

    @Schema(description = "选项创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}