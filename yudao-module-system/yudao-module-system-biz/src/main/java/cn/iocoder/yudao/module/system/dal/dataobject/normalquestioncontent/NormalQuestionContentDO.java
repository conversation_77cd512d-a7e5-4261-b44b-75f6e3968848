package cn.iocoder.yudao.module.system.dal.dataobject.normalquestioncontent;

import lombok.*;
import java.util.*;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.*;
import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;

/**
 * AI 报告 DO
 *
 * <AUTHOR>
 */
@TableName("ai_normal_question_content")
@KeySequence("ai_normal_question_content_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class NormalQuestionContentDO extends BaseDO {

    /**
     * 主键id
     */
    @TableId
    private Long id;
    /**
     * 关联id
     */
    private Integer questionId;
    /**
     * 内容
     */
    private String content;
    /**
     * 子类型
     */
    private String type;
    /**
     * 类型 1- 行业趋势 2-志愿100问 3-热门专业 4-慎选专业
     */
    private String parentType;
    /**
     * 问题
     */
    private String question;
    /**
     * 版本
     */
    private Integer version;
    /**
     * 星星
     */
    private Integer star;
    /**
     * 指令
     */
    private String ds;
    /**
     * 定制化指令
     */
    private String dsCus;

}