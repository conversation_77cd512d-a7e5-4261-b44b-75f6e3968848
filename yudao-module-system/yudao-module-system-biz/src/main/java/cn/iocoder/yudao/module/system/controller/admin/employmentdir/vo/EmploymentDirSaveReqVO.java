package cn.iocoder.yudao.module.system.controller.admin.employmentdir.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import javax.validation.constraints.*;

@Schema(description = "管理后台 - 就业方向新增/修改 Request VO")
@Data
public class EmploymentDirSaveReqVO {

    @Schema(description = "主键id", requiredMode = Schema.RequiredMode.REQUIRED, example = "4702")
    private Long id;

    @Schema(description = "内容", example = "李四")
    private String name;

    @Schema(description = "对口院校名称")
    private String schools;

    @Schema(description = "对口院校ids")
    private String schoolIds;

    @Schema(description = "状态", example = "1")
    private Integer status;

    @Schema(description = "排序")
    private Integer sort;

}