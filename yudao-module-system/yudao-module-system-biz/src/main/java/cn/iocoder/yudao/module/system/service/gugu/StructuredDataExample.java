package cn.iocoder.yudao.module.system.service.gugu;

import cn.iocoder.yudao.module.system.controller.admin.gugu.gugu.UserProfileInfo;
import cn.iocoder.yudao.module.system.controller.admin.gugu.vo.StructuredUserDataReqVO;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Arrays;

/**
 * 结构化数据处理示例
 * 展示如何使用新的结构化问答数据格式
 */
public class StructuredDataExample {

    /**
     * 示例：创建结构化用户数据
     */
    public static StructuredUserDataReqVO createExampleStructuredData() {
        StructuredUserDataReqVO reqVO = new StructuredUserDataReqVO();
        Map<String, StructuredUserDataReqVO.QuestionAnswer> userAnswers = new HashMap<>();

        // 1. 学生所处的高考省份
        StructuredUserDataReqVO.QuestionAnswer provinceAnswer = new StructuredUserDataReqVO.QuestionAnswer();
        provinceAnswer.setQuestionContent("学生所处的高考省份");
        provinceAnswer.setAnswer("辽宁");
        userAnswers.put("1", provinceAnswer);

        // 2. 学生性别
        StructuredUserDataReqVO.QuestionAnswer genderAnswer = new StructuredUserDataReqVO.QuestionAnswer();
        genderAnswer.setQuestionContent("学生性别");
        genderAnswer.setAnswer("我是女生");
        userAnswers.put("2", genderAnswer);

        // 3. 学生选科
        StructuredUserDataReqVO.QuestionAnswer subjectsAnswer = new StructuredUserDataReqVO.QuestionAnswer();
        subjectsAnswer.setQuestionContent("学生选科");
        subjectsAnswer.setAnswer(Arrays.asList("历史", "生物", "政治"));
        userAnswers.put("3", subjectsAnswer);

        // 5. 高考总分
        StructuredUserDataReqVO.QuestionAnswer totalScoreAnswer = new StructuredUserDataReqVO.QuestionAnswer();
        totalScoreAnswer.setQuestionContent("高考总分");
        totalScoreAnswer.setAnswer("538");
        userAnswers.put("5", totalScoreAnswer);

        // 6. 各科分数
        StructuredUserDataReqVO.QuestionAnswer subjectScoresAnswer = new StructuredUserDataReqVO.QuestionAnswer();
        subjectScoresAnswer.setQuestionContent("各科分数");
        subjectScoresAnswer.setAnswer("语文:88,数学:99,英语:88,历史:99,生物:99,政治:65");
        userAnswers.put("6", subjectScoresAnswer);

        // 7. 意向专业
        StructuredUserDataReqVO.QuestionAnswer interestedMajorsAnswer = new StructuredUserDataReqVO.QuestionAnswer();
        interestedMajorsAnswer.setQuestionContent("意向专业");
        interestedMajorsAnswer.setAnswer(Arrays.asList("护理学"));
        userAnswers.put("7", interestedMajorsAnswer);

        // 8. 性格
        StructuredUserDataReqVO.QuestionAnswer personalityAnswer = new StructuredUserDataReqVO.QuestionAnswer();
        personalityAnswer.setQuestionContent("性格");
        personalityAnswer.setAnswer("内向");
        userAnswers.put("8", personalityAnswer);

        // 9. 学习能力
        StructuredUserDataReqVO.QuestionAnswer learningAbilityAnswer = new StructuredUserDataReqVO.QuestionAnswer();
        learningAbilityAnswer.setQuestionContent("学习能力");
        learningAbilityAnswer.setAnswer("强");
        userAnswers.put("9", learningAbilityAnswer);

        // 10. 社交能力
        StructuredUserDataReqVO.QuestionAnswer socialAbilityAnswer = new StructuredUserDataReqVO.QuestionAnswer();
        socialAbilityAnswer.setQuestionContent("社交能力");
        socialAbilityAnswer.setAnswer("弱");
        userAnswers.put("10", socialAbilityAnswer);

        // 11. 家庭年收入
        StructuredUserDataReqVO.QuestionAnswer incomeAnswer = new StructuredUserDataReqVO.QuestionAnswer();
        incomeAnswer.setQuestionContent("家庭年收入（单位元）");
        incomeAnswer.setAnswer("10万到20万");
        userAnswers.put("11", incomeAnswer);

        // 12. 就业方向
        StructuredUserDataReqVO.QuestionAnswer careerAnswer = new StructuredUserDataReqVO.QuestionAnswer();
        careerAnswer.setQuestionContent("就业方向");
        careerAnswer.setAnswer("体制内");
        userAnswers.put("12", careerAnswer);

        // 14. 毕业去向
        StructuredUserDataReqVO.QuestionAnswer graduationAnswer = new StructuredUserDataReqVO.QuestionAnswer();
        graduationAnswer.setQuestionContent("毕业去向");
        graduationAnswer.setAnswer("就业");
        userAnswers.put("14", graduationAnswer);

        // 15. 就读城市省份
        StructuredUserDataReqVO.QuestionAnswer locationAnswer = new StructuredUserDataReqVO.QuestionAnswer();
        locationAnswer.setQuestionContent("就读城市省份");
        locationAnswer.setAnswer("全国");
        userAnswers.put("15", locationAnswer);

        reqVO.setUserAnswers(userAnswers);
        return reqVO;
    }

    /**
     * 示例：展示解析后的用户信息
     */
    public static void demonstrateUserProfileExtraction() {
        System.out.println("=== 结构化数据解析示例 ===");
        
        // 创建示例数据
        StructuredUserDataReqVO reqVO = createExampleStructuredData();
        
        // 注意：这里只是示例代码，实际使用时需要注入UserProfileService
        // UserProfileService userProfileService = ...; // 通过Spring注入获取
        // UserProfileInfo profile = userProfileService.extractUserProfileFromStructuredData(reqVO.getUserAnswers());
        
        // 模拟解析结果
        UserProfileInfo profile = new UserProfileInfo();
        profile.setProvince("辽宁");
        profile.setYear(2024);
        profile.setGender("女");
        profile.setSubjects(Arrays.asList("历史", "生物", "政治"));
        profile.setTotalScore(538);
        profile.setInterestedMajorCategories(Arrays.asList("护理学"));
        profile.setCareerDirection("体制内");
        profile.setGraduationPlan("就业");
        profile.setTypeName("历史类");
        
        System.out.println("解析后的用户信息:");
        System.out.println("  省份: " + profile.getProvince());
        System.out.println("  年份: " + profile.getYear());
        System.out.println("  性别: " + profile.getGender());
        System.out.println("  选科: " + String.join("、", profile.getSubjects()));
        System.out.println("  总分: " + profile.getTotalScore());
        System.out.println("  意向专业: " + String.join("、", profile.getInterestedMajorCategories()));
        System.out.println("  就业方向: " + profile.getCareerDirection());
        System.out.println("  毕业去向: " + profile.getGraduationPlan());
        System.out.println("  用户类型: " + profile.getTypeName());
    }

    /**
     * 示例：JSON格式的请求数据
     */
    public static void demonstrateJsonFormat() {
        System.out.println("\n=== JSON 请求格式示例 ===");
        System.out.println("POST /system/metadata/ceemajor/all-suitable");
        System.out.println("Content-Type: application/json");
        System.out.println();
        System.out.println("{");
        System.out.println("  \"userAnswers\": {");
        System.out.println("    \"1\": {");
        System.out.println("      \"questionContent\": \"学生所处的高考省份\",");
        System.out.println("      \"answer\": \"辽宁\"");
        System.out.println("    },");
        System.out.println("    \"2\": {");
        System.out.println("      \"questionContent\": \"学生性别\",");
        System.out.println("      \"answer\": \"我是女生\"");
        System.out.println("    },");
        System.out.println("    \"3\": {");
        System.out.println("      \"questionContent\": \"学生选科\",");
        System.out.println("      \"answer\": [\"历史\", \"生物\", \"政治\"]");
        System.out.println("    },");
        System.out.println("    \"5\": {");
        System.out.println("      \"questionContent\": \"高考总分\",");
        System.out.println("      \"answer\": \"538\"");
        System.out.println("    },");
        System.out.println("    \"7\": {");
        System.out.println("      \"questionContent\": \"意向专业\",");
        System.out.println("      \"answer\": [\"护理学\"]");
        System.out.println("    }");
        System.out.println("    // ... 其他问答数据");
        System.out.println("  }");
        System.out.println("}");
    }

    public static void main(String[] args) {
        demonstrateUserProfileExtraction();
        demonstrateJsonFormat();
    }
}
