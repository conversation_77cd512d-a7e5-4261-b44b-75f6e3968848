package cn.iocoder.yudao.module.system.controller.admin.gugu.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 大学高校专业数据 Response VO
 */
@Schema(description = "管理后台 - 大学高校专业数据 Response VO")
@Data
public class CeeMajorRespVO {

    @Schema(description = "专业ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "1024")
    private Long id;

    @Schema(description = "教育级别", example = "本科")
    private String educationLevel;

    @Schema(description = "学科门类", example = "工学")
    private String disciplinaryCategory;

    @Schema(description = "学科子类", example = "计算机类")
    private String disciplinarySubCategory;

    @Schema(description = "专业代码", example = "080901")
    private String majorCode;

    @Schema(description = "专业名称", example = "计算机科学与技术")
    private String majorName;

    @Schema(description = "专业介绍")
    private String majorIntroduction;

    @Schema(description = "开设课程")
    private List<Course> courses;

    @Schema(description = "毕业规模", example = "大型")
    private String graduateScale;

    @Schema(description = "男女比例", example = "7:3")
    private String maleFemaleRatio;

    @Schema(description = "推荐院校")
    private List<String> recommendSchools;

    @Schema(description = "是否推荐专业")
    private Boolean isRecommended;

    @Schema(description = "就业方向")
    private String careerDirection;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    /**
     * 课程信息
     */
    @Data
    public static class Course {
        @Schema(description = "课程名称", example = "数据结构")
        private String courseName;

        @Schema(description = "课程难度", example = "中等")
        private String courseDifficulty;
    }
}
