package cn.iocoder.yudao.module.system.controller.admin.gugu.gugu;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * 用户个人信息实体类
 */
@Schema(description = "用户个人信息")
@Data
public class UserProfileInfo {

    @Schema(description = "省份")
    private String province;

    @Schema(description = "年份")
    private Integer year;

    @Schema(description = "性别")
    private String gender;

    @Schema(description = "选科")
    private List<String> subjects;

    @Schema(description = "总分")
    private Integer totalScore;

    @Schema(description = "各科分数")
    private Map<String, Integer> subjectScores;

    @Schema(description = "性格特点")
    private List<String> personalityTraits;

    @Schema(description = "家庭年收入范围")
    private String familyIncome;

    @Schema(description = "毕业去向")
    private String graduationPlan;

    @Schema(description = "感兴趣的专业类别")
    private List<String> interestedMajorCategories;

    @Schema(description = "期望学校所在地")
    private String preferredLocation;

    @Schema(description = "就业方向")
    private String careerDirection;

    @Schema(description = "用户类型，如物理类、历史类等")
    private String typeName;
}
