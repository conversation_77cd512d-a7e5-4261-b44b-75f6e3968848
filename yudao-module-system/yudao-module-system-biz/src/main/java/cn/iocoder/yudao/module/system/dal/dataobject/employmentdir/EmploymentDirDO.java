package cn.iocoder.yudao.module.system.dal.dataobject.employmentdir;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.*;
import java.util.*;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.*;
import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;

/**
 * 就业方向 DO
 *
 * <AUTHOR>
 */
@TableName("kf_employment_dir")
@KeySequence("kf_employment_dir_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class EmploymentDirDO extends BaseDO {

    /**
     * 主键id
     */
    @TableId
    private Long id;
    /**
     * 内容
     */
    private String name;
    /**
     * 对口院校名称
     */
    private String schools;

    /**
     * 对口院校ids
     */
    private String schoolIds;

    /**
     * 状态
     */
    private Integer status;
    /**
     * 排序
     */
    private Integer sort;

}