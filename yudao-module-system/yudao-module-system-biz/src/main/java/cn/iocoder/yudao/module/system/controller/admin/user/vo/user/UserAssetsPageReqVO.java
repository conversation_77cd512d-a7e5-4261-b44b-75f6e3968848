package cn.iocoder.yudao.module.system.controller.admin.user.vo.user;

import lombok.*;
import io.swagger.v3.oas.annotations.media.Schema;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 用户资源分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class UserAssetsPageReqVO extends PageParam {

    @Schema(description = "用户id", example = "13201")
    private Long userId;

    @Schema(description = "体验版次数", example = "8522")
    private Integer trailCount;

    @Schema(description = "体验版剩余次数", example = "1251")
    private Integer trailLeftCount;

    @Schema(description = "专业版次数", example = "10314")
    private Integer psCount;

    @Schema(description = "专业版剩余次数", example = "21260")
    private Integer psLeftCount;

    @Schema(description = "内容查看次数", example = "23296")
    private Integer contentCount;

    @Schema(description = "内容查看剩余次数", example = "18759")
    private Integer contentLeftCount;

    @Schema(description = "问答次数", example = "3143")
    private Integer askCount;

    @Schema(description = "问答剩余次数", example = "17518")
    private Integer askLeftCount;

    @Schema(description = "体验版最新购买时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] trailUpdateTime;

    @Schema(description = "专业版最新购买时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] psUpdateTime;

    @Schema(description = "内容会员到期时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] contentEndTime;

    @Schema(description = "内容会员开始时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] contentStartTime;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}