package cn.iocoder.yudao.module.system.controller.admin.gugu.gugu;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * 大学高校专业数据实体类
 */
@Schema(description = "大学高校专业数据")
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class CeeMajorInfo {

    @JsonProperty("EducationLevel")
    private String educationLevel;

    @JsonProperty("DisciplinaryCategory")
    private String disciplinaryCategory;

    @JsonProperty("DisciplinarySubCategory")
    private String disciplinarySubCategory;

    @JsonProperty("MajorCode")
    private String majorCode;

    @JsonProperty("MajorName")
    private String majorName;

    @JsonProperty("MajorIntroduction")
    private String majorIntroduction;

    @JsonProperty("Courses")
    private List<Course> courses;

    @JsonProperty("GraduateScale")
    private String graduateScale;

    @JsonProperty("MaleFemaleRatio")
    private String maleFemaleRatio;

    @JsonProperty("RecommendSchools")
    private List<String> recommendSchools;

    /**
     * 课程信息
     */
    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class Course {
        @JsonProperty("CourseName")
        private String courseName;

        @JsonProperty("CourseDifficulty")
        private String courseDifficulty;
    }
}
