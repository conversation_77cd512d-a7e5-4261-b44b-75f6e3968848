package cn.iocoder.yudao.module.system.controller.admin.useranalysis.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;
import com.alibaba.excel.annotation.*;

@Schema(description = "管理后台 - 用户分析 Response VO")
@Data
@ExcelIgnoreUnannotated
public class UserAnalysisRespVO {

    @Schema(description = "主键id,即分析Id", requiredMode = Schema.RequiredMode.REQUIRED, example = "17306")
    @ExcelProperty("主键id,即分析Id")
    private Integer id;

    @Schema(description = "用户Id", requiredMode = Schema.RequiredMode.REQUIRED, example = "8553")
    @ExcelProperty("用户Id")
    private Integer userId;

    @Schema(description = "答题次序号", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("答题次序号")
    private Integer answerNo;

    @Schema(description = "问题内容", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("问题内容")
    private String analysisContent;

    @Schema(description = "选项创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("选项创建时间")
    private LocalDateTime createTime;

}