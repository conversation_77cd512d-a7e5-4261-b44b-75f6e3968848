package cn.iocoder.yudao.module.system.controller.admin.gugu.gugu;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * 用户排名查询响应 VO
 */
@Schema(description = "管理后台 - 用户排名查询响应 VO")
@Data
public class UserRankingRespVO {

    @Schema(description = "排名", example = "673")
    private String ranking;

    @Schema(description = "排名范围", example = "1-21")
    private String rankingRange;

    @Schema(description = "总考生数量", example = "25")
    private Integer totalCandidates;

    @Schema(description = "录取批次名称", example = "本科批")
    private String admissionBatchName;

    @Schema(description = "最低录取分数", example = "462")
    private String minimumAdmissionScore;

    @Schema(description = "历史分数记录")
    private List<ExaminationResult.HistoricalScore> historicalScores;
}
