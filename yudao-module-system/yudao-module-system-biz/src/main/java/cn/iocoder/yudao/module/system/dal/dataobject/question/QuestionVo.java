package cn.iocoder.yudao.module.system.dal.dataobject.question;

import lombok.Data;

import java.util.List;

/**
 * @date 2025年03月30日 下午 04:06
 */
@Data
public class QuestionVo {
    /**
     * 主键id
     */

    private Integer id;
    /**
     * 问题内容
     */
    private String content;
    /**
     * 问题类型 1单选题 2多选题 3问答题
     * <p>
     */
    private Integer type;
    /**
     * 是否必答题 1必答 0 可不回答
     * <p>
     */
    private Integer isNecessary;
    /**
     * 状态 1可用 0 不可用
     * <p>
     */
    private Integer status;
    /**
     * 问题顺序
     */
    private Integer sort;
    /**
     * 问题的选项 填空题没有
     */
    private List<QuestionChoiceDO> questionChoiceDoS;
//    /**
//     * 用户是否已回答了问题
//     */
//    private Integer isAnswered;
//    /**
//     * 用户选择的选项Id集合
//     */
//    private List<Integer> choosedIds;
//    /**
//     * 用户问答题手写内容
//     */
//    private String writeContent;
//    /**
//     * 是否是最后一题
//     */
//    private Integer isLastQuestion;
}
