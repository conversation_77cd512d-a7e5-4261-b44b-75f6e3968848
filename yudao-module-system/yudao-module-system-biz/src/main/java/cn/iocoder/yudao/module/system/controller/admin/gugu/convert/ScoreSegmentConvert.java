package cn.iocoder.yudao.module.system.controller.admin.gugu.convert;

import cn.iocoder.yudao.framework.common.util.json.JsonUtils;
import cn.iocoder.yudao.module.system.controller.admin.gugu.gugu.ExaminationResult;
import cn.iocoder.yudao.module.system.controller.admin.gugu.vo.ScoreSegmentRespVO;
import cn.iocoder.yudao.module.system.dal.dataobject.gugu.ScoreSegmentDO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 高考一分一段数据 Convert
 */
@Mapper
public interface ScoreSegmentConvert {

    ScoreSegmentConvert INSTANCE = Mappers.getMapper(ScoreSegmentConvert.class);

    ScoreSegmentRespVO convert(ScoreSegmentDO bean);

    List<ScoreSegmentRespVO> convertList(List<ScoreSegmentDO> list);

    @Mapping(source = "historicalScores", target = "historicalScores", qualifiedByName = "historicalScoresToString")
    ScoreSegmentDO convert(ExaminationResult bean);

    default List<ScoreSegmentDO> convertListFromExaminationResult(List<ExaminationResult> list) {
        if (list == null) {
            return null;
        }
        List<ScoreSegmentDO> result = new java.util.ArrayList<>(list.size());
        for (ExaminationResult examinationResult : list) {
            result.add(convert(examinationResult));
        }
        return result;
    }

    /**
     * 将历史分数列表转换为JSON字符串
     *
     * @param historicalScores 历史分数列表
     * @return JSON字符串
     */
    @Named("historicalScoresToString")
    default String historicalScoresToString(List<ExaminationResult.HistoricalScore> historicalScores) {
        if (historicalScores == null || historicalScores.isEmpty()) {
            return null;
        }
        return JsonUtils.toJsonString(historicalScores);
    }
}
