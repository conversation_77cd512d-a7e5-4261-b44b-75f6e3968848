package cn.iocoder.yudao.module.system.service.answer;

import cn.iocoder.yudao.module.system.dal.dataobject.answer.UserAnswerDO;
import cn.iocoder.yudao.module.system.dal.mysql.answer.UserAnswerMapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import org.springframework.stereotype.Service;

import org.springframework.validation.annotation.Validated;


import cn.iocoder.yudao.module.system.controller.admin.answer.vo.*;
import cn.iocoder.yudao.framework.common.pojo.PageResult;

import cn.iocoder.yudao.framework.common.util.object.BeanUtils;

import javax.annotation.Resource;

import java.util.List;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.module.system.enums.ErrorCodeConstants.*;

/**
 * 用户答案 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class UserAnswerServiceImpl implements UserAnswerService {

    @Resource
    private UserAnswerMapper userAnswerMapper;

    @Override
    public Integer createUserAnswer(UserAnswerSaveReqVO createReqVO) {
        // 插入
        UserAnswerDO userAnswer = BeanUtils.toBean(createReqVO, UserAnswerDO.class);
        userAnswerMapper.insert(userAnswer);
        // 返回
        return userAnswer.getId();
    }

    @Override
    public void updateUserAnswer(UserAnswerSaveReqVO updateReqVO) {
        // 校验存在
        validateUserAnswerExists(updateReqVO.getId());
        // 更新
        UserAnswerDO updateObj = BeanUtils.toBean(updateReqVO, UserAnswerDO.class);
        userAnswerMapper.updateById(updateObj);
    }

    @Override
    public void deleteUserAnswer(Integer id) {
        // 校验存在
        validateUserAnswerExists(id);
        // 删除
        userAnswerMapper.deleteById(id);
    }

    private void validateUserAnswerExists(Integer id) {
        if (userAnswerMapper.selectById(id) == null) {
            throw exception(USER_ANSWER_NOT_EXISTS);
        }
    }

    @Override
    public UserAnswerDO getUserAnswer(Integer id) {
        return userAnswerMapper.selectById(id);
    }

    @Override
    public PageResult<UserAnswerDO> getUserAnswerPage(UserAnswerPageReqVO pageReqVO) {
        return userAnswerMapper.selectPage(pageReqVO);
    }

    /**
     * 查询用户最后一题的答题记录
     *
     * @param answerNo
     * @return
     */
    @Override
    public UserAnswerDO getUserLastAnswer(Integer answerNo) {
        QueryWrapper<UserAnswerDO> wrapper = new QueryWrapper<>();
        wrapper.eq("answerNo", answerNo)
                .orderByDesc("create_time")
                .last("limit 1");
        return userAnswerMapper.selectOne(wrapper);
    }

    @Override
    public UserAnswerDO getOldUserAnswer(Integer questionId, Integer answerNo, Integer userId) {
        QueryWrapper<UserAnswerDO> wrapper = new QueryWrapper<>();
        wrapper.eq("question_id", questionId)
                .eq("user_id", userId)
                .eq("answer_no", answerNo);
        UserAnswerDO userAnswerDO = userAnswerMapper.selectOne(wrapper);
        System.out.println("查询到的结果: " + userAnswerDO);
        return userAnswerDO;
    }

    /**
     * 查询当前最大的答题编号
     *
     * @param userId
     * @return
     */
    @Override
    public Integer getBiggestAnswerNo(Integer userId) {
        QueryWrapper<UserAnswerDO> wrapper = new QueryWrapper<>();
        wrapper.eq("user_id", userId);
        wrapper.orderByDesc("answer_no").last("limit 1");
        UserAnswerDO userAnswerDO = userAnswerMapper.selectOne(wrapper);
        if (userAnswerDO == null) {
            return 0;
        }
        return userAnswerDO.getAnswerNo();
    }

    @Override
    public List<UserAnswerDO> getAnswerByUserIdAndAnswerNo(Integer userId, Integer answerNo) {
        QueryWrapper<UserAnswerDO> wrapper = new QueryWrapper<>();
        wrapper.eq("user_id", userId).eq("answer_no", answerNo);
        return userAnswerMapper.selectList(wrapper);
    }

    @Override
    public UserAnswerDO getAnswerByUserIdAndAnswerNoAndQuestionId(Integer userId, Integer answerNo, Integer questionId) {
        QueryWrapper<UserAnswerDO> wrapper = new QueryWrapper<>();
        wrapper.eq("user_id",userId).eq("answer_no",answerNo).eq("question_id",questionId);
        return userAnswerMapper.selectOne(wrapper);
    }


}