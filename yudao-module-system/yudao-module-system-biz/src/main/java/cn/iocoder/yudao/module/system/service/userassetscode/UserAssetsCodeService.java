package cn.iocoder.yudao.module.system.service.userassetscode;

import java.util.*;
import javax.validation.*;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.system.controller.admin.userassetscode.vo.UserAssetsCodePageReqVO;
import cn.iocoder.yudao.module.system.controller.admin.userassetscode.vo.UserAssetsCodeSaveReqVO;
import cn.iocoder.yudao.module.system.dal.dataobject.userassetscode.UserAssetsCodeDO;

/**
 * 用户资源兑换 Service 接口
 *
 * <AUTHOR>
 */
public interface UserAssetsCodeService {

    /**
     * 创建用户资源兑换
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Integer createUserAssetsCode(@Valid UserAssetsCodeSaveReqVO createReqVO);

    /**
     * 更新用户资源兑换
     *
     * @param updateReqVO 更新信息
     */
    void updateUserAssetsCode(@Valid UserAssetsCodeSaveReqVO updateReqVO);

    /**
     * 删除用户资源兑换
     *
     * @param id 编号
     */
    void deleteUserAssetsCode(Integer id);

    /**
     * 获得用户资源兑换
     *
     * @param id 编号
     * @return 用户资源兑换
     */
    UserAssetsCodeDO getUserAssetsCode(Integer id);

    /**
     * 获得用户资源兑换分页
     *
     * @param pageReqVO 分页查询
     * @return 用户资源兑换分页
     */
    PageResult<UserAssetsCodeDO> getUserAssetsCodePage(UserAssetsCodePageReqVO pageReqVO);

    /**
     * 兑换用户资源码
     *
     * @param code 兑换码
     * @param userId 用户ID
     * @return 兑换的资源信息
     */
    UserAssetsCodeDO redeemUserAssetsCode(String code,Long userId);
}