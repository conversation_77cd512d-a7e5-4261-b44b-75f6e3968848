package cn.iocoder.yudao.module.system.dal.dataobject.question;

import lombok.*;

import com.baomidou.mybatisplus.annotation.*;
import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;

/**
 * 问题选项 DO
 *
 * <AUTHOR>
 */
@TableName("tb_question_choice")
@KeySequence("tb_question_choice_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class QuestionChoiceDO extends BaseDO {

    /**
     * 主键id,即选项Id
     */
    @TableId
    private Integer id;
    /**
     * 问题id
     */
    private Integer questionId;
    /**
     * 选项内容
     */
    private String choiceContent;
    /**
     * 选项顺序
     */
    private Integer sort;
    /**
     * 选项类型 1文字选项 2图文选项
     */
    private Integer type;
    /**
     * 图片地址
     */
    private String imageUrl;

}