package cn.iocoder.yudao.module.system.dal.dataobject.ai;

import lombok.*;
import com.baomidou.mybatisplus.annotation.*;
import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;

/**
 * AI 报告 DO
 *
 * <AUTHOR>
 */
@TableName("ai_user_report")
@KeySequence("ai_user_report_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UserReportDO extends BaseDO {

    /**
     * 主键id
     */
    @TableId
    private Long id;
    /**
     * 用户id
     */
    private Long userId;
    /**
     * 内容
     */
    private String content;
    /**
     * 类型
     */
    private String analysisType;

    private String question;

    private Integer version;

    private Integer answerRecordId;

    /**
     * 报告名称
     */
    private String name;

}