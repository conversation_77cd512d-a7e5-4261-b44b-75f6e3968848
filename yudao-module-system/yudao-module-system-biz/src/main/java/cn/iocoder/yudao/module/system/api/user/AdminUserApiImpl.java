package cn.iocoder.yudao.module.system.api.user;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjUtil;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.framework.datapermission.core.util.DataPermissionUtils;
import cn.iocoder.yudao.module.system.api.user.dto.AdminUserRespDTO;
import cn.iocoder.yudao.module.system.controller.admin.user.vo.user.UserAssetsSaveReqVO;
import cn.iocoder.yudao.module.system.controller.admin.user.vo.user.UserSaveReqVO;
import cn.iocoder.yudao.module.system.dal.dataobject.dept.DeptDO;
import cn.iocoder.yudao.module.system.dal.dataobject.user.AdminUserDO;
import cn.iocoder.yudao.module.system.dal.dataobject.user.UserAssetsDO;
import cn.iocoder.yudao.module.system.service.dept.DeptService;
import cn.iocoder.yudao.module.system.service.user.AdminUserService;
import cn.iocoder.yudao.module.system.service.user.UserAssetsService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Set;

import static cn.iocoder.yudao.framework.common.util.collection.CollectionUtils.convertSet;

/**
 * Admin 用户 API 实现类
 *
 * <AUTHOR>
 */
@Service
public class AdminUserApiImpl implements AdminUserApi {

    @Resource
    private AdminUserService userService;
    @Resource
    private DeptService deptService;

    @Resource
    private UserAssetsService assetsService;

    @Override
    public AdminUserRespDTO getUser(Long id) {
        AdminUserDO user = userService.getUser(id);
        return BeanUtils.toBean(user, AdminUserRespDTO.class);
    }

    @Override
    public List<AdminUserRespDTO> getUserListBySubordinate(Long id) {
        // 1.1 获取用户负责的部门
        List<DeptDO> depts = deptService.getDeptListByLeaderUserId(id);
        if (CollUtil.isEmpty(depts)) {
            return Collections.emptyList();
        }
        // 1.2 获取所有子部门
        Set<Long> deptIds = convertSet(depts, DeptDO::getId);
        List<DeptDO> childDeptList = deptService.getChildDeptList(deptIds);
        if (CollUtil.isNotEmpty(childDeptList)) {
            deptIds.addAll(convertSet(childDeptList, DeptDO::getId));
        }

        // 2. 获取部门对应的用户信息
        List<AdminUserDO> users = userService.getUserListByDeptIds(deptIds);
        users.removeIf(item -> ObjUtil.equal(item.getId(), id)); // 排除自己
        return BeanUtils.toBean(users, AdminUserRespDTO.class);
    }

    @Override
    public List<AdminUserRespDTO> getUserList(Collection<Long> ids) {
        return DataPermissionUtils.executeIgnore(() -> { // 禁用数据权限。原因是，一般基于指定 id 的 API 查询，都是数据拼接为主
            List<AdminUserDO> users = userService.getUserList(ids);
            return BeanUtils.toBean(users, AdminUserRespDTO.class);
        });
    }

    @Override
    public List<AdminUserRespDTO> getUserListByDeptIds(Collection<Long> deptIds) {
        List<AdminUserDO> users = userService.getUserListByDeptIds(deptIds);
        return BeanUtils.toBean(users, AdminUserRespDTO.class);
    }

    @Override
    public List<AdminUserRespDTO> getUserListByPostIds(Collection<Long> postIds) {
        List<AdminUserDO> users = userService.getUserListByPostIds(postIds);
        return BeanUtils.toBean(users, AdminUserRespDTO.class);
    }

    @Override
    public void validateUserList(Collection<Long> ids) {
        userService.validateUserList(ids);
    }

    @Override
    public AdminUserRespDTO getUserByOpenId(String channelUserId) {
        AdminUserDO adminUserDO = userService.getUserByOpenId(channelUserId);

        return BeanUtils.toBean(adminUserDO, AdminUserRespDTO.class);
    }

    @Override
    public void updateUserAssets(String channelUserId, String subject) {
        AdminUserDO adminUserDO = userService.getUserByOpenId(channelUserId);
        if (adminUserDO != null) {
            UserAssetsDO userAssetsDO = assetsService.getUserAssetsByUserId(adminUserDO.getId());
            //TODO 暂时先用中文匹配 后面再改
            if (userAssetsDO != null) {
                switch (subject) {
                    case "升学规划报告基础版":
                        userAssetsDO.setTrailCount(userAssetsDO.getTrailCount() + 1);
                        userAssetsDO.setTrailLeftCount(userAssetsDO.getTrailLeftCount() + 1);
                        adminUserDO.setTestTotalTimes(adminUserDO.getTestTotalTimes() + 1);
                        adminUserDO.setTestLeftTimes(adminUserDO.getTestLeftTimes() + 1);
                        break;
                    case "升学规划报告专业版":
                        userAssetsDO.setPsCount(userAssetsDO.getPsCount() + 3);
                        userAssetsDO.setPsLeftCount(userAssetsDO.getPsLeftCount() + 3);
                        adminUserDO.setProTotalTimes(adminUserDO.getProTotalTimes() + 3);
                        adminUserDO.setProLeftTimes(adminUserDO.getProLeftTimes() + 3);
                        break;
                    case "问答资源包1次":
                        userAssetsDO.setAskCount(userAssetsDO.getAskCount() + 1);
                        userAssetsDO.setAskLeftCount(userAssetsDO.getAskLeftCount() + 1);
                        break;
                    case "问答资源包10次":
                        userAssetsDO.setAskCount(userAssetsDO.getAskCount() + 10);
                        userAssetsDO.setAskLeftCount(userAssetsDO.getAskLeftCount() + 10);
                        break;
                    case "问答资源包100次":
                        userAssetsDO.setAskCount(userAssetsDO.getAskCount() + 100);
                        userAssetsDO.setAskLeftCount(userAssetsDO.getAskLeftCount() + 100);
                        break;
                    case "问答资源包1000次":
                        userAssetsDO.setAskCount(userAssetsDO.getAskCount() + 1000);
                        userAssetsDO.setAskLeftCount(userAssetsDO.getAskLeftCount() + 1000);
                        break;

                    case "内容会员1个月":
                        userAssetsDO.setContentCount(userAssetsDO.getContentCount() + 9999);
                        userAssetsDO.setContentLeftCount(userAssetsDO.getContentLeftCount() + 9999);
                        userAssetsDO.setContentStartTime(LocalDateTime.now());
                        userAssetsDO.setContentEndTime(LocalDateTime.now().plusDays(30));
                        break;
                    case "内容会员100天":
                        userAssetsDO.setContentCount(userAssetsDO.getContentCount() + 9999);
                        userAssetsDO.setContentLeftCount(userAssetsDO.getContentLeftCount() + 9999);
                        userAssetsDO.setContentStartTime(LocalDateTime.now());
                        userAssetsDO.setContentEndTime(LocalDateTime.now().plusDays(100));
                        break;
                    case "内容会员1年":
                        userAssetsDO.setContentCount(userAssetsDO.getContentCount() + 9999);
                        userAssetsDO.setContentLeftCount(userAssetsDO.getContentLeftCount() + 9999);
                        userAssetsDO.setContentStartTime(LocalDateTime.now());
                        userAssetsDO.setContentEndTime(LocalDateTime.now().plusDays(365));
                        break;
                    case "内容会员3年":
                        userAssetsDO.setContentCount(userAssetsDO.getContentCount() + 9999);
                        userAssetsDO.setContentLeftCount(userAssetsDO.getContentLeftCount() + 9999);
                        userAssetsDO.setContentStartTime(LocalDateTime.now());
                        userAssetsDO.setContentEndTime(LocalDateTime.now().plusDays(3 * 365));
                        break;
                    default:
                        break;
                }
                assetsService.updateUserAssets(BeanUtils.toBean(userAssetsDO, UserAssetsSaveReqVO.class));
                userService.updateUser(BeanUtils.toBean(adminUserDO, UserSaveReqVO.class));
            }
        }
    }
}
