package cn.iocoder.yudao.module.system.controller.admin.gugu.gugu;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 历年高校招生计划信息实体类
 */
@Schema(description = "历年高校招生计划信息")
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class CollegeEnrollmentPlanInfo {

    /**
     * 学制年限
     */
    @Schema(description = "学制年限")
    @JsonProperty("InSchoolYears")
    private String inSchoolYears;

    /**
     * 专业大类
     */
    @Schema(description = "专业大类")
    @JsonProperty("ClassOne")
    private String classOne;

    /**
     * 专业小类
     */
    @Schema(description = "专业小类")
    @JsonProperty("ClassTwo")
    private String classTwo;

    /**
     * 录取批次
     */
    @Schema(description = "录取批次")
    @JsonProperty("BatchName")
    private String batchName;

    /**
     * 文理综合类别
     */
    @Schema(description = "文理综合类别")
    @JsonProperty("Type")
    private String type;

    /**
     * 高校名称
     */
    @Schema(description = "高校名称")
    @JsonProperty("SchoolName")
    private String schoolName;

    /**
     * 招生人数
     */
    @Schema(description = "招生人数")
    @JsonProperty("EnrollmentNumbers")
    private Integer enrollmentNumbers;

    /**
     * 咕咕数据平台高校唯一 ID
     */
    @Schema(description = "咕咕数据平台高校唯一 ID")
    @JsonProperty("SchoolUUID")
    private String schoolUuid;

    /**
     * 选科要求
     */
    @Schema(description = "选科要求")
    @JsonProperty("CourseSelectionRequirements")
    private String courseSelectionRequirements;

    /**
     * 高校专业名称
     */
    @Schema(description = "高校专业名称")
    @JsonProperty("CollegeMajorName")
    private String collegeMajorName;

    /**
     * 高校专业代码
     */
    @Schema(description = "高校专业代码")
    @JsonProperty("CollegeMajorCode")
    private String collegeMajorCode;

    /**
     * 招生年份
     */
    @Schema(description = "招生年份")
    @JsonProperty("Year")
    private Integer year;

    /**
     * 招生省份
     */
    @Schema(description = "招生省份")
    @JsonProperty("ProvinceName")
    private String provinceName;
}
