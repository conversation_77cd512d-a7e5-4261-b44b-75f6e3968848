package cn.iocoder.yudao.module.system.controller.admin.gugu.gugu;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * 高校基础信息实体类
 */
@Schema(description = "高校基础信息")
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class CollegeInfo {

    @JsonProperty("DataId")
    private String dataId;

    @JsonProperty("SchoolUUID")
    private String schoolUUID;

    @JsonProperty("CollegeName")
    private String collegeName;

    @JsonProperty("Province")
    private String province;

    @JsonProperty("City")
    private String city;

    @JsonProperty("District")
    private String district;

    @JsonProperty("Coordinate")
    private String coordinate;

    @JsonProperty("CollegeType")
    private String collegeType;

    @JsonProperty("Is985")
    private Boolean is985;

    @JsonProperty("Is211")
    private Boolean is211;

    @JsonProperty("IsDualClass")
    private Boolean isDualClass;

    @JsonProperty("CollegeCategory")
    private String collegeCategory;

    @JsonProperty("CollegeTags")
    private String collegeTags;

    /**
     * 获取解析后的学院标签列表
     * @return 学院标签列表
     */
    public List<String> getCollegeTagsList() {
        if (collegeTags == null || collegeTags.isEmpty() || collegeTags.equals("[]")) {
            return new ArrayList<>();
        }
        try {
            // 尝试解析JSON字符串
            com.fasterxml.jackson.databind.ObjectMapper mapper = new com.fasterxml.jackson.databind.ObjectMapper();
            // 处理字符串形式的JSON数组
            return mapper.readValue(collegeTags, mapper.getTypeFactory().constructCollectionType(List.class, String.class));
        } catch (Exception e) {
            // 如果解析失败，尝试将字符串分割并去除中括号
            if (collegeTags.startsWith("[") && collegeTags.endsWith("]")) {
                String content = collegeTags.substring(1, collegeTags.length() - 1);
                if (content.isEmpty()) {
                    return new ArrayList<>();
                }
                String[] tags = content.split(",");
                List<String> result = new ArrayList<>();
                for (String tag : tags) {
                    // 去除可能的引号
                    String trimmed = tag.trim();
                    if (trimmed.startsWith("\"") && trimmed.endsWith("\"")) {
                        trimmed = trimmed.substring(1, trimmed.length() - 1);
                    }
                    result.add(trimmed);
                }
                return result;
            }
            return new ArrayList<>();
        }
    }

    @JsonProperty("EduLevel")
    private String eduLevel;

    @JsonProperty("CollegeProperty")
    private String collegeProperty;

    @JsonProperty("CollegeCode")
    private String collegeCode;

    @JsonProperty("Ranking")
    private Integer ranking;

    @JsonProperty("RankingInCategory")
    private String rankingInCategory;

    @JsonProperty("WebSite")
    private String webSite;

    @JsonProperty("CallNumber")
    private String callNumber;

    @JsonProperty("Email")
    private String email;

    @JsonProperty("Address")
    private String address;

    @JsonProperty("BranchList")
    private List<Branch> branchList;

    /**
     * 分校区信息
     */
    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class Branch {
        @JsonProperty("BranchName")
        private String branchName;

        @JsonProperty("BranchAddress")
        private String branchAddress;
    }

    @JsonProperty("CoverImage")
    private String coverImage;

    @JsonProperty("Intro")
    private String intro;

    @JsonProperty("Expenses")
    private String expenses;

    @JsonProperty("OldName")
    private String oldName;

    @JsonProperty("ShortName")
    private String shortName;

    @JsonProperty("MajorList")
    private List<MajorCategory> majorList;

    @JsonProperty("IsDeleted")
    private Boolean isDeleted;

    /**
     * 专业大类
     */
    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class MajorCategory {
        @JsonProperty("MajorTitle")
        private String majorTitle;

        @JsonProperty("Majors")
        private List<String> majors;
    }
}
