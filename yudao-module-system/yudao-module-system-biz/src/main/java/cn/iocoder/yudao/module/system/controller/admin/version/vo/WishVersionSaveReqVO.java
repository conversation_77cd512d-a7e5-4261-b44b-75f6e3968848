package cn.iocoder.yudao.module.system.controller.admin.version.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;

import javax.validation.constraints.NotEmpty;

@Schema(description = "管理后台 - 志愿咨询题库版本表	新增/修改 Request VO")
@Data
public class WishVersionSaveReqVO {

    @Schema(description = "主键id", requiredMode = Schema.RequiredMode.REQUIRED, example = "32609")
    private Integer id;

    @Schema(description = "题库版本名", requiredMode = Schema.RequiredMode.REQUIRED, example = "张三")
    @NotEmpty(message = "题库版本名不能为空")
    private String name;

    @Schema(description = "该版本介绍", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "该版本介绍不能为空")
    private String content;

}