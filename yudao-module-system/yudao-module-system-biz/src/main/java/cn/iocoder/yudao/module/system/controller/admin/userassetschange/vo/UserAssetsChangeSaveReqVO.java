package cn.iocoder.yudao.module.system.controller.admin.userassetschange.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import javax.validation.constraints.*;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;

@Schema(description = "管理后台 - 用户资源兑换套餐新增/修改 Request VO")
@Data
public class UserAssetsChangeSaveReqVO {

    @Schema(description = "主键id", requiredMode = Schema.RequiredMode.REQUIRED, example = "7273")
    private Integer id;

    @Schema(description = "客户id", example = "12017")
    private Long customerId;

    @Schema(description = "体验版次数", example = "16563")
    private Integer trailCount;

    @Schema(description = "客户名称", example = "王五")
    private String customerName;

    @Schema(description = "内容天数", example = "1")
    private Integer contentDays;

    @Schema(description = "兑换套餐名称", example = "王五")
    private String changeName;

    @Schema(description = "体验版剩余次数", example = "20190")
    private Integer trailLeftCount;

    @Schema(description = "专业版次数", example = "21108")
    private Integer psCount;

    @Schema(description = "专业版剩余次数", example = "3725")
    private Integer psLeftCount;

    @Schema(description = "内容查看次数", example = "14083")
    private Integer contentCount;

    @Schema(description = "内容查看剩余次数", example = "12870")
    private Integer contentLeftCount;

    @Schema(description = "问答次数", example = "5529")
    private Integer askCount;

    @Schema(description = "问答剩余次数", example = "11029")
    private Integer askLeftCount;

    @Schema(description = "体验版最新购买时间")
    private LocalDateTime trailUpdateTime;

    @Schema(description = "专业版最新购买时间")
    private LocalDateTime psUpdateTime;

    @Schema(description = "内容会员到期时间")
    private LocalDateTime contentEndTime;

    @Schema(description = "内容会员开始时间")
    private LocalDateTime contentStartTime;

}