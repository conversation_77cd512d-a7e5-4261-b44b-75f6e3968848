package cn.iocoder.yudao.module.system.controller.admin.gugu.vo;

import cn.iocoder.yudao.module.system.dal.dataobject.gugu.CeeMajorDO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 大学高校专业树形结构 VO
 */
@Schema(description = "管理后台 - 大学高校专业树形结构 VO")
@Data
public class CeeMajorTreeVO {

    @Schema(description = "学科门类树")
    private List<CategoryNode> categories;

    /**
     * 学科门类节点
     */
    @Schema(description = "学科门类节点")
    @Data
    public static class CategoryNode {

        @Schema(description = "学科门类 ID", example = "工学")
        private String id;

        @Schema(description = "学科门类名称", example = "工学")
        private String name;

        @Schema(description = "子节点列表")
        private List<SubCategoryNode> children;

        @Schema(description = "是否已加载子节点数据")
        private Boolean loaded;

        @Schema(description = "该门类下的专业总数")
        private Integer majorCount;
    }

    /**
     * 学科子类节点
     */
    @Schema(description = "学科子类节点")
    @Data
    public static class SubCategoryNode {

        @Schema(description = "学科子类 ID", example = "计算机类")
        private String id;

        @Schema(description = "学科子类名称", example = "计算机类")
        private String name;

        @Schema(description = "专业节点列表")
        private List<MajorNode> children;

        @Schema(description = "该子类下的专业数量")
        private Integer majorCount;
    }

    /**
     * 专业节点
     */
    @Schema(description = "专业节点")
    @Data
    public static class MajorNode {

        @Schema(description = "专业ID", example = "1")
        private Long id;

        @Schema(description = "专业名称", example = "计算机科学与技术")
        private String name;

        @Schema(description = "专业代码", example = "080901")
        private String code;

        @Schema(description = "教育级别", example = "本科")
        private String educationLevel;

        @Schema(description = "是否推荐专业")
        private Boolean isRecommended;

        @Schema(description = "就业方向")
        private String careerDirection;

        @Schema(description = "专业介绍")
        private String majorIntroduction;

        @Schema(description = "毕业规模")
        private String graduateScale;

        @Schema(description = "男女比例")
        private String maleFemaleRatio;

        @Schema(description = "推荐院校")
        private List<String> recommendSchools;

        @Schema(description = "开设课程")
        private List<CeeMajorDO.Course> courses;

        @Schema(description = "学科门类", example = "工学")
        private String disciplinaryCategory;

        @Schema(description = "学科子类", example = "计算机类")
        private String disciplinarySubCategory;

        @Schema(description = "创建时间")
        private LocalDateTime createTime;
    }
}
