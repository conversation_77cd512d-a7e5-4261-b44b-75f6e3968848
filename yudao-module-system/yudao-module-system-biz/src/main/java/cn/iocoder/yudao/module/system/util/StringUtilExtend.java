package cn.iocoder.yudao.module.system.util;

import java.util.ArrayList;
import java.util.List;

/**
 * @date 2025年03月31日 上午 09:45
 */
public class StringUtilExtend {
    public static List<Integer> transferStringToList(String str) {
        System.out.println("工具类拿到的字符串:"+str);
        List<Integer> numbers = new ArrayList<>();

        if(str.contains(",")){
            String[] split = str.split(",");
            for (String s : split) {
                numbers.add(Integer.valueOf(s));
            }
        }

        return numbers;
    }

    public static String transferListToString(List<Integer> numbers) {
        System.out.println("工具类接收到的集合"+numbers);
        StringBuilder stringBuilder = new StringBuilder("");
        if (numbers != null && numbers.size() > 0) {
            for (Integer number : numbers) {
                stringBuilder.append(number).append(",");
            }
            if (stringBuilder.length() > 0) {
                stringBuilder.delete(stringBuilder.length() - 1, stringBuilder.length());
            }
        }
        return stringBuilder.toString();
    }
}
