package cn.iocoder.yudao.module.system.dal.mysql.gugu;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.module.system.controller.admin.gugu.vo.CeeMajorPageReqVO;
import cn.iocoder.yudao.module.system.dal.dataobject.gugu.CeeMajorDO;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 大学高校专业数据 Mapper
 */
@Mapper
public interface CeeMajorMapper extends BaseMapperX<CeeMajorDO> {

    /**
     * 根据专业名称查询专业信息
     *
     * @param majorName 专业名称
     * @return 专业信息列表
     */
    default List<CeeMajorDO> selectByMajorName(String majorName) {
        return selectList(CeeMajorDO::getMajorName, majorName);
    }

    /**
     * 根据专业名称和教育级别查询专业信息
     *
     * @param majorName 专业名称
     * @param educationLevel 教育级别
     * @return 专业信息列表
     */
    default List<CeeMajorDO> selectByMajorNameAndEducationLevel(String majorName, String educationLevel) {
        LambdaQueryWrapperX<CeeMajorDO> wrapper = new LambdaQueryWrapperX<>();
        wrapper.eq(CeeMajorDO::getMajorName, majorName);
        if (StringUtils.hasText(educationLevel)) {
            wrapper.like(CeeMajorDO::getEducationLevel, educationLevel);
        }
        return selectList(wrapper);
    }

    /**
     * 根据专业代码查询专业信息
     *
     * @param majorCode 专业代码
     * @return 专业信息
     */
    default CeeMajorDO selectByMajorCode(String majorCode) {
        return selectOne(CeeMajorDO::getMajorCode, majorCode);
    }

    /**
     * 根据关键字模糊查询专业信息
     *
     * @param keywords 关键字
     * @return 专业信息列表
     */
    default List<CeeMajorDO> selectListByKeywords(String keywords) {
        LambdaQueryWrapperX<CeeMajorDO> wrapper = new LambdaQueryWrapperX<>();
        if (StringUtils.hasText(keywords)) {
            wrapper.like(CeeMajorDO::getMajorName, keywords)
                  .or().like(CeeMajorDO::getDisciplinaryCategory, keywords)
                  .or().like(CeeMajorDO::getMajorIntroduction, keywords);
        }
        wrapper.orderByDesc(CeeMajorDO::getId);
        return selectList(wrapper);
    }

    /**
     * 分页查询专业信息
     *
     * @param reqVO 查询条件
     * @return 专业信息分页结果
     */
    default PageResult<CeeMajorDO> selectPage(CeeMajorPageReqVO reqVO) {
        LambdaQueryWrapperX<CeeMajorDO> wrapper = new LambdaQueryWrapperX<>();

        // 处理关键字搜索
        if (StringUtils.hasText(reqVO.getKeywords())) {
            wrapper.and(w -> w.like(CeeMajorDO::getMajorName, reqVO.getKeywords())
                    .or().like(CeeMajorDO::getDisciplinaryCategory, reqVO.getKeywords())
                    .or().like(CeeMajorDO::getMajorIntroduction, reqVO.getKeywords()));
        }

        // 处理精确匹配条件
        wrapper.eqIfPresent(CeeMajorDO::getMajorCode, reqVO.getMajorCode())
               .eqIfPresent(CeeMajorDO::getEducationLevel, reqVO.getEducationLevel());

        // 如果传递了exactMajorName参数，则按majorname精确匹配
        if (StringUtils.hasText(reqVO.getExactMajorName())) {
            wrapper.eq(CeeMajorDO::getMajorName, reqVO.getExactMajorName());
        }

        // 处理模糊匹配条件
        if (StringUtils.hasText(reqVO.getMajorName())) {
            wrapper.like(CeeMajorDO::getMajorName, reqVO.getMajorName());
        }
        if (StringUtils.hasText(reqVO.getDisciplinaryCategory())) {
            wrapper.like(CeeMajorDO::getDisciplinaryCategory, reqVO.getDisciplinaryCategory());
        }

        // 排序
        wrapper.orderByDesc(CeeMajorDO::getId);

        // 执行分页查询
        return selectPage(reqVO, wrapper);
    }

    /**
     * 查询所有学科门类
     *
     * @param educationLevel 教育级别
     * @return 学科门类列表
     */
    default List<String> selectDistinctDisciplinaryCategories(String educationLevel) {
        LambdaQueryWrapperX<CeeMajorDO> wrapper = new LambdaQueryWrapperX<>();
        if (StringUtils.hasText(educationLevel)) {
            wrapper.eq(CeeMajorDO::getEducationLevel, educationLevel);
        }
        wrapper.select(CeeMajorDO::getDisciplinaryCategory);
        wrapper.groupBy(CeeMajorDO::getDisciplinaryCategory);
        wrapper.orderByAsc(CeeMajorDO::getDisciplinaryCategory);

        // 执行查询并提取学科门类列表
        return selectList(wrapper).stream()
                .map(CeeMajorDO::getDisciplinaryCategory)
                .filter(StringUtils::hasText)
                .collect(Collectors.toList());
    }

    /**
     * 查询指定学科门类下的所有学科子类
     *
     * @param disciplinaryCategory 学科门类
     * @param educationLevel 教育级别
     * @return 学科子类列表
     */
    default List<String> selectDistinctDisciplinarySubCategories(String disciplinaryCategory, String educationLevel) {
        LambdaQueryWrapperX<CeeMajorDO> wrapper = new LambdaQueryWrapperX<>();
        wrapper.eq(CeeMajorDO::getDisciplinaryCategory, disciplinaryCategory);
        if (StringUtils.hasText(educationLevel)) {
            wrapper.eq(CeeMajorDO::getEducationLevel, educationLevel);
        }
        wrapper.select(CeeMajorDO::getDisciplinarySubCategory);
        wrapper.groupBy(CeeMajorDO::getDisciplinarySubCategory);
        wrapper.orderByAsc(CeeMajorDO::getDisciplinarySubCategory);

        // 执行查询并提取学科子类列表
        return selectList(wrapper).stream()
                .map(CeeMajorDO::getDisciplinarySubCategory)
                .filter(StringUtils::hasText)
                .collect(Collectors.toList());
    }

    /**
     * 查询指定学科子类下的所有专业
     *
     * @param disciplinaryCategory 学科门类
     * @param disciplinarySubCategory 学科子类
     * @param educationLevel 教育级别
     * @param majorName 专业名称，模糊搜索
     * @return 专业列表
     */
    default List<CeeMajorDO> selectByDisciplinarySubCategory(String disciplinaryCategory, String disciplinarySubCategory, String educationLevel, String majorName) {
        LambdaQueryWrapperX<CeeMajorDO> wrapper = new LambdaQueryWrapperX<>();
        wrapper.eq(CeeMajorDO::getDisciplinaryCategory, disciplinaryCategory);
        wrapper.eq(CeeMajorDO::getDisciplinarySubCategory, disciplinarySubCategory);
        if (StringUtils.hasText(educationLevel)) {
            wrapper.eq(CeeMajorDO::getEducationLevel, educationLevel);
        }
        if (StringUtils.hasText(majorName)) {
            wrapper.like(CeeMajorDO::getMajorName, majorName);
        }
        wrapper.orderByAsc(CeeMajorDO::getMajorCode);

        return selectList(wrapper);
    }

    /**
     * 根据专业名称模糊搜索专业
     *
     * @param majorName 专业名称
     * @param educationLevel 教育级别
     * @return 专业列表
     */
    default List<CeeMajorDO> searchByMajorName(String majorName, String educationLevel) {
        LambdaQueryWrapperX<CeeMajorDO> wrapper = new LambdaQueryWrapperX<>();
        wrapper.like(CeeMajorDO::getMajorName, majorName);
        if (StringUtils.hasText(educationLevel)) {
            wrapper.eq(CeeMajorDO::getEducationLevel, educationLevel);
        }
        wrapper.orderByAsc(CeeMajorDO::getDisciplinaryCategory)
               .orderByAsc(CeeMajorDO::getDisciplinarySubCategory)
               .orderByAsc(CeeMajorDO::getMajorCode);

        return selectList(wrapper);
    }
}
