package cn.iocoder.yudao.module.system.service.gugu;

import cn.iocoder.yudao.module.system.controller.admin.gugu.gugu.CollegeEnrollmentPlanInfo;
import cn.iocoder.yudao.module.system.controller.admin.gugu.gugu.CollegeEnrollmentPlanQueryReqVO;
import cn.iocoder.yudao.module.system.dal.dataobject.collegeenrollmentplan.CollegeEnrollmentPlanDO;
import cn.iocoder.yudao.module.system.dal.mysql.collegeenrollmentplan.CollegeEnrollmentPlanMapper;
import cn.iocoder.yudao.module.system.util.gugu.GuGuDataUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 高校招生计划服务实现类
 */
@Service
@Slf4j
public class CollegeEnrollmentPlanServiceImpl implements CollegeEnrollmentPlanService {

    @Resource
    private CollegeEnrollmentPlanMapper collegeEnrollmentPlanMapper;

    // 中国省份列表
    private static final List<String> CHINA_PROVINCES = Arrays.asList(
            "北京", "天津", "河北", "山西", "内蒙古", "辽宁", "吉林", "黑龙江",
            "上海", "江苏", "浙江", "安徽", "福建", "江西", "山东", "河南",
            "湖北", "湖南", "广东", "广西", "海南", "重庆", "四川", "贵州",
            "云南", "西藏", "陕西", "甘肃", "青海", "宁夏", "新疆"
    );

    // 数据获取线程池
    private final ExecutorService dataFetchExecutor = Executors.newFixedThreadPool(3);

    // 数据保存线程池
    private final ExecutorService dataSaveExecutor = Executors.newFixedThreadPool(2);

    // 请求频率控制
    private volatile long lastRequestTime = 0;
    private static final long MIN_REQUEST_INTERVAL = 1000; // 最小请求间隔1秒

    @Override
    @Cacheable(value = "enrollmentPlanInfo#1d",
               key = "#reqVO.schoolName + ':' + #reqVO.collegeMajorName + ':' + #reqVO.provinceName + ':' + #reqVO.type + ':' + #reqVO.year + ':' + #reqVO.pageIndex + ':' + #reqVO.pageSize",
               unless = "#result == null or #result.get('success') != true")
    public Map<String, Object> getCollegeEnrollmentPlanInfo(CollegeEnrollmentPlanQueryReqVO reqVO) {
        log.debug("查询招生计划数据: 学校={}, 专业={}, 省份={}, 类型={}, 年份={}",
                 reqVO.getSchoolName(), reqVO.getCollegeMajorName(), reqVO.getProvinceName(),
                 reqVO.getType(), reqVO.getYear());

        return GuGuDataUtils.getCollegeEnrollmentPlanInfo(reqVO);
    }

    @Override
    @Cacheable(value = "enrollmentPlanMajor#1d",
               key = "#schoolName + ':' + #majorName + ':' + #provinceName + ':' + #typeName + ':' + #year",
               unless = "#result == null or #result.isEmpty()")
    public List<CollegeEnrollmentPlanInfo> getEnrollmentPlanForMajor(String schoolName, String majorName,
                                                                    String provinceName, String typeName, Integer year) {
        log.debug("查询专业招生计划数据: 学校={}, 专业={}, 省份={}, 类型={}, 年份={}",
                 schoolName, majorName, provinceName, typeName, year);

        // 构建查询参数
        CollegeEnrollmentPlanQueryReqVO reqVO = new CollegeEnrollmentPlanQueryReqVO();
        reqVO.setPageIndex(1);
        reqVO.setPageSize(100); // 每个专业最多查询20条招生计划数据
        reqVO.setSchoolName(schoolName);
        reqVO.setCollegeMajorName(majorName);
        reqVO.setProvinceName(provinceName);
        reqVO.setYear(year);

        if (typeName != null && !typeName.isEmpty()) {
            reqVO.setType(typeName);
        }

        // 调用API查询数据
        Map<String, Object> result = getCollegeEnrollmentPlanInfo(reqVO);

        if (Boolean.TRUE.equals(result.get("success"))) {
            @SuppressWarnings("unchecked")
            List<CollegeEnrollmentPlanInfo> enrollmentPlanList =
                (List<CollegeEnrollmentPlanInfo>) result.get("enrollmentPlanList");
            return enrollmentPlanList;
        }

        return null;
    }

    /**
     * 控制API请求频率，避免请求过快触发限流
     */
    private void controlRequestRate() {
        long currentTime = System.currentTimeMillis();
        long elapsedTime = currentTime - lastRequestTime;

        if (lastRequestTime > 0 && elapsedTime < MIN_REQUEST_INTERVAL) {
            try {
                long sleepTime = MIN_REQUEST_INTERVAL - elapsedTime;
                Thread.sleep(sleepTime);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                log.warn("请求频率控制被中断: {}", e.getMessage());
            }
        }

        lastRequestTime = System.currentTimeMillis();
    }

    /**
     * 获取指定省份和年份的所有招生计划数据
     * @param provinceName 省份名称
     * @param year 年份
     * @return 所有数据
     */
    private Map<String, Object> getEnrollmentPlanInfoAll(String provinceName, Integer year) {
        Map<String, Object> result = new HashMap<>();
        List<CollegeEnrollmentPlanInfo> allEnrollmentPlanList = new ArrayList<>();

        try {
            // 构建查询参数
            CollegeEnrollmentPlanQueryReqVO reqVO = new CollegeEnrollmentPlanQueryReqVO();
            reqVO.setPageIndex(1);
            reqVO.setPageSize(100); // 使用API允许的最大页面大小
            reqVO.setProvinceName(provinceName);
            reqVO.setYear(year);

            // 控制请求频率
            controlRequestRate();

            // 获取第一页数据，用于获取总数量和总页数
            Map<String, Object> firstPageResult = GuGuDataUtils.getCollegeEnrollmentPlanInfo(reqVO);

            if (!Boolean.TRUE.equals(firstPageResult.get("success"))) {
                return firstPageResult; // 如果第一页查询失败，直接返回错误
            }

            // 获取总数量和总页数
            int totalCount = (Integer) firstPageResult.get("totalCount");
            int totalPages = (Integer) firstPageResult.get("totalPages");

            log.info("查询到{}省{}年的招生计划数据总数量为{}，总页数为{}",
                    provinceName, year, totalCount, totalPages);

            // 添加第一页的数据
            @SuppressWarnings("unchecked")
            List<CollegeEnrollmentPlanInfo> firstPageList =
                (List<CollegeEnrollmentPlanInfo>) firstPageResult.get("enrollmentPlanList");
            if (firstPageList != null && !firstPageList.isEmpty()) {
                allEnrollmentPlanList.addAll(firstPageList);
                log.info("成功获取第1页数据，共{}条记录", firstPageList.size());

                // 异步保存数据到数据库
                asyncSaveEnrollmentPlanData(new ArrayList<>(firstPageList));
            }

            // 如果有多页，使用多线程获取其他页的数据
            if (totalPages > 1) {
                // 创建一个线程安全的集合来存储所有页的数据
                final Map<Integer, List<CollegeEnrollmentPlanInfo>> pageDataMap = new ConcurrentHashMap<>();

                // 创建任务列表
                List<CompletableFuture<Void>> futures = new ArrayList<>();

                // 从第2页开始获取数据
                for (int pageIndex = 2; pageIndex <= totalPages; pageIndex++) {
                    final int currentPageIndex = pageIndex;

                    // 创建并提交任务
                    CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
                        try {
                            // 创建当前页的请求参数
                            CollegeEnrollmentPlanQueryReqVO pageReqVO = new CollegeEnrollmentPlanQueryReqVO();
                            pageReqVO.setPageIndex(currentPageIndex);
                            pageReqVO.setPageSize(100);
                            pageReqVO.setProvinceName(provinceName);
                            pageReqVO.setYear(year);

                            // 控制请求频率
                            controlRequestRate();

                            // 获取当前页数据
                            Map<String, Object> pageResult = GuGuDataUtils.getCollegeEnrollmentPlanInfo(pageReqVO);

                            if (Boolean.TRUE.equals(pageResult.get("success"))) {
                                @SuppressWarnings("unchecked")
                                List<CollegeEnrollmentPlanInfo> pageEnrollmentPlanList =
                                    (List<CollegeEnrollmentPlanInfo>) pageResult.get("enrollmentPlanList");
                                if (pageEnrollmentPlanList != null && !pageEnrollmentPlanList.isEmpty()) {
                                    // 将数据存入线程安全的Map
                                    pageDataMap.put(currentPageIndex, pageEnrollmentPlanList);
                                    log.info("成功获取第{}页数据，共{}条记录--剩余{}页",
                                            currentPageIndex, pageEnrollmentPlanList.size(), totalPages - currentPageIndex);

                                    // 立即异步保存数据
                                    final List<CollegeEnrollmentPlanInfo> dataToSave = new ArrayList<>(pageEnrollmentPlanList);
                                    final int pageIdx = currentPageIndex;
                                    asyncSaveEnrollmentPlanData(dataToSave);
                                }
                            } else {
                                log.error("查询第{}页失败: {}", currentPageIndex, pageResult.get("message"));
                            }
                        } catch (Exception e) {
                            log.error("处理第{}页数据时发生异常", currentPageIndex, e);
                        }
                    }, dataFetchExecutor);

                    futures.add(future);
                }

                // 等待所有任务完成
                CompletableFuture<Void> allFutures = CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]));
                allFutures.join(); // 等待所有任务完成

                // 按页码顺序添加数据
                for (int pageIndex = 2; pageIndex <= totalPages; pageIndex++) {
                    List<CollegeEnrollmentPlanInfo> pageData = pageDataMap.get(pageIndex);
                    if (pageData != null) {
                        allEnrollmentPlanList.addAll(pageData);
                    }
                }
            }

            // 构建返回结果
            result.put("success", true);
            result.put("enrollmentPlanList", allEnrollmentPlanList);
            result.put("totalCount", totalCount);
            result.put("pageIndex", 1);
            result.put("pageSize", allEnrollmentPlanList.size());
            result.put("totalPages", totalPages);
            result.put("isAllData", true);

            return result;
        } catch (Exception e) {
            log.error("获取{}省{}年招生计划数据失败: {}", provinceName, year, e.getMessage(), e);
            result.put("success", false);
            result.put("message", "查询招生计划数据失败: " + e.getMessage());
            return result;
        }
    }

    /**
     * 异步保存招生计划数据
     * @param enrollmentPlanList 招生计划数据列表
     */
    private void asyncSaveEnrollmentPlanData(List<CollegeEnrollmentPlanInfo> enrollmentPlanList) {
        if (enrollmentPlanList == null || enrollmentPlanList.isEmpty()) {
            return;
        }

        try {
            // 检查线程池状态
            if (dataSaveExecutor.isShutdown() || dataSaveExecutor.isTerminated()) {
                log.warn("数据保存线程池已关闭，无法提交异步任务，将直接同步保存数据");
                int savedCount = saveEnrollmentPlanData(enrollmentPlanList);
                log.info("同步保存{}条招生计划数据到数据库", savedCount);
            } else {
                // 使用线程池提交任务
                dataSaveExecutor.submit(() -> {
                    try {
                        int savedCount = saveEnrollmentPlanData(enrollmentPlanList);
                        log.info("异步保存{}条招生计划数据到数据库", savedCount);
                    } catch (Exception e) {
                        log.error("异步保存招生计划数据到数据库失败: {}", e.getMessage(), e);
                    }
                });
            }
        } catch (Exception e) {
            log.error("提交异步保存任务失败，将尝试同步保存: {}", e.getMessage(), e);
            try {
                // 如果提交失败，则在当前线程中同步执行
                int savedCount = saveEnrollmentPlanData(enrollmentPlanList);
                log.info("同步保存{}条招生计划数据到数据库", savedCount);
            } catch (Exception ex) {
                log.error("同步保存数据失败: {}", ex.getMessage(), ex);
            }
        }
    }

    @Override
    public Map<String, Object> batchImportEnrollmentPlanData(List<Integer> years, boolean useMultiThread) {
        log.info("开始批量导入历年高校招生计划数据，年份: {}, 多线程: {}", years, useMultiThread);

        Map<String, Object> result = new HashMap<>();
        AtomicInteger totalImported = new AtomicInteger(0);
        AtomicInteger totalProvinces = new AtomicInteger(0);
        AtomicInteger processedProvinces = new AtomicInteger(0);

        long startTime = System.currentTimeMillis();

        try {
            for (Integer year : years) {
                log.info("开始导入{}年数据", year);
                int yearImported = 0;

                if (useMultiThread) {
                    // 使用多线程处理
                    int threadCount = Math.min(CHINA_PROVINCES.size(), 5); // 最多使用5个线程
                    ExecutorService executorService = Executors.newFixedThreadPool(threadCount);

                    try {
                        List<CompletableFuture<Integer>> futures = new ArrayList<>();

                        for (String province : CHINA_PROVINCES) {
                            CompletableFuture<Integer> future = CompletableFuture.supplyAsync(() -> {
                                try {
                                    log.info("开始获取{}年{}省的招生计划数据", year, province);
                                    Map<String, Object> provinceResult = getEnrollmentPlanInfoAll(province, year);

                                    if (Boolean.TRUE.equals(provinceResult.get("success"))) {
                                        @SuppressWarnings("unchecked")
                                        List<CollegeEnrollmentPlanInfo> allEnrollmentPlanList =
                                            (List<CollegeEnrollmentPlanInfo>) provinceResult.get("enrollmentPlanList");

                                        int imported = allEnrollmentPlanList != null ? allEnrollmentPlanList.size() : 0;
                                        processedProvinces.incrementAndGet();
                                        log.info("{}年{}省数据导入完成，导入{}条数据", year, province, imported);
                                        return imported;
                                    } else {
                                        String message = (String) provinceResult.get("message");
                                        log.error("{}年{}省数据导入失败: {}", year, province, message);
                                        return 0;
                                    }
                                } catch (Exception e) {
                                    log.error("{}年{}省数据导入失败: {}", year, province, e.getMessage(), e);
                                    return 0;
                                }
                            }, executorService);

                            futures.add(future);
                        }

                        // 等待所有任务完成
                        for (CompletableFuture<Integer> future : futures) {
                            yearImported += future.get();
                        }

                        totalProvinces.addAndGet(CHINA_PROVINCES.size());

                    } finally {
                        executorService.shutdown();
                    }
                } else {
                    // 单线程处理
                    for (String province : CHINA_PROVINCES) {
                        try {
                            log.info("开始获取{}年{}省的招生计划数据", year, province);
                            Map<String, Object> provinceResult = getEnrollmentPlanInfoAll(province, year);

                            if (Boolean.TRUE.equals(provinceResult.get("success"))) {
                                @SuppressWarnings("unchecked")
                                List<CollegeEnrollmentPlanInfo> allEnrollmentPlanList =
                                    (List<CollegeEnrollmentPlanInfo>) provinceResult.get("enrollmentPlanList");

                                int imported = allEnrollmentPlanList != null ? allEnrollmentPlanList.size() : 0;
                                yearImported += imported;
                                processedProvinces.incrementAndGet();
                                totalProvinces.incrementAndGet();
                                log.info("{}年{}省数据导入完成，导入{}条数据", year, province, imported);
                            } else {
                                String message = (String) provinceResult.get("message");
                                log.error("{}年{}省数据导入失败: {}", year, province, message);
                            }
                        } catch (Exception e) {
                            log.error("{}年{}省数据导入失败: {}", year, province, e.getMessage(), e);
                        }
                    }
                }

                totalImported.addAndGet(yearImported);
                log.info("{}年数据导入完成，共导入{}条数据", year, yearImported);
            }

            long endTime = System.currentTimeMillis();
            long duration = endTime - startTime;

            result.put("success", true);
            result.put("totalImported", totalImported.get());
            result.put("totalProvinces", totalProvinces.get());
            result.put("processedProvinces", processedProvinces.get());
            result.put("duration", duration);
            result.put("message", String.format("批量导入完成，共处理%d个省份，导入%d条数据，耗时%d毫秒",
                    processedProvinces.get(), totalImported.get(), duration));

            log.info("批量导入历年高校招生计划数据完成: {}", result.get("message"));

        } catch (Exception e) {
            log.error("批量导入历年高校招生计划数据失败: {}", e.getMessage(), e);
            result.put("success", false);
            result.put("message", "批量导入失败: " + e.getMessage());
            result.put("totalImported", totalImported.get());
            result.put("processedProvinces", processedProvinces.get());
        }

        return result;
    }

    @Override
    public int importEnrollmentPlanDataByProvince(String provinceName, Integer year) {
        log.debug("开始导入{}年{}省招生计划数据", year, provinceName);

        try {
            Map<String, Object> result = getEnrollmentPlanInfoAll(provinceName, year);

            if (Boolean.TRUE.equals(result.get("success"))) {
                @SuppressWarnings("unchecked")
                List<CollegeEnrollmentPlanInfo> allEnrollmentPlanList =
                    (List<CollegeEnrollmentPlanInfo>) result.get("enrollmentPlanList");

                int totalImported = allEnrollmentPlanList != null ? allEnrollmentPlanList.size() : 0;
                log.debug("{}年{}省招生计划数据导入完成，共导入{}条", year, provinceName, totalImported);
                return totalImported;
            } else {
                String message = (String) result.get("message");
                log.error("{}年{}省招生计划数据导入失败: {}", year, provinceName, message);
                return 0;
            }
        } catch (Exception e) {
            log.error("{}年{}省招生计划数据导入异常: {}", year, provinceName, e.getMessage(), e);
            return 0;
        }
    }

    /**
     * 保存招生计划数据到数据库
     * @param enrollmentPlanList 招生计划数据列表
     * @return 保存的数据条数
     */
    private int saveEnrollmentPlanData(List<CollegeEnrollmentPlanInfo> enrollmentPlanList) {
        if (enrollmentPlanList == null || enrollmentPlanList.isEmpty()) {
            return 0;
        }

        int savedCount = 0;

        // 分批保存数据，每批最多500条
        int batchSize = 500;
        for (int i = 0; i < enrollmentPlanList.size(); i += batchSize) {
            int endIndex = Math.min(i + batchSize, enrollmentPlanList.size());
            List<CollegeEnrollmentPlanInfo> batch = enrollmentPlanList.subList(i, endIndex);

            // 批量保存到数据库
            for (CollegeEnrollmentPlanInfo info : batch) {
                try {
                    // 检查是否已存在相同数据
                    boolean exists = collegeEnrollmentPlanMapper.existsByUniqueKey(
                            info.getProvinceName(),
                            info.getSchoolUuid(),
                            info.getCollegeMajorName(),
                            info.getYear(),
                            info.getType(),
                            info.getBatchName()
                    );

                    if (!exists) {
                        // 转换为DO对象
                        CollegeEnrollmentPlanDO planDO = convertToCollegeEnrollmentPlanDO(info);

                        // 保存到数据库
                        collegeEnrollmentPlanMapper.insert(planDO);
                        savedCount++;
                    }

                } catch (Exception e) {
                    // 记录错误但继续处理其他记录
                    log.warn("保存招生计划数据失败: {}", e.getMessage());
                }
            }
        }

        return savedCount;
    }

    /**
     * 转换CollegeEnrollmentPlanInfo为CollegeEnrollmentPlanDO
     * @param info API返回的数据
     * @return 数据库实体对象
     */
    private CollegeEnrollmentPlanDO convertToCollegeEnrollmentPlanDO(CollegeEnrollmentPlanInfo info) {
        return CollegeEnrollmentPlanDO.builder()
                .provinceName(info.getProvinceName())
                .schoolUuid(info.getSchoolUuid())
                .schoolName(info.getSchoolName())
                .collegeMajorName(info.getCollegeMajorName())
                .collegeMajorCode(info.getCollegeMajorCode())
                .year(info.getYear())
                .enrollmentNumbers(info.getEnrollmentNumbers())
                .inSchoolYears(info.getInSchoolYears())
                .classOne(info.getClassOne())
                .classTwo(info.getClassTwo())
                .batchName(info.getBatchName())
                .type(info.getType())
                .courseSelectionRequirements(info.getCourseSelectionRequirements())
                .build();
    }

    @Override
    public List<CollegeEnrollmentPlanDO> getEnrollmentPlanFromDatabase(String schoolName, String majorName,
                                                                      String provinceName, Integer year) {
        return collegeEnrollmentPlanMapper.selectListBySchoolAndMajor(schoolName, majorName, provinceName, year);
    }

    @Override
    public Map<String, Object> getImportProgress() {
        Map<String, Object> progress = new HashMap<>();

        try {
            // 统计各年份的数据量
            Map<Integer, Long> yearCounts = new HashMap<>();
            for (int year = 2022; year <= 2024; year++) {
                Long count = collegeEnrollmentPlanMapper.countByYear(year);
                yearCounts.put(year, count);
            }

            // 统计各省份的数据量
            Map<String, Long> provinceCounts = new HashMap<>();
            for (String province : CHINA_PROVINCES) {
                Long count = collegeEnrollmentPlanMapper.countByProvince(province);
                provinceCounts.put(province, count);
            }

            progress.put("success", true);
            progress.put("yearCounts", yearCounts);
            progress.put("provinceCounts", provinceCounts);
            progress.put("totalProvinces", CHINA_PROVINCES.size());

        } catch (Exception e) {
            log.error("获取导入进度统计失败: {}", e.getMessage(), e);
            progress.put("success", false);
            progress.put("message", "获取进度统计失败: " + e.getMessage());
        }

        return progress;
    }
}
