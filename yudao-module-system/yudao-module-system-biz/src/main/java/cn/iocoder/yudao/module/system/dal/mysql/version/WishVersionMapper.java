package cn.iocoder.yudao.module.system.dal.mysql.version;

import java.util.*;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.module.system.controller.admin.version.vo.WishVersionPageReqVO;
import cn.iocoder.yudao.module.system.dal.dataobject.version.WishVersionDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 志愿咨询题库版本表	 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface WishVersionMapper extends BaseMapperX<WishVersionDO> {

    default PageResult<WishVersionDO> selectPage(WishVersionPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<WishVersionDO>()
                .likeIfPresent(WishVersionDO::getName, reqVO.getName())
                .eqIfPresent(WishVersionDO::getContent, reqVO.getContent())
                .betweenIfPresent(WishVersionDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(WishVersionDO::getId));
    }

}