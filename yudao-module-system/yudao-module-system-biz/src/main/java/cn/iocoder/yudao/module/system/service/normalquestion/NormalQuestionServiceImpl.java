package cn.iocoder.yudao.module.system.service.normalquestion;

import cn.iocoder.yudao.framework.common.exception.ErrorCode;
import cn.iocoder.yudao.module.system.controller.admin.normalquestion.vo.NormalQuestionPageReqVO;
import cn.iocoder.yudao.module.system.controller.admin.normalquestion.vo.NormalQuestionSaveReqVO;
import cn.iocoder.yudao.module.system.dal.dataobject.normalquestion.NormalQuestionDO;
import cn.iocoder.yudao.module.system.dal.mysql.normalquestion.NormalQuestionMapper;
import cn.iocoder.yudao.module.system.util.baidu.BaiduAiUtils;
import com.baidubce.appbuilder.console.appbuilderclient.AppBuilderClient;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import java.util.*;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;


import javax.annotation.Resource;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.module.system.util.baidu.BaiduAiUtils.APP_BUILDER_TOKEN;

/**
 * AI 报告 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class NormalQuestionServiceImpl implements NormalQuestionService {

    @Resource
    private NormalQuestionMapper normalQuestionMapper;

    @Override
    public Long createNormalQuestion(NormalQuestionSaveReqVO createReqVO) {
        // 插入
        NormalQuestionDO normalQuestion = BeanUtils.toBean(createReqVO, NormalQuestionDO.class);
        normalQuestionMapper.insert(normalQuestion);
        // 返回
        return normalQuestion.getId();
    }

    @Override
    public void updateNormalQuestion(NormalQuestionSaveReqVO updateReqVO) {
        // 校验存在
        validateNormalQuestionExists(updateReqVO.getId());
        // 更新
        NormalQuestionDO updateObj = BeanUtils.toBean(updateReqVO, NormalQuestionDO.class);
        normalQuestionMapper.updateById(updateObj);
    }

    @Override
    public void deleteNormalQuestion(Long id) {
        // 校验存在
        validateNormalQuestionExists(id);
        // 删除
        normalQuestionMapper.deleteById(id);
    }

    private void validateNormalQuestionExists(Long id) {
        if (normalQuestionMapper.selectById(id) == null) {
            throw exception(new ErrorCode(404,"AI 报告不存在"));
        }
    }

    @Override
    public NormalQuestionDO getNormalQuestion(Long id) {
        return normalQuestionMapper.selectById(id);
    }

    @Override
    public PageResult<NormalQuestionDO> getNormalQuestionPage(NormalQuestionPageReqVO pageReqVO) {
        PageResult<NormalQuestionDO> normalQuestionDOPageResult = normalQuestionMapper.selectPage(pageReqVO);
        return normalQuestionDOPageResult;
    }

}