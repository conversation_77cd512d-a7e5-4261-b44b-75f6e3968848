package cn.iocoder.yudao.module.system.dal.mysql.chatconversation;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.module.system.controller.admin.conversation.vo.ChatConversationPageReqVO;
import cn.iocoder.yudao.module.system.dal.dataobject.chatconversation.ChatConversationDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * AI 聊天对话 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface ChatConversationMapper extends BaseMapperX<ChatConversationDO> {

    default PageResult<ChatConversationDO> selectPage(ChatConversationPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<ChatConversationDO>()
                .eqIfPresent(ChatConversationDO::getUserId, reqVO.getUserId())
                .eqIfPresent(ChatConversationDO::getRoleId, reqVO.getRoleId())
                .eqIfPresent(ChatConversationDO::getTitle, reqVO.getTitle())
                .eqIfPresent(ChatConversationDO::getModelId, reqVO.getModelId())
                .eqIfPresent(ChatConversationDO::getModel, reqVO.getModel())
                .eqIfPresent(ChatConversationDO::getPinned, reqVO.getPinned())
                .betweenIfPresent(ChatConversationDO::getPinnedTime, reqVO.getPinnedTime())
                .eqIfPresent(ChatConversationDO::getSystemMessage, reqVO.getSystemMessage())
                .eqIfPresent(ChatConversationDO::getTemperature, reqVO.getTemperature())
                .eqIfPresent(ChatConversationDO::getMaxTokens, reqVO.getMaxTokens())
                .eqIfPresent(ChatConversationDO::getMaxContexts, reqVO.getMaxContexts())
                .betweenIfPresent(ChatConversationDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(ChatConversationDO::getId));
    }

}