package cn.iocoder.yudao.module.system.mq.producer;

import cn.iocoder.yudao.framework.mq.redis.core.RedisMQTemplate;
import cn.iocoder.yudao.module.system.mq.message.ReportGenerateMessage;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.connection.stream.RecordId;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 报告生成消息生产者
 *
 * <AUTHOR>
 */
@Component
@Slf4j
public class ReportProducer {

    @Resource
    private RedisMQTemplate redisMQTemplate;

    /**
     * 发送报告生成消息
     *
     * @param userId 用户ID
     * @param answerRecordId 答题记录ID
     * @param question 问题内容
     * @param version 报告版本
     * @param name 报告名称
     * @return 消息记录ID
     */
    public RecordId sendReportGenerateMessage(Long userId, Integer answerRecordId, 
                                            String question, Integer version, String name) {
        ReportGenerateMessage message = new ReportGenerateMessage();
        message.setUserId(userId);
        message.setAnswerRecordId(answerRecordId);
        message.setQuestion(question);
        message.setVersion(version);
        message.setName(name);
        
        RecordId recordId = redisMQTemplate.send(message);
        log.info("[sendReportGenerateMessage][发送报告生成消息成功: userId={}, answerRecordId={}, version={}, recordId={}]", 
                userId, answerRecordId, version, recordId);
        
        return recordId;
    }
}
