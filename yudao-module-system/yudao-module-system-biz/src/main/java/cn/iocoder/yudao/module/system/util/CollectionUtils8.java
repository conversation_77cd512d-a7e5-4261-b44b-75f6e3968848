package cn.iocoder.yudao.module.system.util;

import java.util.*;

/**
 * Java 8 兼容的集合工具类
 * 提供类似 Java 9+ 的便捷方法
 */
public class CollectionUtils8 {

    /**
     * 创建不可变的Set（Java 8兼容版本）
     */
    @SafeVarargs
    public static <T> Set<T> setOf(T... elements) {
        Set<T> set = new HashSet<>(Arrays.asList(elements));
        return Collections.unmodifiableSet(set);
    }

    /**
     * 创建不可变的List（Java 8兼容版本）
     */
    @SafeVarargs
    public static <T> List<T> listOf(T... elements) {
        List<T> list = new ArrayList<>(Arrays.asList(elements));
        return Collections.unmodifiableList(list);
    }

    /**
     * 创建可变的Set
     */
    @SafeVarargs
    public static <T> Set<T> mutableSetOf(T... elements) {
        return new HashSet<>(Arrays.asList(elements));
    }

    /**
     * 创建可变的List
     */
    @SafeVarargs
    public static <T> List<T> mutableListOf(T... elements) {
        return new ArrayList<>(Arrays.asList(elements));
    }

    /**
     * 创建空的不可变Set
     */
    public static <T> Set<T> emptySet() {
        return Collections.emptySet();
    }

    /**
     * 创建空的不可变List
     */
    public static <T> List<T> emptyList() {
        return Collections.emptyList();
    }

    /**
     * 创建单元素的不可变Set
     */
    public static <T> Set<T> singletonSet(T element) {
        return Collections.singleton(element);
    }

    /**
     * 创建单元素的不可变List
     */
    public static <T> List<T> singletonList(T element) {
        return Collections.singletonList(element);
    }

    /**
     * 检查集合是否为空或null
     */
    public static boolean isEmpty(Collection<?> collection) {
        return collection == null || collection.isEmpty();
    }

    /**
     * 检查集合是否不为空且不为null
     */
    public static boolean isNotEmpty(Collection<?> collection) {
        return !isEmpty(collection);
    }

    /**
     * 安全地获取集合大小
     */
    public static int size(Collection<?> collection) {
        return collection == null ? 0 : collection.size();
    }

    /**
     * 将数组转换为Set
     */
    @SafeVarargs
    public static <T> Set<T> asSet(T... elements) {
        if (elements == null || elements.length == 0) {
            return new HashSet<>();
        }
        return new HashSet<>(Arrays.asList(elements));
    }

    /**
     * 将数组转换为List
     */
    @SafeVarargs
    public static <T> List<T> asList(T... elements) {
        if (elements == null || elements.length == 0) {
            return new ArrayList<>();
        }
        return new ArrayList<>(Arrays.asList(elements));
    }
}
