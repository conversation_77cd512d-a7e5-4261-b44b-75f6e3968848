package cn.iocoder.yudao.module.system.controller.admin.gugu.vo;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/**
 * 大学高校专业数据分页查询 Request VO
 */
@Schema(description = "管理后台 - 大学高校专业数据分页查询 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class CeeMajorPageReqVO extends PageParam {

    @Schema(description = "专业名称，模糊匹配", example = "计算机")
    private String majorName;

    @Schema(description = "专业代码，精确匹配", example = "080901")
    private String majorCode;

    @Schema(description = "学科门类，模糊匹配", example = "工学")
    private String disciplinaryCategory;

    @Schema(description = "教育级别，精确匹配", example = "本科")
    private String educationLevel;

    @Schema(description = "关键字，模糊匹配专业名称、学科、专业介绍", example = "计算机")
    private String keywords;

    @Schema(description = "专业名称，精确匹配", example = "计算机科学与技术")
    private String exactMajorName;
}
