package cn.iocoder.yudao.module.system.dal.dataobject.collegeenrollmentplan;

import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

/**
 * 历年高校招生计划数据 DO
 *
 * <AUTHOR>
 */
@TableName("system_college_enrollment_plan")
@KeySequence("system_college_enrollment_plan_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CollegeEnrollmentPlanDO extends BaseDO {

    /**
     * 主键ID
     */
    @TableId
    private Long id;

    /**
     * 招生省份
     */
    private String provinceName;

    /**
     * 高校唯一ID
     */
    private String schoolUuid;

    /**
     * 高校名称
     */
    private String schoolName;

    /**
     * 高校专业名称
     */
    private String collegeMajorName;

    /**
     * 高校专业代码
     */
    private String collegeMajorCode;

    /**
     * 招生年份
     */
    private Integer year;

    /**
     * 招生人数
     */
    private Integer enrollmentNumbers;

    /**
     * 学制年限
     */
    private String inSchoolYears;

    /**
     * 专业大类
     */
    private String classOne;

    /**
     * 专业小类
     */
    private String classTwo;

    /**
     * 录取批次
     */
    private String batchName;

    /**
     * 文理综合类别
     */
    private String type;

    /**
     * 选科要求
     */
    private String courseSelectionRequirements;

}
