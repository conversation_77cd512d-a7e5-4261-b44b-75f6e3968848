package cn.iocoder.yudao.module.system.controller.admin.gugu.gugu;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * 专业推荐请求 VO
 */
@Schema(description = "专业推荐请求参数")
@Data
public class MajorRecommendationReqVO {

    @Schema(description = "用户输入的文本信息", requiredMode = Schema.RequiredMode.REQUIRED, example = "安徽2025年考生，我是男生，选科是物理、化学、历史，高考分数：总分409...")
    @NotBlank(message = "用户输入不能为空")
    private String userInput;
}
