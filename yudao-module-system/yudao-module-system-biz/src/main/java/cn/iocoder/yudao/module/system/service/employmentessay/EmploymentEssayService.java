package cn.iocoder.yudao.module.system.service.employmentessay;

import java.util.*;
import javax.validation.*;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.system.controller.admin.employmentessay.vo.EmploymentEssayPageReqVO;
import cn.iocoder.yudao.module.system.controller.admin.employmentessay.vo.EmploymentEssaySaveReqVO;
import cn.iocoder.yudao.module.system.dal.dataobject.employmentessay.EmploymentEssayDO;

/**
 * 就业文章 Service 接口
 *
 * <AUTHOR>
 */
public interface EmploymentEssayService {

    /**
     * 创建就业文章
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createEmploymentEssay(@Valid EmploymentEssaySaveReqVO createReqVO);

    /**
     * 更新就业文章
     *
     * @param updateReqVO 更新信息
     */
    void updateEmploymentEssay(@Valid EmploymentEssaySaveReqVO updateReqVO);

    /**
     * 删除就业文章
     *
     * @param id 编号
     */
    void deleteEmploymentEssay(Long id);

    /**
     * 获得就业文章
     *
     * @param id 编号
     * @return 就业文章
     */
    EmploymentEssayDO getEmploymentEssay(Long id);

    /**
     * 获得就业文章分页
     *
     * @param pageReqVO 分页查询
     * @return 就业文章分页
     */
    PageResult<EmploymentEssayDO> getEmploymentEssayPage(EmploymentEssayPageReqVO pageReqVO);

}