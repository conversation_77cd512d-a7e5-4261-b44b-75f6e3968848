package cn.iocoder.yudao.module.system.controller.admin.ai.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.*;

@Schema(description = "管理后台 - AI 报告新增/修改 Request VO")
@Data
public class UserReportSaveReqVO {

    @Schema(description = "主键id", example = "11550")
    private Long id;

    @Schema(description = "用户id", example = "3368")
    @NotNull(message = "用户id不能为空")
    private Long userId;

    @Schema(description = "问题")
    @NotEmpty(message = "问题不能为空")
    private String question;

    @Schema(description = "内容")
    @NotEmpty(message = "内容不能为空")
    private String content;

    @Schema(description = "类型", example = "2")
    @NotEmpty(message = "类型不能为空")
    private String analysisType;


    @Schema(description = "版本", example = "2")
    @NotNull(message = "版本不能为空")
    private Integer version;

    @Schema(description = "工作台id", example = "2")
    private Integer answerRecordId;

    @Schema(description = "报告名称")
    private String name;
}
