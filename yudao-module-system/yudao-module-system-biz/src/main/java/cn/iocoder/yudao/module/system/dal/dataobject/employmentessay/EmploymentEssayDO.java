package cn.iocoder.yudao.module.system.dal.dataobject.employmentessay;

import lombok.*;
import java.util.*;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.*;
import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;

/**
 * 就业文章 DO
 *
 * <AUTHOR>
 */
@TableName("kf_employment_essay")
@KeySequence("kf_employment_essay_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class EmploymentEssayDO extends BaseDO {

    /**
     * 主键id
     */
    @TableId
    private Long id;

    /**
     * 就业方向名称
     */
    private String dirName;
    /**
     * 就业方向id
     */
    private Integer dirId;
    /**
     * 名称
     */
    private String name;
    /**
     * 对口院校id
     */
    private String schoolId;
    /**
     * 对口院校名称
     */
    private String schoolName;
    /**
     * 文章内容
     */
    private String content;
    /**
     * 状态
     *
     * 枚举 {@link TODO kf_type 对应的类}
     */
    private Integer status;
    /**
     * 排序
     */
    private Integer sort;

}