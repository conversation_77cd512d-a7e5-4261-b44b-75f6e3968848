package cn.iocoder.yudao.module.system.controller.admin.userassetschange;

import cn.iocoder.yudao.module.system.controller.admin.userassetschange.vo.UserAssetsChangePageReqVO;
import cn.iocoder.yudao.module.system.controller.admin.userassetschange.vo.UserAssetsChangeRespVO;
import cn.iocoder.yudao.module.system.controller.admin.userassetschange.vo.UserAssetsChangeSaveReqVO;
import cn.iocoder.yudao.module.system.dal.dataobject.userassetschange.UserAssetsChangeDO;
import cn.iocoder.yudao.module.system.service.userassetschange.UserAssetsChangeService;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import javax.annotation.security.PermitAll;
import javax.validation.*;
import javax.servlet.http.*;
import java.util.*;
import java.io.IOException;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

import cn.iocoder.yudao.framework.excel.core.util.ExcelUtils;

import cn.iocoder.yudao.framework.apilog.core.annotation.ApiAccessLog;
import static cn.iocoder.yudao.framework.apilog.core.enums.OperateTypeEnum.*;


@Tag(name = "管理后台 - 用户资源兑换套餐")
@RestController
@RequestMapping("/system/ai/user-assets-change")
@Validated
public class UserAssetsChangeController {

    @Resource
    private UserAssetsChangeService userAssetsChangeService;

    @PostMapping("/create")
    @Operation(summary = "创建用户资源兑换套餐")
    @PermitAll
    public CommonResult<Integer> createUserAssetsChange(@Valid @RequestBody UserAssetsChangeSaveReqVO createReqVO) {
        return success(userAssetsChangeService.createUserAssetsChange(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新用户资源兑换套餐")
    @PermitAll
    public CommonResult<Boolean> updateUserAssetsChange(@Valid @RequestBody UserAssetsChangeSaveReqVO updateReqVO) {
        userAssetsChangeService.updateUserAssetsChange(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除用户资源兑换套餐")
    @Parameter(name = "id", description = "编号", required = true)
    @PermitAll
    public CommonResult<Boolean> deleteUserAssetsChange(@RequestParam("id") Integer id) {
        userAssetsChangeService.deleteUserAssetsChange(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得用户资源兑换套餐")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PermitAll
    public CommonResult<UserAssetsChangeRespVO> getUserAssetsChange(@RequestParam("id") Integer id) {
        UserAssetsChangeDO userAssetsChange = userAssetsChangeService.getUserAssetsChange(id);
        return success(BeanUtils.toBean(userAssetsChange, UserAssetsChangeRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得用户资源兑换套餐分页")
    @PermitAll
    public CommonResult<PageResult<UserAssetsChangeRespVO>> getUserAssetsChangePage(@Valid UserAssetsChangePageReqVO pageReqVO) {
        PageResult<UserAssetsChangeDO> pageResult = userAssetsChangeService.getUserAssetsChangePage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, UserAssetsChangeRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出用户资源兑换套餐 Excel")
    @PreAuthorize("@ss.hasPermission('ai:user-assets-change:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportUserAssetsChangeExcel(@Valid UserAssetsChangePageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<UserAssetsChangeDO> list = userAssetsChangeService.getUserAssetsChangePage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "用户资源兑换套餐.xls", "数据", UserAssetsChangeRespVO.class,
                        BeanUtils.toBean(list, UserAssetsChangeRespVO.class));
    }

}