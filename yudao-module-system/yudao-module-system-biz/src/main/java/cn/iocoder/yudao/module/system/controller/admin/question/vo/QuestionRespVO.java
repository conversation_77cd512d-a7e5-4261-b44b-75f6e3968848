package cn.iocoder.yudao.module.system.controller.admin.question.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;
import com.alibaba.excel.annotation.*;
import cn.iocoder.yudao.framework.excel.core.annotations.DictFormat;
import cn.iocoder.yudao.framework.excel.core.convert.DictConvert;

@Schema(description = "管理后台 - 问题 Response VO")
@Data
@ExcelIgnoreUnannotated
public class QuestionRespVO {

    @Schema(description = "主键id", requiredMode = Schema.RequiredMode.REQUIRED, example = "14433")
    @ExcelProperty("主键id")
    private Integer id;

    @Schema(description = "问题内容", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("问题内容")
    private String content;

    @Schema(description = "问题类型 1单选题 2多选题 3问答题", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @ExcelProperty(value = "问题类型 1单选题 2多选题 3问答题", converter = DictConvert.class)
    @DictFormat("问题类型") // TODO 代码优化：建议设置到对应的 DictTypeConstants 枚举类中
    private Integer type;

    @Schema(description = "是否必答题 1必答 0 可不回答", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty(value = "是否必答题 1必答 0 可不回答", converter = DictConvert.class)
    @DictFormat("question_is_necessary") // TODO 代码优化：建议设置到对应的 DictTypeConstants 枚举类中
    private Integer isNecessary;

    @Schema(description = "状态 1可用 0 不可用", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @ExcelProperty(value = "状态 1可用 0 不可用", converter = DictConvert.class)
    @DictFormat("question_status") // TODO 代码优化：建议设置到对应的 DictTypeConstants 枚举类中
    private Integer status;

    @Schema(description = "问题顺序", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("问题顺序")
    private Integer sort;

    @Schema(description = "问题创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("问题创建时间")
    private LocalDateTime createTime;

}