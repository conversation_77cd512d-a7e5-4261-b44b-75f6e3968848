package cn.iocoder.yudao.module.system.service.employmentessay;

import cn.iocoder.yudao.framework.common.exception.ErrorCode;
import cn.iocoder.yudao.framework.security.core.util.SecurityFrameworkUtils;
import cn.iocoder.yudao.module.system.controller.admin.employmentessay.vo.EmploymentEssayPageReqVO;
import cn.iocoder.yudao.module.system.controller.admin.employmentessay.vo.EmploymentEssaySaveReqVO;
import cn.iocoder.yudao.module.system.controller.admin.user.vo.user.UserAssetsSaveReqVO;
import cn.iocoder.yudao.module.system.dal.dataobject.employmentessay.EmploymentEssayDO;
import cn.iocoder.yudao.module.system.dal.dataobject.user.UserAssetsDO;
import cn.iocoder.yudao.module.system.dal.mysql.employmentessay.EmploymentEssayMapper;
import cn.iocoder.yudao.module.system.service.user.UserAssetsService;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;

import java.time.LocalDateTime;
import java.util.*;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;


import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;

/**
 * 就业文章 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class EmploymentEssayServiceImpl implements EmploymentEssayService {

    @Resource
    private EmploymentEssayMapper employmentEssayMapper;

    @Resource
    private UserAssetsService userAssetsService;

    @Override
    public Long createEmploymentEssay(EmploymentEssaySaveReqVO createReqVO) {
        // 插入
        EmploymentEssayDO employmentEssay = BeanUtils.toBean(createReqVO, EmploymentEssayDO.class);
        employmentEssayMapper.insert(employmentEssay);
        // 返回
        return employmentEssay.getId();
    }

    @Override
    public void updateEmploymentEssay(EmploymentEssaySaveReqVO updateReqVO) {
        // 校验存在
        validateEmploymentEssayExists(updateReqVO.getId());
        // 更新
        EmploymentEssayDO updateObj = BeanUtils.toBean(updateReqVO, EmploymentEssayDO.class);
        employmentEssayMapper.updateById(updateObj);
    }

    @Override
    public void deleteEmploymentEssay(Long id) {
        // 校验存在
        validateEmploymentEssayExists(id);
        // 删除
        employmentEssayMapper.deleteById(id);
    }

    private void validateEmploymentEssayExists(Long id) {
        if (employmentEssayMapper.selectById(id) == null) {
            throw exception(new ErrorCode(404,"就业文章不存在"));
        }
    }

    @Override
    public EmploymentEssayDO getEmploymentEssay(Long id) {
        // 获取当前登录用户ID
        Long userId = SecurityFrameworkUtils.getLoginUserId();

        // 检查用户内容会员剩余次数
        userAssetsService.validateUserContentMembership(userId);

        return employmentEssayMapper.selectById(id);
    }



    @Override
    public PageResult<EmploymentEssayDO> getEmploymentEssayPage(EmploymentEssayPageReqVO pageReqVO) {
        // 获取用户ID，如果请求中没有提供，则尝试从当前登录用户获取
        Long userId = pageReqVO.getUserId();
        if (userId == null) {
            userId = SecurityFrameworkUtils.getLoginUserId();
            // 设置回请求对象，方便后续使用
            pageReqVO.setUserId(userId);
        }

        // 检查用户内容会员剩余次数
        userAssetsService.validateUserContentMembership(userId);

        // 查询文章列表
        return employmentEssayMapper.selectPage(pageReqVO);
    }

}