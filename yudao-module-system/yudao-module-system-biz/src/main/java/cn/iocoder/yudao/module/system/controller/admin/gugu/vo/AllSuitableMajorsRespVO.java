package cn.iocoder.yudao.module.system.controller.admin.gugu.vo;

import cn.iocoder.yudao.module.system.controller.admin.gugu.gugu.MajorAdmissionInfo;
import cn.iocoder.yudao.module.system.controller.admin.gugu.gugu.UserProfileInfo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * 所有适合专业响应 VO
 */
@Schema(description = "管理后台 - 所有适合专业响应 VO")
@Data
public class AllSuitableMajorsRespVO {

    @Schema(description = "用户个人信息")
    private UserProfileInfo userProfile;

    @Schema(description = "所有适合的专业列表")
    private List<MajorAdmissionInfo> allSuitableMajors;

    @Schema(description = "专业总数")
    private Integer totalCount;

    @Schema(description = "查询信息说明")
    private String queryInfo;
}
