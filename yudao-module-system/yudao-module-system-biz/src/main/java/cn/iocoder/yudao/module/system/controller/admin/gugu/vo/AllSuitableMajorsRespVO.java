package cn.iocoder.yudao.module.system.controller.admin.gugu.vo;

import cn.iocoder.yudao.module.system.controller.admin.gugu.gugu.MajorAdmissionInfo;
import cn.iocoder.yudao.module.system.controller.admin.gugu.gugu.UserProfileInfo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * 所有适合专业响应 VO（按冲稳保分类）
 */
@Schema(description = "管理后台 - 所有适合专业响应 VO（按冲稳保分类）")
@Data
public class AllSuitableMajorsRespVO {

    @Schema(description = "用户个人信息")
    private UserProfileInfo userProfile;

    @Schema(description = "冲刺专业列表（分数高于用户分数）")
    private List<MajorAdmissionInfo> rushMajors;

    @Schema(description = "稳妥专业列表（分数接近用户分数）")
    private List<MajorAdmissionInfo> stableMajors;

    @Schema(description = "保底专业列表（分数低于用户分数）")
    private List<MajorAdmissionInfo> safeMajors;

    @Schema(description = "冲刺专业数量")
    private Integer rushCount;

    @Schema(description = "稳妥专业数量")
    private Integer stableCount;

    @Schema(description = "保底专业数量")
    private Integer safeCount;

    @Schema(description = "专业总数")
    private Integer totalCount;

    @Schema(description = "查询信息说明")
    private String queryInfo;
}
