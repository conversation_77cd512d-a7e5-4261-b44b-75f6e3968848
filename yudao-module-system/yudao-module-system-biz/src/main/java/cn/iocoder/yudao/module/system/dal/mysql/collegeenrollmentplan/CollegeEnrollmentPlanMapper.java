package cn.iocoder.yudao.module.system.dal.mysql.collegeenrollmentplan;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.module.system.dal.dataobject.collegeenrollmentplan.CollegeEnrollmentPlanDO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 历年高校招生计划数据 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface CollegeEnrollmentPlanMapper extends BaseMapperX<CollegeEnrollmentPlanDO> {

    /**
     * 根据省份和年份查询招生计划数据
     * @param provinceName 省份名称
     * @param year 年份
     * @return 招生计划数据列表
     */
    default List<CollegeEnrollmentPlanDO> selectListByProvinceAndYear(String provinceName, Integer year) {
        return selectList(new LambdaQueryWrapperX<CollegeEnrollmentPlanDO>()
                .eq(CollegeEnrollmentPlanDO::getProvinceName, provinceName)
                .eq(CollegeEnrollmentPlanDO::getYear, year));
    }

    /**
     * 根据学校名称和专业名称查询招生计划数据
     * @param schoolName 学校名称
     * @param majorName 专业名称
     * @param provinceName 省份名称
     * @param year 年份
     * @return 招生计划数据列表
     */
    default List<CollegeEnrollmentPlanDO> selectListBySchoolAndMajor(String schoolName, String majorName, 
                                                                    String provinceName, Integer year) {
        return selectList(new LambdaQueryWrapperX<CollegeEnrollmentPlanDO>()
                .likeIfPresent(CollegeEnrollmentPlanDO::getSchoolName, schoolName)
                .likeIfPresent(CollegeEnrollmentPlanDO::getCollegeMajorName, majorName)
                .eqIfPresent(CollegeEnrollmentPlanDO::getProvinceName, provinceName)
                .eqIfPresent(CollegeEnrollmentPlanDO::getYear, year));
    }

    /**
     * 检查是否存在重复数据
     * @param provinceName 省份名称
     * @param schoolUuid 学校UUID
     * @param majorName 专业名称
     * @param year 年份
     * @param type 类型
     * @param batchName 批次名称
     * @return 是否存在
     */
    default boolean existsByUniqueKey(String provinceName, String schoolUuid, String majorName, 
                                     Integer year, String type, String batchName) {
        return selectCount(new LambdaQueryWrapperX<CollegeEnrollmentPlanDO>()
                .eq(CollegeEnrollmentPlanDO::getProvinceName, provinceName)
                .eq(CollegeEnrollmentPlanDO::getSchoolUuid, schoolUuid)
                .eq(CollegeEnrollmentPlanDO::getCollegeMajorName, majorName)
                .eq(CollegeEnrollmentPlanDO::getYear, year)
                .eqIfPresent(CollegeEnrollmentPlanDO::getType, type)
                .eqIfPresent(CollegeEnrollmentPlanDO::getBatchName, batchName)) > 0;
    }

    /**
     * 根据年份统计数据量
     * @param year 年份
     * @return 数据量
     */
    default Long countByYear(Integer year) {
        return selectCount(new LambdaQueryWrapperX<CollegeEnrollmentPlanDO>()
                .eq(CollegeEnrollmentPlanDO::getYear, year));
    }

    /**
     * 根据省份统计数据量
     * @param provinceName 省份名称
     * @return 数据量
     */
    default Long countByProvince(String provinceName) {
        return selectCount(new LambdaQueryWrapperX<CollegeEnrollmentPlanDO>()
                .eq(CollegeEnrollmentPlanDO::getProvinceName, provinceName));
    }

}
