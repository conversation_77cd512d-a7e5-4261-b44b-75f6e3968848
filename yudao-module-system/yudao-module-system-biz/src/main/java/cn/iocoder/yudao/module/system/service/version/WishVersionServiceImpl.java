package cn.iocoder.yudao.module.system.service.version;

import cn.iocoder.yudao.module.system.controller.admin.version.vo.WishVersionPageReqVO;
import cn.iocoder.yudao.module.system.controller.admin.version.vo.WishVersionSaveReqVO;
import cn.iocoder.yudao.module.system.dal.dataobject.version.WishVersionDO;
import cn.iocoder.yudao.module.system.dal.mysql.version.WishVersionMapper;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;


import javax.annotation.Resource;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.module.system.enums.ErrorCodeConstants.WISH_VERSION_NOT_EXISTS;

/**
 * 志愿咨询题库版本表	 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class WishVersionServiceImpl implements WishVersionService {

    @Resource
    private WishVersionMapper wishVersionMapper;

    @Override
    public Integer createWishVersion(WishVersionSaveReqVO createReqVO) {
        // 插入
        WishVersionDO wishVersion = BeanUtils.toBean(createReqVO, WishVersionDO.class);
        wishVersionMapper.insert(wishVersion);
        // 返回
        return wishVersion.getId();
    }

    @Override
    public void updateWishVersion(WishVersionSaveReqVO updateReqVO) {
        // 校验存在
        validateWishVersionExists(updateReqVO.getId());
        // 更新
        WishVersionDO updateObj = BeanUtils.toBean(updateReqVO, WishVersionDO.class);
        wishVersionMapper.updateById(updateObj);
    }

    @Override
    public void deleteWishVersion(Integer id) {
        // 校验存在
        validateWishVersionExists(id);
        // 删除
        wishVersionMapper.deleteById(id);
    }

    private void validateWishVersionExists(Integer id) {
        if (wishVersionMapper.selectById(id) == null) {
            throw exception(WISH_VERSION_NOT_EXISTS);
        }
    }

    @Override
    public WishVersionDO getWishVersion(Integer id) {
        return wishVersionMapper.selectById(id);
    }

    @Override
    public PageResult<WishVersionDO> getWishVersionPage(WishVersionPageReqVO pageReqVO) {
        return wishVersionMapper.selectPage(pageReqVO);
    }

}