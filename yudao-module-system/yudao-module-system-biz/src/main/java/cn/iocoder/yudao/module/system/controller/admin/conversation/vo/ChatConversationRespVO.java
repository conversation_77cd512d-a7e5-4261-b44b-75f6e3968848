package cn.iocoder.yudao.module.system.controller.admin.conversation.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.time.LocalDateTime;
import com.alibaba.excel.annotation.*;

@Schema(description = "管理后台 - AI 聊天对话 Response VO")
@Data
@ExcelIgnoreUnannotated
public class ChatConversationRespVO {

    @Schema(description = "对话编号",  example = "27630")
    @ExcelProperty("对话编号")
    private Long id;

    @Schema(description = "用户编号",  example = "25513")
    @ExcelProperty("用户编号")
    private Long userId;

    @Schema(description = "聊天角色", example = "21425")
    @ExcelProperty("聊天角色")
    private Long roleId;

    @Schema(description = "对话标题" )
    @ExcelProperty("对话标题")
    private String title;

    @Schema(description = "模型编号",  example = "23853")
    @ExcelProperty("模型编号")
    private Long modelId;

    @Schema(description = "模型标识")
    @ExcelProperty("模型标识")
    private String model;

    @Schema(description = "是否置顶" )
    @ExcelProperty("是否置顶")
    private Boolean pinned;

    @Schema(description = "置顶时间")
    @ExcelProperty("置顶时间")
    private LocalDateTime pinnedTime;

    @Schema(description = "角色设定")
    @ExcelProperty("角色设定")
    private String systemMessage;

    @Schema(description = "温度参数" )
    @ExcelProperty("温度参数")
    private Double temperature;

    @Schema(description = "流式返回的回答内容")
    @ExcelProperty("流式返回的回答内容")
    private String content;

    @Schema(description = "单条回复的最大 Token 数量" )
    @ExcelProperty("单条回复的最大 Token 数量")
    private Integer maxTokens;

    @Schema(description = "上下文的最大 Message 数量" )
    @ExcelProperty("上下文的最大 Message 数量")
    private Integer maxContexts;

    @Schema(description = "创建时间")
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}