package cn.iocoder.yudao.module.system.controller.admin.gugu.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Map;

/**
 * 结构化用户数据请求 VO
 */
@Schema(description = "结构化用户数据请求参数")
@Data
public class StructuredUserDataReqVO {

    @Schema(description = "用户问答数据", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "用户问答数据不能为空")
    private Map<String, QuestionAnswer> userAnswers;

    /**
     * 问答数据内部类
     */
    @Schema(description = "问答数据")
    @Data
    public static class QuestionAnswer {
        
        @Schema(description = "问题内容", example = "学生所处的高考省份")
        private String questionContent;
        
        @Schema(description = "答案", example = "辽宁")
        private Object answer; // 可能是字符串、数组等不同类型
    }
}
