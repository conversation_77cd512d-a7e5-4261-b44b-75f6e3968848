package cn.iocoder.yudao.module.system.service.gugu;

import cn.iocoder.yudao.module.system.controller.admin.gugu.gugu.CollegeEnrollmentPlanInfo;
import cn.iocoder.yudao.module.system.controller.admin.gugu.gugu.CollegeEnrollmentPlanQueryReqVO;
import cn.iocoder.yudao.module.system.dal.dataobject.collegeenrollmentplan.CollegeEnrollmentPlanDO;

import java.util.List;
import java.util.Map;

/**
 * 高校招生计划服务接口
 */
public interface CollegeEnrollmentPlanService {

    /**
     * 查询历年高校招生计划数据（带缓存）
     * @param reqVO 查询参数
     * @return 历年高校招生计划数据和总数量
     */
    Map<String, Object> getCollegeEnrollmentPlanInfo(CollegeEnrollmentPlanQueryReqVO reqVO);

    /**
     * 查询单个专业的招生计划数据（带缓存）
     * @param schoolName 学校名称
     * @param majorName 专业名称
     * @param provinceName 省份名称
     * @param typeName 类型名称
     * @param year 年份
     * @return 招生计划数据列表
     */
    List<CollegeEnrollmentPlanInfo> getEnrollmentPlanForMajor(String schoolName, String majorName,
                                                             String provinceName, String typeName, Integer year);

    /**
     * 批量导入历年高校招生计划数据
     * @param years 年份列表
     * @param useMultiThread 是否使用多线程
     * @return 导入结果统计
     */
    Map<String, Object> batchImportEnrollmentPlanData(List<Integer> years, boolean useMultiThread);

    /**
     * 按省份导入指定年份的招生计划数据
     * @param provinceName 省份名称
     * @param year 年份
     * @return 导入数据条数
     */
    int importEnrollmentPlanDataByProvince(String provinceName, Integer year);

    /**
     * 从数据库查询招生计划数据
     * @param schoolName 学校名称
     * @param majorName 专业名称
     * @param provinceName 省份名称
     * @param year 年份
     * @return 招生计划数据列表
     */
    List<CollegeEnrollmentPlanDO> getEnrollmentPlanFromDatabase(String schoolName, String majorName,
                                                               String provinceName, Integer year);

    /**
     * 获取导入进度统计
     * @return 进度统计信息
     */
    Map<String, Object> getImportProgress();
}
