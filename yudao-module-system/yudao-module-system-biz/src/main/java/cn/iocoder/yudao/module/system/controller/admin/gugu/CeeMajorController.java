package cn.iocoder.yudao.module.system.controller.admin.gugu;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.system.controller.admin.gugu.convert.CeeMajorConvert;
import cn.iocoder.yudao.module.system.controller.admin.gugu.gugu.*;
import cn.iocoder.yudao.module.system.controller.admin.gugu.vo.CeeMajorPageReqVO;
import cn.iocoder.yudao.module.system.controller.admin.gugu.vo.CeeMajorRespVO;
import cn.iocoder.yudao.module.system.dal.dataobject.gugu.CeeMajorDO;
import cn.iocoder.yudao.module.system.service.gugu.CeeMajorService;
import cn.iocoder.yudao.module.system.service.gugu.MajorRecommendService;
import cn.iocoder.yudao.module.system.service.gugu.UserProfileService;
import org.springframework.util.StringUtils;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;
import cn.iocoder.yudao.module.system.util.gugu.GuGuDataUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.annotation.security.PermitAll;
import javax.validation.Valid;
import java.util.List;
import java.util.Map;

import static cn.iocoder.yudao.framework.web.core.util.WebFrameworkUtils.getLoginUserId;

/**
 * 大学高校专业数据查询控制器
 */
@Tag(name = "管理后台 - 大学高校专业数据查询")
@RestController
@RequestMapping("/system/metadata/ceemajor")
@Validated
@Slf4j
public class CeeMajorController {

    @Resource
    private UserProfileService userProfileService;

    @Resource
    private CeeMajorService ceeMajorService;

    @Resource
    private MajorRecommendService majorRecommendService;

    @GetMapping("query")
    @Operation(summary = "查询大学高校专业数据")
    @PermitAll
    public CommonResult<CeeMajorQueryRespVO> queryCeeMajor(
            @Parameter(description = "APPKEY", required = true) @RequestParam(value = "appkey") String appkey,
            @Parameter(description = "搜索关键字，模糊匹配专业名称、学科、专业介绍、开设课程") @RequestParam(value = "keywords", required = false) String keywords,
            @Parameter(description = "每页数据量，参数最大值为20，不传默认获取所有数据") @RequestParam(value = "pagesize", required = false) Integer pagesize,
            @Parameter(description = "页码，第几页数据，第一页从1开始，不传默认获取所有数据") @RequestParam(value = "pageindex", required = false) Integer pageindex) {

        // 验证appkey是否有效
        if (!GuGuDataUtils.validateCeeMajorApiKey(appkey)) {
            return CommonResult.error(403, "无效的APPKEY");
        }

        //如果keywords中包含括号 则去掉括号及括号中的内容
        if (keywords != null && keywords.contains("(")) {
            keywords = keywords.replaceAll("\\([^\\)]*\\)", "");
        }

        // 构建请求参数
        CeeMajorQueryReqVO reqVO = new CeeMajorQueryReqVO();
        reqVO.setKeywords(keywords);

        Map<String, Object> result;

        // 判断是否需要获取所有数据
        boolean fetchAllData = (pagesize == null || pageindex == null);

        if (fetchAllData) {
            // 如果没有传递分页参数，自动获取所有数据
            result = GuGuDataUtils.getCeeMajorInfoAll(reqVO);
        } else {
            // 如果传递了分页参数，使用普通分页查询
            reqVO.setPageSize(pagesize);
            reqVO.setPageIndex(pageindex);
            result = GuGuDataUtils.getCeeMajorInfo(reqVO);
        }

        // 判断是否查询成功
        Boolean success = (Boolean) result.get("success");
        if (Boolean.TRUE.equals(success)) {
            // 构建响应VO
            CeeMajorQueryRespVO respVO = new CeeMajorQueryRespVO();
            respVO.setMajorList((List<CeeMajorInfo>) result.get("majorList"));
            respVO.setTotalCount((Integer) result.get("totalCount"));
            respVO.setPageIndex((Integer) result.get("pageIndex"));
            respVO.setPageSize((Integer) result.get("pageSize"));
            respVO.setTotalPages((Integer) result.get("totalPages"));

            // 如果是所有数据，添加标记
            if (Boolean.TRUE.equals(result.get("isAllData"))) {
                respVO.setIsAllData(true);
            }

            // 暂时注释掉保存数据到数据库的代码
            /*
            List<CeeMajorInfo> majorList = respVO.getMajorList();
            try {
                int savedCount = ceeMajorService.saveMajorInfoList(majorList);
                log.info("成功保存{}条专业数据到数据库", savedCount);
            } catch (Exception e) {
                log.error("保存专业数据到数据库失败", e);
                // 数据保存失败不影响API正常返回结果
            }
            */

            return CommonResult.success(respVO);
        } else {
            String message = (String) result.get("message");
            return CommonResult.error(400, message);
        }
    }

    @PostMapping("/recommend")
    @Operation(summary = "根据用户信息推荐专业")
    @PermitAll
    public CommonResult<MajorRecommendationRespVO> recommendMajors(@RequestBody @Valid MajorRecommendationReqVO reqVO) {
        // 从用户输入中提取个人信息
        UserProfileInfo userProfile = userProfileService.extractUserProfile(reqVO.getUserInput());

        // 根据用户信息推荐专业
        Map<String, Object> recommendationResult = userProfileService.recommendMajors(userProfile);

        // 构建响应VO
        MajorRecommendationRespVO respVO = new MajorRecommendationRespVO();
        respVO.setUserProfile(userProfile);
        respVO.setHigherScoreMajors((List<MajorAdmissionInfo>) recommendationResult.get("higherScoreMajors"));
        respVO.setEqualScoreMajors((List<MajorAdmissionInfo>) recommendationResult.get("equalScoreMajors"));
        respVO.setLowerScoreMajors((List<MajorAdmissionInfo>) recommendationResult.get("lowerScoreMajors"));
        respVO.setTotalCount((Integer) recommendationResult.get("totalCount"));
        respVO.setRecommendationReason((String) recommendationResult.get("recommendationReason"));

        return CommonResult.success(respVO);
    }

    @PostMapping("/all-suitable")
    @Operation(summary = "根据用户信息获取所有适合的专业（不进行筛选限制）")
    @PermitAll
    public CommonResult<AllSuitableMajorsRespVO> getAllSuitableMajors(@RequestBody @Valid MajorRecommendationReqVO reqVO) {
        // 从用户输入中提取个人信息
        UserProfileInfo userProfile = userProfileService.extractUserProfile(reqVO.getUserInput());

        // 获取所有适合的专业
        Map<String, Object> result = userProfileService.getAllSuitableMajors(userProfile);

        // 构建响应VO
        AllSuitableMajorsRespVO respVO = new AllSuitableMajorsRespVO();
        respVO.setUserProfile(userProfile);
        respVO.setAllSuitableMajors((List<MajorAdmissionInfo>) result.get("allSuitableMajors"));
        respVO.setTotalCount((Integer) result.get("totalCount"));
        respVO.setQueryInfo((String) result.get("queryInfo"));

        return CommonResult.success(respVO);
    }

    @GetMapping("/page")
    @Operation(summary = "分页查询专业数据")
    @PermitAll
    public CommonResult<PageResult<CeeMajorRespVO>> getMajorPage(CeeMajorPageReqVO reqVO) {
        // 从数据库中查询专业数据

        String exactMajorName = reqVO.getExactMajorName();

        if (exactMajorName != null) {
            // 如果exactMajorName中包含括号，则去掉括号及括号中的内容
            if (exactMajorName.contains("(")) {
               exactMajorName = exactMajorName.replaceAll("\\([^\\)]*\\)", "").trim();
            }

            // 如果exactMajorName是描述性文本，尝试提取"的"后面的专业名称
            // 例如："详细介绍北京大学的软件工程" -> "软件工程"
            if (exactMajorName.contains("的")) {
                String[] parts = exactMajorName.split("的");
                if (parts.length > 1) {
                    // 取"的"后面的文本作为专业名称
                    exactMajorName = parts[parts.length - 1].trim();
                }
            }

            // 更新处理后的专业名称
            reqVO.setExactMajorName(exactMajorName);
        }

        PageResult<CeeMajorDO> pageResult = ceeMajorService.getMajorPage(reqVO);
        // 转换为 VO 对象
        return CommonResult.success(CeeMajorConvert.INSTANCE.convertPage(pageResult));
    }

    @GetMapping("/get")
    @Operation(summary = "获取专业详情")
    @PermitAll
    public CommonResult<CeeMajorRespVO> getMajor(@RequestParam("id") Long id) {
        // 从数据库中查询专业详情
        CeeMajorDO major = ceeMajorService.getMajor(id);
        // 转换为 VO 对象
        return CommonResult.success(CeeMajorConvert.INSTANCE.convert(major));
    }

    @GetMapping("/tree")
    @Operation(summary = "获取专业树形结构")
    @PermitAll
    public CommonResult<Map<String, Object>> getMajorTree(
            @Parameter(description = "教育级别", example = "本科") @RequestParam(value = "educationLevel", required = false) String educationLevel,
            @Parameter(description = "学科门类 ID，当传递时只加载该门类下的数据") @RequestParam(value = "categoryId", required = false) String categoryId,
            @Parameter(description = "专业名称，模糊搜索", example = "计算机") @RequestParam(value = "majorName", required = false) String majorName) {
        // 获取专业树形结构，采用懒加载方式，支持专业名称搜索
        Map<String, Object> result = ceeMajorService.getMajorTree(educationLevel, categoryId, majorName);
        return CommonResult.success(result);
    }

    @GetMapping("/recommended-tree")
    @Operation(summary = "获取用户推荐专业的树形结构")
    @PermitAll
    public CommonResult<Map<String, Object>> getRecommendedMajorTree(
            @Parameter(description = "教育级别", example = "本科") @RequestParam(value = "educationLevel", required = false) String educationLevel,
            @Parameter(description = "学科门类 ID，当传递时只加载该门类下的数据") @RequestParam(value = "categoryId", required = false) String categoryId) {
        // 获取用户推荐专业的树形结构

        Long userId = getLoginUserId();

        Map<String, Object> result = ceeMajorService.getMajorTree(userId, educationLevel, categoryId);
        return CommonResult.success(result);
    }

    @GetMapping("/categories")
    @Operation(summary = "获取学科门类列表")
    @PermitAll
    public CommonResult<List<String>> getDisciplinaryCategories(
            @Parameter(description = "教育级别", example = "本科") @RequestParam(value = "educationLevel", required = false) String educationLevel) {
        // 获取学科门类列表
        List<String> categories = ceeMajorService.getDisciplinaryCategories(educationLevel);
        return CommonResult.success(categories);
    }

    @GetMapping("/subcategories")
    @Operation(summary = "获取学科子类列表")
    @PermitAll
    public CommonResult<List<String>> getDisciplinarySubCategories(
            @Parameter(description = "学科门类", required = true) @RequestParam("disciplinaryCategory") String disciplinaryCategory,
            @Parameter(description = "教育级别", example = "本科") @RequestParam(value = "educationLevel", required = false) String educationLevel) {
        // 获取学科子类列表
        List<String> subCategories = ceeMajorService.getDisciplinarySubCategories(disciplinaryCategory, educationLevel);
        return CommonResult.success(subCategories);
    }

    @GetMapping("/majors-by-subcategory")
    @Operation(summary = "获取指定学科子类下的专业列表")
    @PermitAll
    public CommonResult<List<CeeMajorRespVO>> getMajorsBySubCategory(
            @Parameter(description = "学科门类", required = true) @RequestParam("disciplinaryCategory") String disciplinaryCategory,
            @Parameter(description = "学科子类", required = true) @RequestParam("disciplinarySubCategory") String disciplinarySubCategory,
            @Parameter(description = "教育级别", example = "本科") @RequestParam(value = "educationLevel", required = false) String educationLevel) {
        // 获取专业列表
        List<CeeMajorDO> majors = ceeMajorService.getMajorsBySubCategory(disciplinaryCategory, disciplinarySubCategory, educationLevel,"");
        // 转换为 VO 对象
        return CommonResult.success(CeeMajorConvert.INSTANCE.convertList(majors));
    }

    @PostMapping("/process-recommend-data")
    @Operation(summary = "处理专业推荐数据")
    @PermitAll
    public CommonResult<String> processMajorRecommendData() {
        try {
            String result = majorRecommendService.processMajorRecommendData();
            return CommonResult.success(result);
        } catch (Exception e) {
            log.error("处理专业推荐数据失败", e);
            return CommonResult.error(500, "处理失败: " + e.getMessage());
        }
    }

    @PostMapping("/reset-recommend-status")
    @Operation(summary = "重置所有专业的推荐状态")
    @PermitAll
    public CommonResult<String> resetRecommendStatus() {
        try {
            majorRecommendService.resetAllRecommendStatus();
            return CommonResult.success("重置推荐状态成功");
        } catch (Exception e) {
            log.error("重置推荐状态失败", e);
            return CommonResult.error(500, "重置失败: " + e.getMessage());
        }
    }

    @GetMapping("/recommended-majors")
    @Operation(summary = "获取推荐专业列表")
    @PermitAll
    public CommonResult<List<CeeMajorRespVO>> getRecommendedMajors(
            @Parameter(description = "教育级别", example = "本科") @RequestParam(value = "educationLevel", required = false) String educationLevel) {
        // 查询推荐专业
        List<CeeMajorDO> recommendedMajors = ceeMajorService.getRecommendedMajors(educationLevel);

        // 转换为 VO 对象
        return CommonResult.success(CeeMajorConvert.INSTANCE.convertList(recommendedMajors));
    }

    @GetMapping("/test-major-details/{id}")
    @Operation(summary = "测试专业详细信息返回")
    @PermitAll
    public CommonResult<CeeMajorRespVO> testMajorDetails(@PathVariable("id") Long id) {
        try {
            // 查询专业详情
            CeeMajorDO major = ceeMajorService.getMajor(id);

            // 转换为 VO 对象
            CeeMajorRespVO respVO = CeeMajorConvert.INSTANCE.convert(major);

            // 记录返回的字段信息
            log.info("返回专业详情 - ID: {}, 名称: {}, 毕业规模: {}, 男女比例: {}, 推荐状态: {}, 就业方向: {}, 推荐院校数量: {}, 课程数量: {}",
                    respVO.getId(), respVO.getMajorName(), respVO.getGraduateScale(),
                    respVO.getMaleFemaleRatio(), respVO.getIsRecommended(), respVO.getCareerDirection(),
                    respVO.getRecommendSchools() != null ? respVO.getRecommendSchools().size() : 0,
                    respVO.getCourses() != null ? respVO.getCourses().size() : 0);

            return CommonResult.success(respVO);
        } catch (Exception e) {
            log.error("查询专业详情失败", e);
            return CommonResult.error(500, "查询失败: " + e.getMessage());
        }
    }

    @GetMapping("/test-career-directions")
    @Operation(summary = "测试多个就业方向的专业")
    @PermitAll
    public CommonResult<List<CeeMajorRespVO>> testCareerDirections() {
        // 查询有就业方向的推荐专业
        List<CeeMajorDO> majorsWithCareer = ceeMajorService.getRecommendedMajors(null)
                .stream()
                .filter(major -> StringUtils.hasText(major.getCareerDirection()))
                .limit(10) // 限制返回10个
                .collect(Collectors.toList());

        // 转换为 VO 对象
        List<CeeMajorRespVO> respVOs = CeeMajorConvert.INSTANCE.convertList(majorsWithCareer);

        // 记录就业方向信息
        for (CeeMajorRespVO respVO : respVOs) {
            if (StringUtils.hasText(respVO.getCareerDirection())) {
                String[] directions = respVO.getCareerDirection().split(";");
                log.info("专业: {} 有 {} 个就业方向: {}",
                        respVO.getMajorName(), directions.length, Arrays.toString(directions));
            }
        }

        return CommonResult.success(respVOs);
    }
}
