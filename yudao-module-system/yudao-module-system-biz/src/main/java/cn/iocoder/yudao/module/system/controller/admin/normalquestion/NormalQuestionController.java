package cn.iocoder.yudao.module.system.controller.admin.normalquestion;

import cn.iocoder.yudao.module.system.controller.admin.normalquestion.vo.NormalQuestionPageReqVO;
import cn.iocoder.yudao.module.system.controller.admin.normalquestion.vo.NormalQuestionRespVO;
import cn.iocoder.yudao.module.system.controller.admin.normalquestion.vo.NormalQuestionSaveReqVO;
import cn.iocoder.yudao.module.system.dal.dataobject.normalquestion.NormalQuestionDO;
import cn.iocoder.yudao.module.system.service.normalquestion.NormalQuestionService;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import java.util.*;
import java.io.IOException;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

import cn.iocoder.yudao.framework.excel.core.util.ExcelUtils;

import cn.iocoder.yudao.framework.apilog.core.annotation.ApiAccessLog;

import javax.annotation.Resource;
import javax.annotation.security.PermitAll;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;

import static cn.iocoder.yudao.framework.apilog.core.enums.OperateTypeEnum.*;


@Tag(name = "管理后台 - AI 报告")
@RestController
@RequestMapping("/system/ai/normal-question")
@Validated
public class NormalQuestionController {

    @Resource
    private NormalQuestionService normalQuestionService;

    @PostMapping("/create")
    @Operation(summary = "创建AI 报告")
    @PermitAll
    public CommonResult<Long> createNormalQuestion(@Valid @RequestBody NormalQuestionSaveReqVO createReqVO) {
        return success(normalQuestionService.createNormalQuestion(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新AI 报告")
    @PermitAll
    public CommonResult<Boolean> updateNormalQuestion(@Valid @RequestBody NormalQuestionSaveReqVO updateReqVO) {
        normalQuestionService.updateNormalQuestion(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除AI 报告")
    @Parameter(name = "id", description = "编号", required = true)
    @PermitAll
    public CommonResult<Boolean> deleteNormalQuestion(@RequestParam("id") Long id) {
        normalQuestionService.deleteNormalQuestion(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得AI 报告")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PermitAll
    public CommonResult<NormalQuestionRespVO> getNormalQuestion(@RequestParam("id") Long id) {
        NormalQuestionDO normalQuestion = normalQuestionService.getNormalQuestion(id);
        return success(BeanUtils.toBean(normalQuestion, NormalQuestionRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得AI 报告分页")
    @PermitAll
    public CommonResult<PageResult<NormalQuestionRespVO>> getNormalQuestionPage(@Valid NormalQuestionPageReqVO pageReqVO) {
        PageResult<NormalQuestionDO> pageResult = normalQuestionService.getNormalQuestionPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, NormalQuestionRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出AI 报告 Excel")
    @PermitAll
    @ApiAccessLog(operateType = EXPORT)
    public void exportNormalQuestionExcel(@Valid NormalQuestionPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<NormalQuestionDO> list = normalQuestionService.getNormalQuestionPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "AI 报告.xls", "数据", NormalQuestionRespVO.class,
                        BeanUtils.toBean(list, NormalQuestionRespVO.class));
    }

}