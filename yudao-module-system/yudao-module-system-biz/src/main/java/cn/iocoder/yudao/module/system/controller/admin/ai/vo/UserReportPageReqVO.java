package cn.iocoder.yudao.module.system.controller.admin.ai.vo;
import lombok.*;
import java.util.*;
import io.swagger.v3.oas.annotations.media.Schema;
import cn.iocoder.yudao.framework.common.pojo.PageParam;

@Schema(description = "管理后台 - AI 报告分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class UserReportPageReqVO extends PageParam {

    @Schema(description = "用户id", example = "3368")
    private Long userId;

    @Schema(description = "内容")
    private String content;

    @Schema(description = "类型", example = "2")
    private String analysisType;

    @Schema(description = "问题", example = "2")
    private String question;

    @Schema(description = "版本", example = "2")
    private String version;

    @Schema(description = "工作台id", example = "2")
    private Integer answerRecordId;

    @Schema(description = "报告名称")
    private String name;
}
