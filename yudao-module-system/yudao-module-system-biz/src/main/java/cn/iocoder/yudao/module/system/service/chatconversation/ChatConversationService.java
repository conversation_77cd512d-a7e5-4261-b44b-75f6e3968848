package cn.iocoder.yudao.module.system.service.chatconversation;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.system.controller.admin.conversation.vo.ChatConversationPageReqVO;
import cn.iocoder.yudao.module.system.controller.admin.conversation.vo.ChatConversationRespVO;
import cn.iocoder.yudao.module.system.controller.admin.conversation.vo.ChatConversationSaveReqVO;
import cn.iocoder.yudao.module.system.dal.dataobject.chatconversation.ChatConversationDO;
// 移除 AppBuilderServerException 的导入
import reactor.core.publisher.Flux;

import javax.validation.Valid;
import java.io.IOException;

/**
 * AI 聊天对话 Service 接口
 *
 * <AUTHOR>
 */
public interface ChatConversationService {

    /**
     * 创建AI 聊天对话
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Flux<CommonResult<ChatConversationRespVO>> createChatConversation(@Valid ChatConversationSaveReqVO createReqVO) throws IOException;

    /**
     * 更新AI 聊天对话
     *
     * @param updateReqVO 更新信息
     */
    void updateChatConversation(@Valid ChatConversationSaveReqVO updateReqVO);

    /**
     * 删除AI 聊天对话
     *
     * @param id 编号
     */
    void deleteChatConversation(Long id);

    /**
     * 获得AI 聊天对话
     *
     * @param id 编号
     * @return AI 聊天对话
     */
    ChatConversationDO getChatConversation(Long id);

    /**
     * 获得AI 聊天对话分页
     *
     * @param pageReqVO 分页查询
     * @return AI 聊天对话分页
     */
    PageResult<ChatConversationDO> getChatConversationPage(ChatConversationPageReqVO pageReqVO);

}