package cn.iocoder.yudao.module.system.controller.admin.userassetscode;

import cn.iocoder.yudao.module.system.controller.admin.userassetscode.vo.UserAssetsCodePageReqVO;
import cn.iocoder.yudao.module.system.controller.admin.userassetscode.vo.UserAssetsCodeRespVO;
import cn.iocoder.yudao.module.system.controller.admin.userassetscode.vo.UserAssetsCodeSaveReqVO;
import cn.iocoder.yudao.module.system.controller.admin.userassetscode.vo.UserAssetsCodeRedeemReqVO;
import cn.iocoder.yudao.module.system.dal.dataobject.userassetscode.UserAssetsCodeDO;
import cn.iocoder.yudao.module.system.service.userassetscode.UserAssetsCodeService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import javax.annotation.security.PermitAll;
import javax.validation.constraints.*;
import javax.validation.*;
import javax.servlet.http.*;
import java.util.*;
import java.io.IOException;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;

import static cn.iocoder.yudao.framework.common.pojo.CommonResult.error;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

import cn.iocoder.yudao.framework.excel.core.util.ExcelUtils;

import cn.iocoder.yudao.framework.apilog.core.annotation.ApiAccessLog;

import static cn.iocoder.yudao.framework.apilog.core.enums.OperateTypeEnum.*;
import static cn.iocoder.yudao.framework.web.core.util.WebFrameworkUtils.getLoginUserId;

/**
 * 管理后台 - 用户资源兑换
 */
@Tag(name = "管理后台 - 用户资源兑换")
@RestController
@RequestMapping("/system/ai/user-assets-code")
@Validated
public class UserAssetsCodeController {

    @Resource
    private UserAssetsCodeService userAssetsCodeService;

    @PostMapping("/create")
    @Operation(summary = "创建用户资源兑换")
    @PreAuthorize("@ss.hasPermission('ai:user-assets-code:create')")
    public CommonResult<Integer> createUserAssetsCode(@Valid @RequestBody UserAssetsCodeSaveReqVO createReqVO) {
        return success(userAssetsCodeService.createUserAssetsCode(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新用户资源兑换")
    @PreAuthorize("@ss.hasPermission('ai:user-assets-code:update')")
    public CommonResult<Boolean> updateUserAssetsCode(@Valid @RequestBody UserAssetsCodeSaveReqVO updateReqVO) {
        userAssetsCodeService.updateUserAssetsCode(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除用户资源兑换")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('ai:user-assets-code:delete')")
    public CommonResult<Boolean> deleteUserAssetsCode(@RequestParam("id") Integer id) {
        userAssetsCodeService.deleteUserAssetsCode(id);
        return success(true);
    }

    @PostMapping("/redeem")
    @Operation(summary = "兑换用户资源码")
    @PermitAll
    @ApiAccessLog(operateType = GET)
    public CommonResult<UserAssetsCodeDO> redeemUserAssetsCode(@Valid @RequestBody UserAssetsCodeRedeemReqVO redeemReqVO) {
        Long userId = getLoginUserId();
        if (userId == null) {
            return error(404, "用户不存在");
        }

        return success(userAssetsCodeService.redeemUserAssetsCode(
                redeemReqVO.getCode(), userId));
    }

    @GetMapping("/get")
    @Operation(summary = "获得用户资源兑换")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('ai:user-assets-code:query')")
    public CommonResult<UserAssetsCodeRespVO> getUserAssetsCode(@RequestParam("id") Integer id) {
        UserAssetsCodeDO userAssetsCode = userAssetsCodeService.getUserAssetsCode(id);
        return success(BeanUtils.toBean(userAssetsCode, UserAssetsCodeRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得用户资源兑换分页")
    @PreAuthorize("@ss.hasPermission('ai:user-assets-code:query')")
    public CommonResult<PageResult<UserAssetsCodeRespVO>> getUserAssetsCodePage(@Valid UserAssetsCodePageReqVO pageReqVO) {
        PageResult<UserAssetsCodeDO> pageResult = userAssetsCodeService.getUserAssetsCodePage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, UserAssetsCodeRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出用户资源兑换 Excel")
    @PreAuthorize("@ss.hasPermission('ai:user-assets-code:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportUserAssetsCodeExcel(@Valid UserAssetsCodePageReqVO pageReqVO,
                                          HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<UserAssetsCodeDO> list = userAssetsCodeService.getUserAssetsCodePage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "用户资源兑换.xls", "数据", UserAssetsCodeRespVO.class,
                BeanUtils.toBean(list, UserAssetsCodeRespVO.class));
    }

}