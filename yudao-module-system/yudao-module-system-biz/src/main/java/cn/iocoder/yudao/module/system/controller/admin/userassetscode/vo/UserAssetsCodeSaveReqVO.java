package cn.iocoder.yudao.module.system.controller.admin.userassetscode.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import javax.validation.constraints.*;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;

@Schema(description = "管理后台 - 用户资源兑换新增/修改 Request VO")
@Data
public class UserAssetsCodeSaveReqVO {

    @Schema(description = "主键id", requiredMode = Schema.RequiredMode.REQUIRED, example = "17")
    private Integer id;

    @Schema(description = "用户id", example = "11804")
    private Long userId;

    @Schema(description = "用户名称", example = "赵六")
    private String userName;

    @Schema(description = "套餐名称", example = "赵六")
    private String changeName;

    @Schema(description = "套餐id", example = "27009")
    private Integer changeId;

    @Schema(description = "兑换码")
    private String code;

    @Schema(description = "客户名称", example = "赵六")
    private String customerName;

    @Schema(description = "状态", example = "2")
    private Integer status;

    @Schema(description = "过期时间")
    private LocalDateTime expireDate;

    @Schema(description = "客户id", example = "30352")
    private Integer customerId;

    @Schema(description = "使用日期")
    private LocalDateTime usedDate;

    @Schema(description = "兑换码数量", example = "2")
    private Integer codeNum;

}