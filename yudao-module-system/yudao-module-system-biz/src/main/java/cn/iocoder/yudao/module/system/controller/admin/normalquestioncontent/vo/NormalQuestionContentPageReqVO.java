package cn.iocoder.yudao.module.system.controller.admin.normalquestioncontent.vo;

import lombok.*;
import java.util.*;
import io.swagger.v3.oas.annotations.media.Schema;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - AI 报告分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class NormalQuestionContentPageReqVO extends PageParam {

    @Schema(description = "关联id", example = "25719")
    private Integer questionId;

    @Schema(description = "内容")
    private String content;

    @Schema(description = "子类型", example = "2")
    private String type;

    @Schema(description = "类型 1- 行业趋势 2-志愿100问 3-热门专业 4-慎选专业", example = "2")
    private String parentType;

    @Schema(description = "问题")
    private String question;

    @Schema(description = "版本")
    private Integer version;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

    @Schema(description = "星星")
    private Integer star;

    @Schema(description = "指令")
    private String ds;

    @Schema(description = "定制化指令")
    private String dsCus;

    @Schema(description = "用户id")
    private Integer userId;

}