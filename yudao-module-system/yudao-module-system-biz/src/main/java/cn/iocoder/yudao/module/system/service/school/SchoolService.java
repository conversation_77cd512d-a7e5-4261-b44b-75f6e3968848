package cn.iocoder.yudao.module.system.service.school;

import java.util.*;
import javax.validation.*;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.system.controller.admin.school.vo.SchoolPageReqVO;
import cn.iocoder.yudao.module.system.controller.admin.school.vo.SchoolRespVO;
import cn.iocoder.yudao.module.system.controller.admin.school.vo.SchoolSaveReqVO;
import cn.iocoder.yudao.module.system.dal.dataobject.school.SchoolDO;

/**
 * 院校 Service 接口
 *
 * <AUTHOR>
 */
public interface SchoolService {

    /**
     * 创建院校
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createSchool(@Valid SchoolSaveReqVO createReqVO);

    /**
     * 更新院校
     *
     * @param updateReqVO 更新信息
     */
    void updateSchool(@Valid SchoolSaveReqVO updateReqVO);

    /**
     * 删除院校
     *
     * @param id 编号
     */
    void deleteSchool(Long id);

    /**
     * 获得院校
     *
     * @param id 编号
     * @return 院校
     */
    SchoolDO getSchool(Long id);

    /**
     * 获得院校分页
     *
     * @param pageReqVO 分页查询
     * @return 院校分页
     */
    PageResult<SchoolDO> getSchoolPage(SchoolPageReqVO pageReqVO);

    List<Map<String, Object>> getAllSchool(String name);

    List<SchoolDO> getAllSchools(String name);
}