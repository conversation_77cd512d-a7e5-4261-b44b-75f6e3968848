package cn.iocoder.yudao.module.system.service.gugu;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.system.controller.admin.gugu.gugu.CeeMajorInfo;
import cn.iocoder.yudao.module.system.controller.admin.gugu.vo.CeeMajorPageReqVO;
import cn.iocoder.yudao.module.system.dal.dataobject.gugu.CeeMajorDO;

import java.util.List;
import java.util.Map;

/**
 * 大学高校专业数据 Service 接口
 */
public interface CeeMajorService {

    /**
     * 保存专业数据
     *
     * @param majorInfoList 专业数据列表
     * @return 保存成功的数量
     */
    int saveMajorInfoList(List<CeeMajorInfo> majorInfoList);

    /**
     * 根据专业名称查询专业信息
     *
     * @param majorName 专业名称
     * @return 专业信息
     */
    CeeMajorDO getMajorByName(String majorName);

    /**
     * 根据专业名称和教育级别查询专业信息
     *
     * @param majorName 专业名称
     * @param educationLevel 教育级别
     * @return 专业信息
     */
    CeeMajorDO getMajorByNameAndEducationLevel(String majorName, String educationLevel);

    /**
     * 根据专业代码查询专业信息
     *
     * @param majorCode 专业代码
     * @return 专业信息
     */
    CeeMajorDO getMajorByCode(String majorCode);

    /**
     * 根据关键字模糊查询专业信息
     *
     * @param keywords 关键字
     * @return 专业信息列表
     */
    List<CeeMajorDO> getMajorListByKeywords(String keywords);

    /**
     * 分页查询专业信息
     *
     * @param reqVO 查询条件
     * @return 专业信息分页结果
     */
    PageResult<CeeMajorDO> getMajorPage(CeeMajorPageReqVO reqVO);

    /**
     * 获取专业详情
     *
     * @param id 专业编号
     * @return 专业详情
     */
    CeeMajorDO getMajor(Long id);

    /**
     * 获取学科门类列表
     *
     * @param educationLevel 教育级别
     * @return 学科门类列表
     */
    List<String> getDisciplinaryCategories(String educationLevel);

    /**
     * 获取指定学科门类下的学科子类列表
     *
     * @param disciplinaryCategory 学科门类
     * @param educationLevel 教育级别
     * @return 学科子类列表
     */
    List<String> getDisciplinarySubCategories(String disciplinaryCategory, String educationLevel);

    /**
     * 获取指定学科子类下的专业列表
     *
     * @param disciplinaryCategory 学科门类
     * @param disciplinarySubCategory 学科子类
     * @param educationLevel 教育级别
     * @param majorName 专业名称，模糊搜索
     * @return 专业列表
     */
    List<CeeMajorDO> getMajorsBySubCategory(String disciplinaryCategory, String disciplinarySubCategory, String educationLevel, String majorName);

    /**
     * 根据专业名称模糊搜索专业
     *
     * @param majorName 专业名称
     * @param educationLevel 教育级别
     * @return 专业列表
     */
    List<CeeMajorDO> searchMajorsByName(String majorName, String educationLevel);

    /**
     * 获取专业树形结构（懒加载方式）
     *
     * @param educationLevel 教育级别
     * @param categoryId 学科门类 ID，当为空时只加载第一个门类的数据
     * @return 专业树形结构
     */
    Map<String, Object> getMajorTree(String educationLevel, String categoryId);

    /**
     * 获取专业树形结构（懒加载方式），支持专业名称搜索
     *
     * @param educationLevel 教育级别
     * @param categoryId 学科门类 ID，当为空时只加载第一个门类的数据
     * @param majorName 专业名称，模糊搜索
     * @return 专业树形结构
     */
    Map<String, Object> getMajorTree(String educationLevel, String categoryId, String majorName);

    /**
     * 获取用户推荐专业的树形结构
     *
     * @param userId 用户ID
     * @param educationLevel 教育级别
     * @param categoryId 学科门类 ID，当为空时只加载第一个门类的数据
     * @return 专业树形结构
     */
    Map<String, Object> getMajorTree(Long userId, String educationLevel, String categoryId);

    /**
     * 获取推荐专业列表
     *
     * @param educationLevel 教育级别
     * @return 推荐专业列表
     */
    List<CeeMajorDO> getRecommendedMajors(String educationLevel);
}
