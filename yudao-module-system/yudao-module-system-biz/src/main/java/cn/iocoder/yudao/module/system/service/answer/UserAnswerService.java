package cn.iocoder.yudao.module.system.service.answer;


import cn.iocoder.yudao.module.system.controller.admin.answer.vo.*;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.system.dal.dataobject.answer.UserAnswerDO;

import javax.validation.Valid;
import java.util.List;

/**
 * 用户答案 Service 接口
 *
 * <AUTHOR>
 */
public interface UserAnswerService {

    /**
     * 创建用户答案
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Integer createUserAnswer(@Valid UserAnswerSaveReqVO createReqVO);

    /**
     * 更新用户答案
     *
     * @param updateReqVO 更新信息
     */
    void updateUserAnswer(@Valid UserAnswerSaveReqVO updateReqVO);

    /**
     * 删除用户答案
     *
     * @param id 编号
     */
    void deleteUserAnswer(Integer id);

    /**
     * 获得用户答案
     *
     * @param id 编号
     * @return 用户答案
     */
    UserAnswerDO getUserAnswer(Integer id);

    /**
     * 获得用户答案分页
     *
     * @param pageReqVO 分页查询
     * @return 用户答案分页
     */
    PageResult<UserAnswerDO> getUserAnswerPage(UserAnswerPageReqVO pageReqVO);

    /**
     * 查询用户最后一次的答题记录
     * @param answerNo
     * @return
     */
    UserAnswerDO getUserLastAnswer(Integer answerNo);

    UserAnswerDO getOldUserAnswer(Integer questionId, Integer answerNo,Integer userId);


    Integer getBiggestAnswerNo(Integer userId);


    List<UserAnswerDO> getAnswerByUserIdAndAnswerNo(Integer userId, Integer answerNo);

    UserAnswerDO getAnswerByUserIdAndAnswerNoAndQuestionId(Integer userId, Integer answerNo, Integer questionId);
}