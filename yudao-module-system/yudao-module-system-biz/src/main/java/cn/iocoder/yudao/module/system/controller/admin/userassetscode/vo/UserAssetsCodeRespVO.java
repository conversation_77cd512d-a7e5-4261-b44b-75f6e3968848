package cn.iocoder.yudao.module.system.controller.admin.userassetscode.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;
import com.alibaba.excel.annotation.*;
import cn.iocoder.yudao.framework.excel.core.annotations.DictFormat;
import cn.iocoder.yudao.framework.excel.core.convert.DictConvert;

@Schema(description = "管理后台 - 用户资源兑换 Response VO")
@Data
@ExcelIgnoreUnannotated
public class UserAssetsCodeRespVO {

    @Schema(description = "主键id", requiredMode = Schema.RequiredMode.REQUIRED, example = "17")
    @ExcelProperty("主键id")
    private Integer id;

    @Schema(description = "用户id", example = "11804")
    @ExcelProperty("用户id")
    private Long userId;

    @Schema(description = "用户名称", example = "赵六")
    @ExcelProperty("用户名称")
    private String userName;

    @Schema(description = "套餐名称", example = "赵六")
    @ExcelProperty("套餐名称")
    private String changeName;

    @Schema(description = "创建时间")
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

    @Schema(description = "套餐id", example = "27009")
    @ExcelProperty("套餐id")
    private Integer changeId;

    @Schema(description = "兑换码")
    @ExcelProperty("兑换码")
    private String code;

    @Schema(description = "客户名称", example = "赵六")
    @ExcelProperty(value = "客户名称", converter = DictConvert.class)
    @DictFormat("customer") // TODO 代码优化：建议设置到对应的 DictTypeConstants 枚举类中
    private String customerName;

    @Schema(description = "状态", example = "2")
    @ExcelProperty(value = "状态", converter = DictConvert.class)
    @DictFormat("change_code") // TODO 代码优化：建议设置到对应的 DictTypeConstants 枚举类中
    private Integer status;

    @Schema(description = "过期时间")
    @ExcelProperty("过期时间")
    private LocalDateTime expireDate;

    @Schema(description = "客户id", example = "30352")
    @ExcelProperty("客户id")
    private Integer customerId;

    @Schema(description = "使用日期")
    @ExcelProperty("使用日期")
    private LocalDateTime usedDate;

}