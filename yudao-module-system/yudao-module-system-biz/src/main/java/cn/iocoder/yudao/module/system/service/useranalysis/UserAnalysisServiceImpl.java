package cn.iocoder.yudao.module.system.service.useranalysis;

import cn.iocoder.yudao.module.system.controller.admin.useranalysis.vo.UserAnalysisPageReqVO;
import cn.iocoder.yudao.module.system.controller.admin.useranalysis.vo.UserAnalysisSaveReqVO;
import org.springframework.stereotype.Service;

import org.springframework.validation.annotation.Validated;

import cn.iocoder.yudao.module.system.dal.dataobject.useranalysis.UserAnalysisDO;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;

import cn.iocoder.yudao.module.system.dal.mysql.useranalysis.UserAnalysisMapper;

import javax.annotation.Resource;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;

import static cn.iocoder.yudao.module.system.enums.ErrorCodeConstants.USER_ANALYSIS_NOT_EXISTS;

/**
 * 用户分析 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class UserAnalysisServiceImpl implements UserAnalysisService {

    @Resource
    private UserAnalysisMapper userAnalysisMapper;

    @Override
    public Integer createUserAnalysis(UserAnalysisSaveReqVO createReqVO) {
        // 插入
        UserAnalysisDO userAnalysis = BeanUtils.toBean(createReqVO, UserAnalysisDO.class);
        userAnalysisMapper.insert(userAnalysis);
        // 返回
        return userAnalysis.getId();
    }

    @Override
    public void updateUserAnalysis(UserAnalysisSaveReqVO updateReqVO) {
        // 校验存在
        validateUserAnalysisExists(updateReqVO.getId());
        // 更新
        UserAnalysisDO updateObj = BeanUtils.toBean(updateReqVO, UserAnalysisDO.class);
        userAnalysisMapper.updateById(updateObj);
    }

    @Override
    public void deleteUserAnalysis(Integer id) {
        // 校验存在
        validateUserAnalysisExists(id);
        // 删除
        userAnalysisMapper.deleteById(id);
    }

    private void validateUserAnalysisExists(Integer id) {
        if (userAnalysisMapper.selectById(id) == null) {
            throw exception(USER_ANALYSIS_NOT_EXISTS);
        }
    }

    @Override
    public UserAnalysisDO getUserAnalysis(Integer id) {
        return userAnalysisMapper.selectById(id);
    }

    @Override
    public PageResult<UserAnalysisDO> getUserAnalysisPage(UserAnalysisPageReqVO pageReqVO) {
        return userAnalysisMapper.selectPage(pageReqVO);
    }

}