package cn.iocoder.yudao.module.system.controller.admin.gugu.gugu;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;

/**
 * 高校查询请求 VO
 */
@Schema(description = "高校查询请求参数")
@Data
public class CollegeQueryReqVO {

    @Schema(description = "搜索关键字，模糊匹配高校名称、省市区、高校旧称、地址字段", requiredMode = Schema.RequiredMode.REQUIRED, example = "北京大学")
    @NotBlank(message = "搜索关键字不能为空")
    private String keywords;

    @Schema(description = "每页数据量，参数最大值为20", example = "10")
    @Min(value = 1, message = "每页数据量不能小于1")
    @Max(value = 20, message = "每页数据量不能大于20")
    private Integer pagesize = 10;

    @Schema(description = "页码", example = "1")
    @Min(value = 1, message = "页码不能小于1")
    private Integer pageindex = 1;

    @Schema(description = "控制keyword参数在查询时是否进行模糊查询", example = "false")
    private Boolean keywordstrict = false;

    @Schema(description = "学院类别", example = "理工类")
    private String collegecategory;

    @Schema(description = "学院性质", example = "普通本科")
    private String collegetype;

    @Schema(description = "是否为985院校", example = "true")
    private Boolean is985;

    @Schema(description = "是否为211院校", example = "true")
    private Boolean is211;

    @Schema(description = "是否为双一流院校", example = "true")
    private Boolean isdualclass;

    @Schema(description = "查询学院学制", example = "本科")
    private String edulevel;

    @Schema(description = "查询学院资质", example = "公办")
    private String collegeproperty;
}
