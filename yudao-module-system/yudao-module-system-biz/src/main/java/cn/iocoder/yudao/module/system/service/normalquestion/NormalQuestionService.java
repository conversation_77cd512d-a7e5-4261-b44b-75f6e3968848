package cn.iocoder.yudao.module.system.service.normalquestion;

import java.util.*;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.system.controller.admin.normalquestion.vo.NormalQuestionPageReqVO;
import cn.iocoder.yudao.module.system.controller.admin.normalquestion.vo.NormalQuestionSaveReqVO;
import cn.iocoder.yudao.module.system.dal.dataobject.normalquestion.NormalQuestionDO;

import javax.validation.Valid;

/**
 * AI 报告 Service 接口
 *
 * <AUTHOR>
 */
public interface NormalQuestionService {

    /**
     * 创建AI 报告
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createNormalQuestion(@Valid NormalQuestionSaveReqVO createReqVO);

    /**
     * 更新AI 报告
     *
     * @param updateReqVO 更新信息
     */
    void updateNormalQuestion(@Valid NormalQuestionSaveReqVO updateReqVO);

    /**
     * 删除AI 报告
     *
     * @param id 编号
     */
    void deleteNormalQuestion(Long id);

    /**
     * 获得AI 报告
     *
     * @param id 编号
     * @return AI 报告
     */
    NormalQuestionDO getNormalQuestion(Long id);

    /**
     * 获得AI 报告分页
     *
     * @param pageReqVO 分页查询
     * @return AI 报告分页
     */
    PageResult<NormalQuestionDO> getNormalQuestionPage(NormalQuestionPageReqVO pageReqVO);

}