package cn.iocoder.yudao.module.system.dal.mysql.userassetschange;

import java.util.*;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.module.system.controller.admin.userassetschange.vo.UserAssetsChangePageReqVO;
import cn.iocoder.yudao.module.system.dal.dataobject.userassetschange.UserAssetsChangeDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 用户资源兑换套餐 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface UserAssetsChangeMapper extends BaseMapperX<UserAssetsChangeDO> {

    default PageResult<UserAssetsChangeDO> selectPage(UserAssetsChangePageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<UserAssetsChangeDO>()
                .eqIfPresent(UserAssetsChangeDO::getCustomerId, reqVO.getCustomerId())
                .eqIfPresent(UserAssetsChangeDO::getTrailCount, reqVO.getTrailCount())
                .likeIfPresent(UserAssetsChangeDO::getCustomerName, reqVO.getCustomerName())
                .eqIfPresent(UserAssetsChangeDO::getContentDays, reqVO.getContentDays())
                .likeIfPresent(UserAssetsChangeDO::getChangeName, reqVO.getChangeName())
                .eqIfPresent(UserAssetsChangeDO::getTrailLeftCount, reqVO.getTrailLeftCount())
                .eqIfPresent(UserAssetsChangeDO::getPsCount, reqVO.getPsCount())
                .eqIfPresent(UserAssetsChangeDO::getPsLeftCount, reqVO.getPsLeftCount())
                .eqIfPresent(UserAssetsChangeDO::getContentCount, reqVO.getContentCount())
                .eqIfPresent(UserAssetsChangeDO::getContentLeftCount, reqVO.getContentLeftCount())
                .eqIfPresent(UserAssetsChangeDO::getAskCount, reqVO.getAskCount())
                .eqIfPresent(UserAssetsChangeDO::getAskLeftCount, reqVO.getAskLeftCount())
                .betweenIfPresent(UserAssetsChangeDO::getTrailUpdateTime, reqVO.getTrailUpdateTime())
                .betweenIfPresent(UserAssetsChangeDO::getPsUpdateTime, reqVO.getPsUpdateTime())
                .betweenIfPresent(UserAssetsChangeDO::getContentEndTime, reqVO.getContentEndTime())
                .betweenIfPresent(UserAssetsChangeDO::getContentStartTime, reqVO.getContentStartTime())
                .betweenIfPresent(UserAssetsChangeDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(UserAssetsChangeDO::getId));
    }

}