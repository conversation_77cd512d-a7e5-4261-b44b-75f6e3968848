package cn.iocoder.yudao.module.system.dal.dataobject.useranalysis;

import lombok.*;
import java.util.*;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.*;
import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;

/**
 * 用户分析 DO
 *
 * <AUTHOR>
 */
@TableName("tb_user_analysis")
@KeySequence("tb_user_analysis_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UserAnalysisDO extends BaseDO {

    /**
     * 主键id,即分析Id
     */
    @TableId
    private Integer id;
    /**
     * 用户Id
     */
    private Integer userId;
    /**
     * 答题次序号
     */
    private Integer answerNo;
    /**
     * 问题内容
     */
    private String analysisContent;

}