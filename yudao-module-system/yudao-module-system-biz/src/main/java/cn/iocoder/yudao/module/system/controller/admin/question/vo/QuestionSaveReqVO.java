package cn.iocoder.yudao.module.system.controller.admin.question.vo;

import cn.iocoder.yudao.module.system.dal.dataobject.question.QuestionChoiceDO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;


import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

@Schema(description = "管理后台 - 问题新增/修改 Request VO")
@Data
public class QuestionSaveReqVO {

    @Schema(description = "主键id", requiredMode = Schema.RequiredMode.REQUIRED, example = "14433")
    private Integer id;

    @Schema(description = "问题内容", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "问题内容不能为空")
    private String content;

    @Schema(description = "问题类型 1单选题 2多选题 3问答题", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "问题类型 1单选题 2多选题 3问答题不能为空")
    private Integer type;

    @Schema(description = "是否必答题 1必答 0 可不回答", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "是否必答题 1必答 0 可不回答不能为空")
    private Integer isNecessary;

    @Schema(description = "状态 1可用 0 不可用", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "状态 1可用 0 不可用不能为空")
    private Integer status;

    @Schema(description = "问题顺序", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "问题顺序不能为空")
    private Integer sort;

    @Schema(description = "问题选项列表")
    private List<QuestionChoiceDO> choices;

}