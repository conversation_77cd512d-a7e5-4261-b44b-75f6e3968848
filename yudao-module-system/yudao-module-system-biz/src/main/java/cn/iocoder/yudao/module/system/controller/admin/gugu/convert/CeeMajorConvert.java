package cn.iocoder.yudao.module.system.controller.admin.gugu.convert;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.system.controller.admin.gugu.vo.CeeMajorRespVO;
import cn.iocoder.yudao.module.system.dal.dataobject.gugu.CeeMajorDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 大学高校专业数据 Convert
 */
@Mapper
public interface CeeMajorConvert {

    CeeMajorConvert INSTANCE = Mappers.getMapper(CeeMajorConvert.class);

    /**
     * 将 DO 对象转换为 VO 对象
     *
     * @param bean DO 对象
     * @return VO 对象
     */
    CeeMajorRespVO convert(CeeMajorDO bean);

    /**
     * 将 DO 对象列表转换为 VO 对象列表
     *
     * @param list DO 对象列表
     * @return VO 对象列表
     */
    List<CeeMajorRespVO> convertList(List<CeeMajorDO> list);

    /**
     * 将分页 DO 对象转换为分页 VO 对象
     *
     * @param page 分页 DO 对象
     * @return 分页 VO 对象
     */
    PageResult<CeeMajorRespVO> convertPage(PageResult<CeeMajorDO> page);
}
