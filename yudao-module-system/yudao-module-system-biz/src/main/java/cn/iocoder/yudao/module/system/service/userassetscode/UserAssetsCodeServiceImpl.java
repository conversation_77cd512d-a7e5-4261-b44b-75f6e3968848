package cn.iocoder.yudao.module.system.service.userassetscode;

import cn.iocoder.yudao.framework.common.exception.ErrorCode;
import cn.iocoder.yudao.module.system.controller.admin.userassetscode.vo.UserAssetsCodePageReqVO;
import cn.iocoder.yudao.module.system.controller.admin.userassetscode.vo.UserAssetsCodeSaveReqVO;
import cn.iocoder.yudao.module.system.dal.dataobject.user.AdminUserDO;
import cn.iocoder.yudao.module.system.dal.dataobject.user.UserAssetsDO;
import cn.iocoder.yudao.module.system.dal.dataobject.userassetschange.UserAssetsChangeDO;
import cn.iocoder.yudao.module.system.dal.dataobject.userassetscode.UserAssetsCodeDO;
import cn.iocoder.yudao.module.system.dal.mysql.user.AdminUserMapper;
import cn.iocoder.yudao.module.system.dal.mysql.user.UserAssetsMapper;
import cn.iocoder.yudao.module.system.dal.mysql.userassetschange.UserAssetsChangeMapper;
import cn.iocoder.yudao.module.system.dal.mysql.userassetscode.UserAssetsCodeMapper;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;

import java.util.*;
import java.time.LocalDateTime;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.module.system.enums.ErrorCodeConstants;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;

/**
 * 用户资源兑换 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class UserAssetsCodeServiceImpl implements UserAssetsCodeService {

    @Resource
    private UserAssetsCodeMapper userAssetsCodeMapper;

    @Resource
    private UserAssetsMapper userAssetsMapper;

    @Resource
    AdminUserMapper adminUserMapper;

    @Resource
    UserAssetsChangeMapper assetsChangeMapper;

    @Override
    public Integer createUserAssetsCode(UserAssetsCodeSaveReqVO createReqVO) {
        // 获取需要创建的兑换码数量
        Integer codeNum = createReqVO.getCodeNum();
        if (codeNum == null || codeNum <= 0) {
            codeNum = 1; // 默认创建一个兑换码
        }
        
        // 批量创建兑换码
        List<UserAssetsCodeDO> userAssetsCodes = new ArrayList<>();
        for (int i = 0; i < codeNum; i++) {
            // 创建基本对象并复制属性
            UserAssetsCodeDO userAssetsCode = BeanUtils.toBean(createReqVO, UserAssetsCodeDO.class);
            // 生成16位唯一兑换码
            userAssetsCode.setCode(generateUniqueCode());
            userAssetsCodes.add(userAssetsCode);
        }
        
        // 批量插入
        userAssetsCodes.forEach(userAssetsCodeMapper::insert);
        
        // 返回创建的数量
        return codeNum;
    }

    /**
     * 生成16位唯一兑换码，包含大小写字母和数字
     * 
     * @return 16位唯一兑换码
     */
    private String generateUniqueCode() {
        // 定义字符集：大小写字母和数字
        String characters = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
        int length = 16;
        
        // 使用UUID作为基础确保唯一性
        String uuid = UUID.randomUUID().toString().replace("-", "");
        
        // 创建随机数生成器
        Random random = new Random();
        StringBuilder codeBuilder = new StringBuilder(length);
        
        // 生成16位随机字符串
        for (int i = 0; i < length; i++) {
            // 混合使用UUID的部分字符和随机字符，增强唯一性
            if (i % 2 == 0 && i / 2 < uuid.length()) {
                // 将UUID的字符映射到我们的字符集
                int index = Character.digit(uuid.charAt(i / 2), 16) % characters.length();
                codeBuilder.append(characters.charAt(index));
            } else {
                codeBuilder.append(characters.charAt(random.nextInt(characters.length())));
            }
        }
        
        String code = codeBuilder.toString();
        
        // 检查数据库中是否已存在此兑换码
        LambdaQueryWrapperX<UserAssetsCodeDO> queryWrapper = new LambdaQueryWrapperX<>();
        queryWrapper.eq(UserAssetsCodeDO::getCode, code);
        if (userAssetsCodeMapper.selectCount(queryWrapper) > 0) {
            // 如果已存在，则递归重新生成
            return generateUniqueCode();
        }
        
        return code;
    }

    @Override
    public void updateUserAssetsCode(UserAssetsCodeSaveReqVO updateReqVO) {
        // 校验存在
        validateUserAssetsCodeExists(updateReqVO.getId());
        // 更新
        UserAssetsCodeDO updateObj = BeanUtils.toBean(updateReqVO, UserAssetsCodeDO.class);
        userAssetsCodeMapper.updateById(updateObj);
    }

    @Override
    public void deleteUserAssetsCode(Integer id) {
        // 校验存在
        validateUserAssetsCodeExists(id);
        // 删除
        userAssetsCodeMapper.deleteById(id);
    }

    private void validateUserAssetsCodeExists(Integer id) {
        if (userAssetsCodeMapper.selectById(id) == null) {
            throw exception(new ErrorCode(404,"用户资源不存在"));
        }
    }

    @Override
    public UserAssetsCodeDO getUserAssetsCode(Integer id) {
        return userAssetsCodeMapper.selectById(id);
    }

    @Override
    public PageResult<UserAssetsCodeDO> getUserAssetsCodePage(UserAssetsCodePageReqVO pageReqVO) {
        return userAssetsCodeMapper.selectPage(pageReqVO);
    }

    @Override
    public UserAssetsCodeDO redeemUserAssetsCode(String code,Long userId) {
        // 1. 查询兑换码是否存在
        LambdaQueryWrapperX<UserAssetsCodeDO> queryWrapper = new LambdaQueryWrapperX<>();
        queryWrapper.eq(UserAssetsCodeDO::getCode, code);
        UserAssetsCodeDO userAssetsCode = userAssetsCodeMapper.selectOne(queryWrapper);
        if (userAssetsCode == null) {
            throw exception(new ErrorCode(107_852_000,"兑换码不存在"));
        }
        
        // 2. 检查兑换码状态，只有未使用的兑换码才能兑换
        if (userAssetsCode.getStatus() != null && userAssetsCode.getStatus() != 0) { // 假设0表示未使用状态
            throw exception(new ErrorCode(107_853_000,"兑换码已被使用"));
        }
        
        // 3. 检查兑换码是否过期
        if (userAssetsCode.getExpireDate() != null && userAssetsCode.getExpireDate().isBefore(LocalDateTime.now())) {
            throw exception(new ErrorCode(107_854_000,"兑换码已过期"));
        }
        
        // 4. 更新兑换码状态为已使用
        userAssetsCode.setUserId(userId);
        userAssetsCode.setStatus(1); // 假设1表示已使用状态
        userAssetsCode.setUsedDate(LocalDateTime.now());
        userAssetsCodeMapper.updateById(userAssetsCode);


        //兑换成功后 增加用户权益

        UserAssetsChangeDO userAssetsChangeDO = assetsChangeMapper.selectOne("id",userAssetsCode.getChangeId());

        AdminUserDO adminUserDO = adminUserMapper.selectOne("id",userId);
        if (adminUserDO != null) {
            adminUserDO.setProTotalTimes(adminUserDO.getProTotalTimes() + userAssetsChangeDO.getPsCount());
            adminUserDO.setProLeftTimes(adminUserDO.getProLeftTimes()+userAssetsChangeDO.getPsCount());
            adminUserDO.setTestTotalTimes(adminUserDO.getTestTotalTimes()+userAssetsChangeDO.getTrailCount());
            adminUserDO.setTestLeftTimes(adminUserDO.getTestLeftTimes()+userAssetsChangeDO.getTrailCount());
            adminUserMapper.updateById(adminUserDO);
        }

        UserAssetsDO userAssetsDO = userAssetsMapper.selectOne(UserAssetsDO::getUserId, userId);
        if (userAssetsDO != null) {
            userAssetsDO.setContentCount(userAssetsDO.getContentCount() + userAssetsChangeDO.getContentCount());
            userAssetsDO.setContentLeftCount(userAssetsDO.getContentLeftCount() + userAssetsChangeDO.getContentCount());
            userAssetsDO.setAskCount(userAssetsDO.getAskCount() + userAssetsChangeDO.getAskCount());
            userAssetsDO.setAskLeftCount(userAssetsDO.getAskLeftCount() + userAssetsChangeDO.getAskCount());
            if(userAssetsChangeDO.getContentDays() != null && userAssetsChangeDO.getContentDays() > 0) {
                if(userAssetsDO.getContentEndTime() != null) {
                    if(userAssetsDO.getContentEndTime().isBefore(LocalDateTime.now())){
                        userAssetsDO.setContentStartTime(LocalDateTime.now());
                        userAssetsDO.setContentEndTime(LocalDateTime.now().plusDays(userAssetsChangeDO.getContentDays()));
                    }else {
                        userAssetsDO.setContentEndTime(userAssetsDO.getContentEndTime().plusDays(userAssetsChangeDO.getContentDays()));
                    }
                }
            }
            userAssetsMapper.updateById(userAssetsDO);
        }
        // 5. 返回兑换的资源信息
        return userAssetsCode;
    }
}