package cn.iocoder.yudao.module.system.controller.admin.gugu;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.module.system.controller.admin.gugu.gugu.CollegeInfo;
import cn.iocoder.yudao.module.system.controller.admin.gugu.gugu.CollegeQueryReqVO;
import cn.iocoder.yudao.module.system.controller.admin.gugu.gugu.CollegeQueryRespVO;
import cn.iocoder.yudao.module.system.util.gugu.GuGuDataUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.List;
import java.util.Map;

/**
 * 高校信息查询控制器
 */
@Tag(name = "管理后台 - 高校信息查询")
@RestController
@RequestMapping("/system/college")
@Validated
public class CollegeInfoController {

    @GetMapping("/query")
    @Operation(summary = "查询高校基础信息")
    public CommonResult<CollegeQueryRespVO> queryCollegeInfo(
            @Parameter(description = "搜索关键字", required = true) @RequestParam(value = "keywords") String keywords,
            @Parameter(description = "每页数据量") @RequestParam(value = "pagesize", required = false, defaultValue = "10") Integer pagesize,
            @Parameter(description = "页码") @RequestParam(value = "pageindex", required = false, defaultValue = "1") Integer pageindex,
            @Parameter(description = "是否精确匹配") @RequestParam(value = "keywordstrict", required = false, defaultValue = "false") Boolean keywordstrict,
            @Parameter(description = "学院类别") @RequestParam(value = "collegecategory", required = false) String collegecategory,
            @Parameter(description = "学院性质") @RequestParam(value = "collegetype", required = false) String collegetype,
            @Parameter(description = "是否为985院校") @RequestParam(value = "is985", required = false) Boolean is985,
            @Parameter(description = "是否为211院校") @RequestParam(value = "is211", required = false) Boolean is211,
            @Parameter(description = "是否为双一流院校") @RequestParam(value = "isdualclass", required = false) Boolean isdualclass,
            @Parameter(description = "查询学院学制") @RequestParam(value = "edulevel", required = false) String edulevel,
            @Parameter(description = "查询学院资质") @RequestParam(value = "collegeproperty", required = false) String collegeproperty) {
        
        // 构建请求参数
        CollegeQueryReqVO reqVO = new CollegeQueryReqVO();
        reqVO.setKeywords(keywords);
        reqVO.setPagesize(pagesize);
        reqVO.setPageindex(pageindex);
        reqVO.setKeywordstrict(keywordstrict);
        reqVO.setCollegecategory(collegecategory);
        reqVO.setCollegetype(collegetype);
        reqVO.setIs985(is985);
        reqVO.setIs211(is211);
        reqVO.setIsdualclass(isdualclass);
        reqVO.setEdulevel(edulevel);
        reqVO.setCollegeproperty(collegeproperty);
        
        // 调用工具类获取高校信息
        Map<String, Object> result = GuGuDataUtils.getCollegeInfo(reqVO);
        
        // 判断是否查询成功
        Boolean success = (Boolean) result.get("success");
        if (Boolean.TRUE.equals(success)) {
            // 构建响应VO
            CollegeQueryRespVO respVO = new CollegeQueryRespVO();
            respVO.setCollegeList((List<CollegeInfo>) result.get("collegeList"));
            respVO.setTotalCount((Integer) result.get("totalCount"));
            respVO.setPageIndex((Integer) result.get("pageIndex"));
            respVO.setPageSize((Integer) result.get("pageSize"));
            respVO.setTotalPages((Integer) result.get("totalPages"));
            
            return CommonResult.success(respVO);
        } else {
            String message = (String) result.get("message");
            return CommonResult.error(400, message);
        }
    }
}
