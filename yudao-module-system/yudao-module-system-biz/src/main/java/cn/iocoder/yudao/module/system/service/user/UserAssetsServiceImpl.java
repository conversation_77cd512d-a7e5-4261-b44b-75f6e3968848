package cn.iocoder.yudao.module.system.service.user;

import cn.iocoder.yudao.framework.common.exception.ErrorCode;
import cn.iocoder.yudao.module.system.controller.admin.user.vo.user.UserAssetsPageReqVO;
import cn.iocoder.yudao.module.system.controller.admin.user.vo.user.UserAssetsSaveReqVO;
import cn.iocoder.yudao.module.system.dal.dataobject.user.AdminUserDO;
import cn.iocoder.yudao.module.system.dal.dataobject.user.UserAssetsDO;
import cn.iocoder.yudao.module.system.dal.mysql.user.AdminUserMapper;
import cn.iocoder.yudao.module.system.dal.mysql.user.UserAssetsMapper;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import java.time.LocalDateTime;
import java.util.*;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;


import javax.annotation.Resource;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;

/**
 * 用户资源 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class UserAssetsServiceImpl implements UserAssetsService {

    @Resource
    private UserAssetsMapper userAssetsMapper;

    @Resource
    AdminUserMapper adminUserMapper;

    @Override
    public Integer createUserAssets(UserAssetsSaveReqVO createReqVO) {
        // 插入
        UserAssetsDO userAssets = BeanUtils.toBean(createReqVO, UserAssetsDO.class);
        userAssetsMapper.insert(userAssets);
        // 返回
        return userAssets.getId();
    }

    @Override
    public void updateUserAssets(UserAssetsSaveReqVO updateReqVO) {
        // 校验存在
        validateUserAssetsExists(updateReqVO.getId());
        // 更新
        UserAssetsDO updateObj = BeanUtils.toBean(updateReqVO, UserAssetsDO.class);
        userAssetsMapper.updateById(updateObj);
    }

    @Override
    public void deleteUserAssets(Integer id) {
        // 校验存在
        validateUserAssetsExists(id);
        // 删除
        userAssetsMapper.deleteById(id);
    }

    private void validateUserAssetsExists(Integer id) {
        if (userAssetsMapper.selectById(id) == null) {
            throw exception(new ErrorCode(404,"用户资源不存在"));
        }
    }

    @Override
    public UserAssetsDO getUserAssets(Integer id) {
        return userAssetsMapper.selectById(id);
    }

    @Override
    public PageResult<UserAssetsDO> getUserAssetsPage(UserAssetsPageReqVO pageReqVO) {
        return userAssetsMapper.selectPage(pageReqVO);
    }

    @Override
    public UserAssetsDO getUserAssetsByUserId(Long userId) {
        UserAssetsDO userAssetsDO = userAssetsMapper.selectOne(UserAssetsDO::getUserId, userId);
        if (userAssetsDO != null) {
            //部分资源还存在user表中
            AdminUserDO adminUserDO = adminUserMapper.selectOne("id", userId);
            if (adminUserDO != null) {
                userAssetsDO.setTrailCount(adminUserDO.getTestTotalTimes());
                userAssetsDO.setTrailLeftCount(adminUserDO.getTestLeftTimes());
                userAssetsDO.setPsCount(adminUserDO.getProTotalTimes());
                userAssetsDO.setPsLeftCount(adminUserDO.getProLeftTimes());
            }
        }

        return userAssetsDO;
    }

    @Override
    public boolean validateUserContentMembership(Long userId) {
        if (userId == null) {
            return true; // 如果没有用户ID，则不进行检查，直接通过
        }

        // 获取用户资产信息
        UserAssetsDO userAssets = getUserAssetsByUserId(userId);

        // 检查用户是否有内容会员权限
        if (userAssets == null) {
            // 用户资产信息不存在，抛出异常
            throw exception(new ErrorCode(403, "用户未开通内容会员服务"));
        }

        // 检查是否有内容会员有效期
        boolean hasValidMembership = userAssets.getContentEndTime() != null &&
                                    userAssets.getContentEndTime().isAfter(LocalDateTime.now());

        // 如果是有效会员，则不扣减次数，直接通过
        if (hasValidMembership) {
            return true;
        }

        // 如果不是有效会员，则检查剩余次数
        if (userAssets.getContentLeftCount() == null || userAssets.getContentLeftCount() <= 0) {
            // 剩余次数不足，抛出异常
            throw exception(new ErrorCode(403, "内容查看次数不足，请充值"));
        }

        // 扣减剩余次数
        userAssets.setContentLeftCount(userAssets.getContentLeftCount() - 1);
        // 更新用户资产信息
        UserAssetsSaveReqVO updateReqVO = new UserAssetsSaveReqVO();
        updateReqVO.setId(userAssets.getId());
        updateReqVO.setContentLeftCount(userAssets.getContentLeftCount());
        updateUserAssets(updateReqVO);

        return true;
    }

    @Override
    public boolean validateAskMembership(Long userId) {
        if (userId == null) {
            return true; // 如果没有用户ID，则不进行检查，直接通过
        }

        // 获取用户资产信息
        UserAssetsDO userAssets = getUserAssetsByUserId(userId);

        // 检查用户是否有问答会员权限
        if (userAssets == null) {
            // 用户资产信息不存在，抛出异常
            throw exception(new ErrorCode(403, "用户未开通问答会员服务"));
        }

        // 检查剩余次数
        if (userAssets.getAskLeftCount() == null || userAssets.getAskLeftCount() <= 0) {
            // 剩余次数不足，抛出异常
            throw exception(new ErrorCode(403, "问答次数不足，请充值"));
        }

        // 扣减剩余次数
        userAssets.setAskLeftCount(userAssets.getAskLeftCount() - 1);
        // 更新用户资产信息
        UserAssetsSaveReqVO updateReqVO = new UserAssetsSaveReqVO();
        updateReqVO.setId(userAssets.getId());
        updateReqVO.setAskLeftCount(userAssets.getAskLeftCount());
        updateUserAssets(updateReqVO);

        return true;
    }
}