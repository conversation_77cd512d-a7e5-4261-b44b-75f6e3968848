package cn.iocoder.yudao.module.system.controller.admin.version.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;
import com.alibaba.excel.annotation.*;

@Schema(description = "管理后台 - 志愿咨询题库版本表	 Response VO")
@Data
@ExcelIgnoreUnannotated
public class WishVersionRespVO {

    @Schema(description = "主键id", requiredMode = Schema.RequiredMode.REQUIRED, example = "32609")
    @ExcelProperty("主键id")
    private Integer id;

    @Schema(description = "题库版本名", requiredMode = Schema.RequiredMode.REQUIRED, example = "张三")
    @ExcelProperty("题库版本名")
    private String name;

    @Schema(description = "该版本介绍", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("该版本介绍")
    private String content;

    @Schema(description = "问题创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("问题创建时间")
    private LocalDateTime createTime;

}