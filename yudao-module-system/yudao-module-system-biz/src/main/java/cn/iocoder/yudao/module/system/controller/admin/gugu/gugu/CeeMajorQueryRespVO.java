package cn.iocoder.yudao.module.system.controller.admin.gugu.gugu;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * 大学高校专业数据查询响应 VO
 */
@Schema(description = "大学高校专业数据查询响应结果")
@Data
public class CeeMajorQueryRespVO {

    @Schema(description = "专业数据列表")
    private List<CeeMajorInfo> majorList;

    @Schema(description = "总数据量")
    private Integer totalCount;

    @Schema(description = "当前页码")
    private Integer pageIndex;

    @Schema(description = "每页数据量")
    private Integer pageSize;

    @Schema(description = "总页数")
    private Integer totalPages;

    @Schema(description = "是否返回了所有数据，当不传分页参数时为true")
    private Boolean isAllData;
}
