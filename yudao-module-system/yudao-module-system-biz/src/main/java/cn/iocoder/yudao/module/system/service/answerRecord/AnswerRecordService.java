package cn.iocoder.yudao.module.system.service.answerRecord;


import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.system.controller.admin.answerRecord.vo.AnswerRecordPageReqVO;
import cn.iocoder.yudao.module.system.controller.admin.answerRecord.vo.AnswerRecordSaveReqVO;
import cn.iocoder.yudao.module.system.dal.dataobject.answerRecord.AnswerRecordDO;

import javax.validation.Valid;
import java.util.List;

/**
 * 用户回答记录 Service 接口
 *
 * <AUTHOR>
 */
public interface AnswerRecordService {

    /**
     * 创建用户回答记录
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Integer createAnswerRecord(@Valid AnswerRecordSaveReqVO createReqVO);

    /**
     * 更新用户回答记录
     *
     * @param updateReqVO 更新信息
     */
    void updateAnswerRecord(@Valid AnswerRecordSaveReqVO updateReqVO);

    /**
     * 删除用户回答记录
     *
     * @param id 编号
     */
    void deleteAnswerRecord(Integer id);

    /**
     * 获得用户回答记录
     *
     * @param id 编号
     * @return 用户回答记录
     */
    AnswerRecordDO getAnswerRecord(Integer id);

    /**
     * 获得用户回答记录分页
     *
     * @param pageReqVO 分页查询
     * @return 用户回答记录分页
     */
    PageResult<AnswerRecordDO> getAnswerRecordPage(AnswerRecordPageReqVO pageReqVO);

    List<AnswerRecordDO> getUserAnswerRecord(Integer userId, Integer type, Boolean recommend);

    AnswerRecordDO getByUserIdAndAnswerNo(Integer userId, Integer answerNo);
}