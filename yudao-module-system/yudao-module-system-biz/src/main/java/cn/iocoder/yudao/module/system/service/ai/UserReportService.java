package cn.iocoder.yudao.module.system.service.ai;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.system.controller.admin.ai.vo.UserReportPageReqVO;
import cn.iocoder.yudao.module.system.controller.admin.ai.vo.UserReportSaveReqVO;
import cn.iocoder.yudao.module.system.dal.dataobject.ai.UserReportDO;
import cn.iocoder.yudao.module.system.util.baidu.BaiduAiUtils;
import org.springframework.scheduling.annotation.Async;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;

public interface UserReportService {
    /**
     * 创建AI 报告
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createUserReport(@Valid UserReportSaveReqVO createReqVO);

    /**
     * 更新AI 报告
     *
     * @param updateReqVO 更新信息
     */
    void updateUserReport(@Valid UserReportSaveReqVO updateReqVO);

    /**
     * 删除AI 报告
     *
     * @param id 编号
     */
    void deleteUserReport(Long id);

    /**
     * 获得AI 报告
     *
     * @param id 编号
     * @return AI 报告
     */
    UserReportDO getUserReport(Long id);

    /**
     * 获得AI 报告分页
     *
     * @param pageReqVO 分页查询
     * @return AI 报告分页
     */
    PageResult<UserReportDO> getUserReportPage(UserReportPageReqVO pageReqVO);

    void processAllSchoolDetails(String schoolResponse, BaiduAiUtils.Version version, @NotNull Long userId,Integer recordId);

    List<UserReportDO> getUserReportByUserId(Long userId, Integer reportId);

    CompletableFuture<String> createUserReportByCf(UserReportSaveReqVO userReportSaveReqVO) throws ExecutionException, InterruptedException;

    void createUserReportBySd(UserReportSaveReqVO userReportSaveReqVO);

    @Async
    void sendReportCompletedMessage(Long userId, String reportName, Integer answerRecordId, Long reportId);
}

