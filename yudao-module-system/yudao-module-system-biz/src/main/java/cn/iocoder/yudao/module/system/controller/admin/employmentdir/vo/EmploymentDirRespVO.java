package cn.iocoder.yudao.module.system.controller.admin.employmentdir.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;
import com.alibaba.excel.annotation.*;

@Schema(description = "管理后台 - 就业方向 Response VO")
@Data
@ExcelIgnoreUnannotated
public class EmploymentDirRespVO {

    @Schema(description = "主键id", requiredMode = Schema.RequiredMode.REQUIRED, example = "4702")
    @ExcelProperty("主键id")
    private Long id;

    @Schema(description = "内容", example = "李四")
    @ExcelProperty("内容")
    private String name;

    @Schema(description = "对口院校名称")
    @ExcelProperty("对口院校名称")
    private String schools;

    @Schema(description = "对口院校id")
    @ExcelProperty("对口院校ids")
    private String schoolIds;

    @Schema(description = "状态", example = "1")
    @ExcelProperty("状态")
    private Integer status;

    @Schema(description = "排序")
    @ExcelProperty("排序")
    private Integer sort;

    @Schema(description = "创建时间")
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}