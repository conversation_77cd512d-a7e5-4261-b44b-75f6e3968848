package cn.iocoder.yudao.module.system.controller.admin.useranalysis;

import cn.iocoder.yudao.module.system.controller.admin.useranalysis.vo.UserAnalysisPageReqVO;
import cn.iocoder.yudao.module.system.controller.admin.useranalysis.vo.UserAnalysisRespVO;
import cn.iocoder.yudao.module.system.controller.admin.useranalysis.vo.UserAnalysisSaveReqVO;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;
import java.util.*;
import java.io.IOException;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

import cn.iocoder.yudao.framework.excel.core.util.ExcelUtils;

import cn.iocoder.yudao.framework.apilog.core.annotation.ApiAccessLog;
import static cn.iocoder.yudao.framework.apilog.core.enums.OperateTypeEnum.*;


import cn.iocoder.yudao.module.system.dal.dataobject.useranalysis.UserAnalysisDO;
import cn.iocoder.yudao.module.system.service.useranalysis.UserAnalysisService;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;

@Tag(name = "管理后台 - 用户分析")
@RestController
@RequestMapping("/analysis/user-analysis")
@Validated
public class UserAnalysisController {

    @Resource
    private UserAnalysisService userAnalysisService;

    @PostMapping("/create")
    @Operation(summary = "创建用户分析")
    @PreAuthorize("@ss.hasPermission('analysis:user-analysis:create')")
    public CommonResult<Integer> createUserAnalysis(@Valid @RequestBody UserAnalysisSaveReqVO createReqVO) {
        return success(userAnalysisService.createUserAnalysis(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新用户分析")
    @PreAuthorize("@ss.hasPermission('analysis:user-analysis:update')")
    public CommonResult<Boolean> updateUserAnalysis(@Valid @RequestBody UserAnalysisSaveReqVO updateReqVO) {
        userAnalysisService.updateUserAnalysis(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除用户分析")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('analysis:user-analysis:delete')")
    public CommonResult<Boolean> deleteUserAnalysis(@RequestParam("id") Integer id) {
        userAnalysisService.deleteUserAnalysis(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得用户分析")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('analysis:user-analysis:query')")
    public CommonResult<UserAnalysisRespVO> getUserAnalysis(@RequestParam("id") Integer id) {
        UserAnalysisDO userAnalysis = userAnalysisService.getUserAnalysis(id);
        return success(BeanUtils.toBean(userAnalysis, UserAnalysisRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得用户分析分页")
    @PreAuthorize("@ss.hasPermission('analysis:user-analysis:query')")
    public CommonResult<PageResult<UserAnalysisRespVO>> getUserAnalysisPage(@Valid UserAnalysisPageReqVO pageReqVO) {
        PageResult<UserAnalysisDO> pageResult = userAnalysisService.getUserAnalysisPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, UserAnalysisRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出用户分析 Excel")
    @PreAuthorize("@ss.hasPermission('analysis:user-analysis:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportUserAnalysisExcel(@Valid UserAnalysisPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<UserAnalysisDO> list = userAnalysisService.getUserAnalysisPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "用户分析.xls", "数据", UserAnalysisRespVO.class,
                        BeanUtils.toBean(list, UserAnalysisRespVO.class));
    }

}