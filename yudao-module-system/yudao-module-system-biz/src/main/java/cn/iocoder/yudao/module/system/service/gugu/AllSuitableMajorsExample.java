package cn.iocoder.yudao.module.system.service.gugu;

import cn.iocoder.yudao.module.system.controller.admin.gugu.gugu.MajorAdmissionInfo;
import cn.iocoder.yudao.module.system.controller.admin.gugu.gugu.UserProfileInfo;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * 获取所有适合专业功能示例
 * 展示如何使用新的 getAllSuitableMajors 方法
 */
public class AllSuitableMajorsExample {

    /**
     * 示例：使用新的获取所有适合专业功能
     */
    public static void demonstrateGetAllSuitableMajors() {
        // 创建用户个人信息
        UserProfileInfo profile = new UserProfileInfo();
        profile.setProvince("北京");
        profile.setYear(2024);
        profile.setGender("男");
        profile.setSubjects(Arrays.asList("物理", "化学", "生物"));
        profile.setTotalScore(650);
        profile.setPersonalityTraits(Arrays.asList("学习能力强", "逻辑思维强"));
        profile.setGraduationPlan("就业");
        profile.setInterestedMajorCategories(Arrays.asList("计算机", "软件工程"));
        profile.setPreferredLocation("北京");
        profile.setTypeName("理科");

        System.out.println("=== 获取所有适合专业功能示例 ===");
        System.out.println("用户信息:");
        System.out.println("  省份: " + profile.getProvince());
        System.out.println("  年份: " + profile.getYear());
        System.out.println("  选科: " + String.join("、", profile.getSubjects()));
        System.out.println("  总分: " + profile.getTotalScore());
        System.out.println();

        // 注意：这里只是示例代码，实际使用时需要注入UserProfileService
        // UserProfileService userProfileService = ...; // 通过Spring注入获取
        // Map<String, Object> result = userProfileService.getAllSuitableMajors(profile);
        
        // 以下是模拟的结果展示
        System.out.println("新方法 getAllSuitableMajors 的特点:");
        System.out.println("1. 不限制感兴趣的专业类别匹配");
        System.out.println("2. 扩大分数范围（-100分到+50分）");
        System.out.println("3. 不限制查询数量");
        System.out.println("4. 只根据选科筛选，不进行兴趣匹配");
        System.out.println("5. 返回所有符合基本条件的专业");
        System.out.println("6. 按分数排序");
        System.out.println();

        // 模拟展示结果结构
        demonstrateResultStructure();
    }

    /**
     * 展示结果数据结构
     */
    private static void demonstrateResultStructure() {
        System.out.println("=== 返回结果数据结构 ===");
        System.out.println("result.get(\"allSuitableMajors\") - List<MajorAdmissionInfo>");
        System.out.println("  包含所有符合条件的专业，按分数从低到高排序");
        System.out.println();
        System.out.println("result.get(\"totalCount\") - Integer");
        System.out.println("  专业总数");
        System.out.println();
        System.out.println("result.get(\"queryInfo\") - String");
        System.out.println("  查询信息说明，包括:");
        System.out.println("  • 查询条件（省份、年份、选科、分数范围）");
        System.out.println("  • 查询结果统计");
        System.out.println("  • 分数范围（最低分、最高分）");
        System.out.println();

        System.out.println("=== 与 recommendMajors 方法的区别 ===");
        System.out.println("recommendMajors:");
        System.out.println("  • 根据兴趣匹配专业");
        System.out.println("  • 限制查询数量（最多100个）");
        System.out.println("  • 分数范围较小（-50到+10分）");
        System.out.println("  • 分类返回（高分、同分、低分）");
        System.out.println();
        System.out.println("getAllSuitableMajors:");
        System.out.println("  • 不限制兴趣匹配");
        System.out.println("  • 不限制查询数量");
        System.out.println("  • 分数范围较大（-100到+50分）");
        System.out.println("  • 统一返回，按分数排序");
    }

    /**
     * API 使用示例
     */
    public static void demonstrateApiUsage() {
        System.out.println("=== API 调用示例 ===");
        System.out.println("POST /system/metadata/ceemajor/all-suitable");
        System.out.println();
        System.out.println("请求体:");
        System.out.println("{");
        System.out.println("  \"userInput\": \"我是北京2024年考生，男生，选科是物理化学生物，高考总分650分，性格学习能力强，毕业后会就业，对计算机软件工程感兴趣\"");
        System.out.println("}");
        System.out.println();
        System.out.println("响应体:");
        System.out.println("{");
        System.out.println("  \"code\": 0,");
        System.out.println("  \"data\": {");
        System.out.println("    \"userProfile\": { ... },");
        System.out.println("    \"allSuitableMajors\": [ ... ],");
        System.out.println("    \"totalCount\": 150,");
        System.out.println("    \"queryInfo\": \"查询条件：\\n• 省份：北京\\n...\"");
        System.out.println("  },");
        System.out.println("  \"msg\": \"操作成功\"");
        System.out.println("}");
    }

    public static void main(String[] args) {
        demonstrateGetAllSuitableMajors();
        System.out.println();
        demonstrateApiUsage();
    }
}
