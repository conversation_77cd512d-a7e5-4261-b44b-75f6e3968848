package cn.iocoder.yudao.module.system.dal.dataobject.user;

import lombok.*;
import java.util.*;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.*;
import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;

/**
 * 用户资源 DO
 *
 * <AUTHOR>
 */
@TableName("ai_user_assets")
@KeySequence("ai_user_assets_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UserAssetsDO extends BaseDO {

    /**
     * 主键id
     */
    @TableId
    private Integer id;
    /**
     * 用户id
     */
    private Long userId;
    /**
     * 体验版次数
     */
    private Integer trailCount;
    /**
     * 体验版剩余次数
     */
    private Integer trailLeftCount;
    /**
     * 专业版次数
     */
    private Integer psCount;
    /**
     * 专业版剩余次数
     */
    private Integer psLeftCount;
    /**
     * 内容查看次数
     */
    private Integer contentCount;
    /**
     * 内容查看剩余次数
     */
    private Integer contentLeftCount;
    /**
     * 问答次数
     */
    private Integer askCount;
    /**
     * 问答剩余次数
     */
    private Integer askLeftCount;
    /**
     * 体验版最新购买时间
     */
    private LocalDateTime trailUpdateTime;
    /**
     * 专业版最新购买时间
     */
    private LocalDateTime psUpdateTime;
    /**
     * 内容会员到期时间
     */
    private LocalDateTime contentEndTime;
    /**
     * 内容会员开始时间
     */
    private LocalDateTime contentStartTime;

}