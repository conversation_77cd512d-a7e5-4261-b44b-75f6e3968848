package cn.iocoder.yudao.module.system.service.version;



import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.system.controller.admin.version.vo.WishVersionPageReqVO;
import cn.iocoder.yudao.module.system.controller.admin.version.vo.WishVersionSaveReqVO;
import cn.iocoder.yudao.module.system.dal.dataobject.version.WishVersionDO;

import javax.validation.Valid;

/**
 * 志愿咨询题库版本表	 Service 接口
 *
 * <AUTHOR>
 */
public interface WishVersionService {

    /**
     * 创建志愿咨询题库版本表	
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Integer createWishVersion(@Valid WishVersionSaveReqVO createReqVO);

    /**
     * 更新志愿咨询题库版本表	
     *
     * @param updateReqVO 更新信息
     */
    void updateWishVersion(@Valid WishVersionSaveReqVO updateReqVO);

    /**
     * 删除志愿咨询题库版本表	
     *
     * @param id 编号
     */
    void deleteWishVersion(Integer id);

    /**
     * 获得志愿咨询题库版本表	
     *
     * @param id 编号
     * @return 志愿咨询题库版本表	
     */
    WishVersionDO getWishVersion(Integer id);

    /**
     * 获得志愿咨询题库版本表	分页
     *
     * @param pageReqVO 分页查询
     * @return 志愿咨询题库版本表	分页
     */
    PageResult<WishVersionDO> getWishVersionPage(WishVersionPageReqVO pageReqVO);

}