package cn.iocoder.yudao.module.system.controller.admin.ai.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

@Schema(description = "管理后台 - AI 报告 Response VO")
@Data
@ExcelIgnoreUnannotated
public class UserReportRespVO {

    @Schema(description = "主键id", requiredMode = Schema.RequiredMode.REQUIRED, example = "11550")
    @ExcelProperty("主键id")
    private Long id;

    @Schema(description = "用户id", requiredMode = Schema.RequiredMode.REQUIRED, example = "3368")
    @ExcelProperty("用户id")
    private Long userId;

    @Schema(description = "内容", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("内容")
    private String content;

    @Schema(description = "类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @ExcelProperty("类型")
    private String analysisType;

    @Schema(description = "问题", example = "2")
    @ExcelProperty("问题")
    private String question;

    @Schema(description = "版本", example = "2")
    @ExcelProperty("版本")
    private String version;

    @Schema(description = "工作台id", example = "2")
    @ExcelProperty("工作台id")
    private Integer answerRecordId;

    @Schema(description = "报告名称")
    @ExcelProperty("报告名称")
    private String name;

    @Schema(description = "创建时间")
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}
