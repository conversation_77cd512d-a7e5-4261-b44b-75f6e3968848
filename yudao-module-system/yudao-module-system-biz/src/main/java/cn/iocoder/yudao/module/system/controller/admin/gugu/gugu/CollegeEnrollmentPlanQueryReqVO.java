package cn.iocoder.yudao.module.system.controller.admin.gugu.gugu;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

/**
 * 历年高校招生计划查询请求 VO
 */
@Schema(description = "历年高校招生计划查询请求参数")
@Data
public class CollegeEnrollmentPlanQueryReqVO {

    @Schema(description = "页码", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "页码不能为空")
    @Min(value = 1, message = "页码不能小于1")
    private Integer pageIndex;

    @Schema(description = "每页数据量，取值范围在 10 ~ 100 之间（含）", requiredMode = Schema.RequiredMode.REQUIRED, example = "10")
    @NotNull(message = "每页数据量不能为空")
    @Min(value = 10, message = "每页数据量不能小于10")
    @Max(value = 100, message = "每页数据量不能大于100")
    private Integer pageSize;

    @Schema(description = "查询的高校专业名称，支持模糊查询", example = "计算机科学与技术")
    private String collegeMajorName;

    @Schema(description = "查询的招生年份，如 2020、2021、2022、2023、2024。参数默认值为 0：即获取所有年份的招生计划数据", example = "2024")
    private Integer year = 0;

    @Schema(description = "查询的高校名称，支持模糊查询", example = "北京大学")
    private String schoolName;

    @Schema(description = "查询的招生省份", example = "北京")
    private String provinceName;

    @Schema(description = "查询的专业大类", example = "工学")
    private String classOne;

    @Schema(description = "查询的专业小类", example = "计算机类")
    private String classTwo;

    @Schema(description = "录取批次参数", example = "本科一批")
    private String batchName;

    @Schema(description = "文理综合类别", example = "理科")
    private String type;

    @Schema(description = "咕咕数据平台高校唯一 ID", example = "c24a67f87405b82bec08a5638c32f282")
    private String schoolUuid;
}
