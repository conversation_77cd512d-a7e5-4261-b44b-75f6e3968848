package cn.iocoder.yudao.module.system.controller.admin.answer.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;


import javax.validation.constraints.NotNull;

@Schema(description = "管理后台 - 用户答案新增/修改 Request VO")
@Data
public class UserAnswerSaveReqVO {

    @Schema(description = "主键id,即答案Id", requiredMode = Schema.RequiredMode.REQUIRED, example = "6707")
    private Integer id;

    @Schema(description = "用户Id", requiredMode = Schema.RequiredMode.REQUIRED, example = "32599")
    @NotNull(message = "用户Id不能为空")
    private Integer userId;

    @Schema(description = "问题id", requiredMode = Schema.RequiredMode.REQUIRED, example = "17396")
    @NotNull(message = "问题id不能为空")
    private Integer questionId;

    @Schema(description = "用户手写内容")
    private String writeContent;

    @Schema(description = "用户所选择答案Id集合，用逗号连接")
    private String answerChoices;

    @Schema(description = "用户第多少次答题")
    private Integer answerNo;

}