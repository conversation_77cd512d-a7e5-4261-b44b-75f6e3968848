package cn.iocoder.yudao.module.system.controller.admin.gugu.gugu;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * 考试结果实体类
 */
@Schema(description = "管理后台 - AI 报告分页 Request VO")
@Data
public class ExaminationResult extends PageParam {
    /**
     * 考试分数范围
     */
    @JsonProperty("ExaminationScore")
    private String examinationScore;

    /**
     * 考生数量
     */
    @JsonProperty("CandidateCount")
    private Integer candidateCount;

    /**
     * 总考生数量
     */
    @JsonProperty("TotalCandidates")
    private Integer totalCandidates;

    /**
     * 排名范围
     */
    @JsonProperty("RankingRange")
    private String rankingRange;

    /**
     * 录取批次名称
     */
    @JsonProperty("AdmissionBatchName")
    private String admissionBatchName;

    /**
     * 最低录取分数
     */
    @JsonProperty("MinimumAdmissionScore")
    private String minimumAdmissionScore;

    /**
     * 排名
     */
    @JsonProperty("Ranking")
    private String ranking;

    /**
     * 历史分数记录
     */
    @JsonProperty("HistoricalScores")
    private List<HistoricalScore> historicalScores;

    /**
     * 最低分数
     */
    @JsonProperty("MinScore")
    private Integer minScore;

    /**
     * 最高分数
     */
    @JsonProperty("MaxScore")
    private Integer maxScore;

    /**
     * 分数类型
     */
    @JsonProperty("ScoreType")
    private String scoreType;

    /**
     * 特殊类别
     */
    @JsonProperty("SpecialCategory")
    private String specialCategory;

    /**
     * 级别名称
     */
    @JsonProperty("LevelName")
    private String levelName;

    /**
     * 类型名称
     */
    @JsonProperty("TypeName")
    private String typeName;

    /**
     * 历史分数内部类
     */
    @Data
    public static class HistoricalScore {
        /**
         * 学年
         */
        @JsonProperty("AcademicYear")
        private Integer academicYear;

        /**
         * 考试分数范围
         */
        @JsonProperty("ExaminationScore")
        private String examinationScore;

        /**
         * 排名范围
         */
        @JsonProperty("RankingRange")
        private String rankingRange;

        // 构造函数
        public HistoricalScore() {
        }

        public HistoricalScore(Integer academicYear, String examinationScore, String rankingRange) {
            this.academicYear = academicYear;
            this.examinationScore = examinationScore;
            this.rankingRange = rankingRange;
        }
    }

    // 构造函数
    public ExaminationResult() {
    }

    @Override
    public String toString() {
        return "ExaminationResult{" +
                "examinationScore='" + examinationScore + '\'' +
                ", candidateCount=" + candidateCount +
                ", totalCandidates=" + totalCandidates +
                ", rankingRange='" + rankingRange + '\'' +
                ", admissionBatchName='" + admissionBatchName + '\'' +
                ", minimumAdmissionScore='" + minimumAdmissionScore + '\'' +
                ", ranking='" + ranking + '\'' +
                ", historicalScores=" + historicalScores +
                ", minScore=" + minScore +
                ", maxScore=" + maxScore +
                ", scoreType='" + scoreType + '\'' +
                ", specialCategory='" + specialCategory + '\'' +
                ", levelName='" + levelName + '\'' +
                ", typeName='" + typeName + '\'' +
                '}';
    }
}