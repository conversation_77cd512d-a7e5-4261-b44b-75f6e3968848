package cn.iocoder.yudao.module.system.service.answerRecord;

import cn.iocoder.yudao.module.system.controller.admin.answerRecord.vo.AnswerRecordPageReqVO;
import cn.iocoder.yudao.module.system.controller.admin.answerRecord.vo.AnswerRecordSaveReqVO;
import cn.iocoder.yudao.module.system.dal.dataobject.answerRecord.AnswerRecordDO;
import cn.iocoder.yudao.module.system.dal.mysql.answerRecord.AnswerRecordMapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import org.springframework.stereotype.Service;

import org.springframework.validation.annotation.Validated;


import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;

import javax.annotation.Resource;

import java.util.List;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.module.system.enums.ErrorCodeConstants.ANSWER_RECORD_NOT_EXISTS;


/**
 * 用户回答记录 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class AnswerRecordServiceImpl implements AnswerRecordService {

    @Resource
    private AnswerRecordMapper answerRecordMapper;

    @Override
    public Integer createAnswerRecord(AnswerRecordSaveReqVO createReqVO) {
        // 插入
        AnswerRecordDO answerRecord = BeanUtils.toBean(createReqVO, AnswerRecordDO.class);
        answerRecordMapper.insert(answerRecord);
        // 返回
        return answerRecord.getId();
    }

    @Override
    public void updateAnswerRecord(AnswerRecordSaveReqVO updateReqVO) {
        // 校验存在
        validateAnswerRecordExists(updateReqVO.getId());
        // 更新
        AnswerRecordDO updateObj = BeanUtils.toBean(updateReqVO, AnswerRecordDO.class);
        answerRecordMapper.updateById(updateObj);
    }

    @Override
    public void deleteAnswerRecord(Integer id) {
        // 校验存在
        validateAnswerRecordExists(id);
        // 删除
        answerRecordMapper.deleteById(id);
    }

    private void validateAnswerRecordExists(Integer id) {
        if (answerRecordMapper.selectById(id) == null) {
            throw exception(ANSWER_RECORD_NOT_EXISTS);
        }
    }

    @Override
    public AnswerRecordDO getAnswerRecord(Integer id) {
        return answerRecordMapper.selectById(id);
    }

    @Override
    public PageResult<AnswerRecordDO> getAnswerRecordPage(AnswerRecordPageReqVO pageReqVO) {
        return answerRecordMapper.selectPage(pageReqVO);
    }

    @Override
    public List<AnswerRecordDO> getUserAnswerRecord(Integer userId, Integer type, Boolean recommend) {
        QueryWrapper<AnswerRecordDO> wrapper = new QueryWrapper<>();
        wrapper.eq("type",type);

        if (recommend != null&&recommend) {
            wrapper.eq("recommend", recommend);
        }else {
            if (userId != null) {
                wrapper.eq("user_id", userId);
            }
        }
        wrapper.orderByDesc("create_time");
        List<AnswerRecordDO> answerRecordDOS = answerRecordMapper.selectList(wrapper);
        System.out.println("查询到的结果: " + answerRecordDOS);
        return answerRecordDOS;
    }

    @Override
    public AnswerRecordDO getByUserIdAndAnswerNo(Integer userId, Integer answerNo) {
        QueryWrapper<AnswerRecordDO> wrapper = new QueryWrapper<>();
        wrapper.eq("user_id", userId).eq("answer_no", answerNo);
        AnswerRecordDO answerRecordDO = answerRecordMapper.selectOne(wrapper);
        return answerRecordDO;

    }

}