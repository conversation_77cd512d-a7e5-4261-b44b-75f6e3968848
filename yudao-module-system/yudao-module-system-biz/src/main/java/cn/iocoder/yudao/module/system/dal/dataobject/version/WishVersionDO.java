package cn.iocoder.yudao.module.system.dal.dataobject.version;

import lombok.*;

import java.math.BigDecimal;
import java.util.*;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.*;
import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;

/**
 * 志愿咨询题库版本表	 DO
 *
 * <AUTHOR>
 */
@TableName("tb_wish_version")
@KeySequence("tb_wish_version_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class WishVersionDO extends BaseDO {

    /**
     * 主键id
     */
    @TableId
    private Integer id;
    /**
     * 题库版本名
     */
    private String name;
    /**
     * 该版本介绍
     */
    private String content;
    //价格
    private BigDecimal price;
    //价格购买到的次数
    private Integer timesOfPrice;
}