package cn.iocoder.yudao.module.system.dal.dataobject.userassetscode;

import lombok.*;
import java.util.*;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.*;
import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;

/**
 * 用户资源兑换 DO
 *
 * <AUTHOR>
 */
@TableName("ai_user_assets_code")
@KeySequence("ai_user_assets_code_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UserAssetsCodeDO extends BaseDO {

    /**
     * 主键id
     */
    @TableId
    private Integer id;
    /**
     * 用户id
     */
    private Long userId;


    /**
     * 用户名称
     */
    private String userName;

    /**
     * 套餐名称
     */
    private String changeName;
    /**
     * 套餐id
     */
    private Integer changeId;
    /**
     * 兑换码
     */
    private String code;
    /**
     * 客户名称
     *
     * 枚举 {@link TODO customer 对应的类}
     */
    private String customerName;
    /**
     * 状态
     *
     * 枚举 {@link TODO change_code 对应的类}
     */
    private Integer status;
    /**
     * 过期时间
     */
    private LocalDateTime expireDate;
    /**
     * 客户id
     */
    private Integer customerId;
    /**
     * 使用日期
     */
    private LocalDateTime usedDate;

}