package cn.iocoder.yudao.module.system.dal.mysql.answer;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.module.system.controller.admin.answer.vo.UserAnswerPageReqVO;
import cn.iocoder.yudao.module.system.dal.dataobject.answer.UserAnswerDO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 用户答案 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface UserAnswerMapper extends BaseMapperX<UserAnswerDO> {

    default PageResult<UserAnswerDO> selectPage(UserAnswerPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<UserAnswerDO>()
                .eqIfPresent(UserAnswerDO::getUserId, reqVO.getUserId())
                .eqIfPresent(UserAnswerDO::getQuestionId, reqVO.getQuestionId())
                .eqIfPresent(UserAnswerDO::getWriteContent, reqVO.getWriteContent())
                .eqIfPresent(UserAnswerDO::getAnswerChoices, reqVO.getAnswerChoices())
                .eqIfPresent(UserAnswerDO::getAnswerNo, reqVO.getAnswerNo())
                .betweenIfPresent(UserAnswerDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(UserAnswerDO::getId));
    }

    List<UserAnswerDO> getUserAnswerRecord(Integer userId);
}