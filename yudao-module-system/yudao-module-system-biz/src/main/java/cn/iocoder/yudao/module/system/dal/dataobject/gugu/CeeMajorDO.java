package cn.iocoder.yudao.module.system.dal.dataobject.gugu;

import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.List;

/**
 * 大学高校专业数据 DO
 */
@TableName(value = "system_cee_major", autoResultMap = true)
@KeySequence("system_cee_major_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CeeMajorDO extends BaseDO {

    /**
     * 主键ID
     */
    @TableId
    private Long id;

    /**
     * 教育级别
     */
    private String educationLevel;

    /**
     * 学科门类
     */
    private String disciplinaryCategory;

    /**
     * 学科子类
     */
    private String disciplinarySubCategory;

    /**
     * 专业代码
     */
    private String majorCode;

    /**
     * 专业名称
     */
    private String majorName;

    /**
     * 专业介绍
     */
    private String majorIntroduction;

    /**
     * 开设课程，使用JSON存储
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private List<Course> courses;

    /**
     * 毕业规模
     */
    private String graduateScale;

    /**
     * 男女比例
     */
    private String maleFemaleRatio;

    /**
     * 推荐院校，使用JSON存储
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private List<String> recommendSchools;

    /**
     * 是否推荐专业
     */
    private Boolean isRecommended;

    /**
     * 就业方向
     */
    private String careerDirection;

    /**
     * 课程信息
     */
    @Data
    public static class Course {
        /**
         * 课程名称
         */
        private String courseName;

        /**
         * 课程难度
         */
        private String courseDifficulty;
    }
}
