package cn.iocoder.yudao.module.system.controller.admin.gugu.gugu;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;

/**
 * 大学高校专业数据查询请求 VO
 */
@Schema(description = "大学高校专业数据查询请求参数")
@Data
public class CeeMajorQueryReqVO {

    @Schema(description = "搜索关键字，模糊匹配专业名称、学科、专业介绍、开设课程", example = "计算机")
    private String keywords;

    @Schema(description = "每页数据量，参数最大值为20", example = "10")
    @Min(value = 1, message = "每页数据量不能小于1")
    @Max(value = 20, message = "每页数据量不能大于20")
    private Integer pageSize = 10;

    @Schema(description = "页码", example = "1")
    @Min(value = 1, message = "页码不能小于1")
    private Integer pageIndex = 1;
}
