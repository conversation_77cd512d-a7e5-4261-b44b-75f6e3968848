package cn.iocoder.yudao.module.system.service.gugu;

import cn.iocoder.yudao.module.system.controller.admin.gugu.gugu.ExaminationResult;
import cn.iocoder.yudao.module.system.dal.dataobject.gugu.ScoreSegmentDO;

import java.util.List;
import java.util.Map;

/**
 * 高考一分一段数据 Service 接口
 */
public interface ScoreSegmentService {

    /**
     * 保存一分一段数据
     *
     * @param scoreSegmentList 一分一段数据列表
     * @return 保存成功的数量
     */
    int saveScoreSegmentList(List<ExaminationResult> scoreSegmentList, String year, String provinceName, String subjectSelection);

    /**
     * 根据年份、省份和科目选择类型查询一分一段数据
     *
     * @param year 年份
     * @param provinceName 省份名称
     * @param subjectSelection 科目选择类型
     * @return 一分一段数据列表
     */
    List<ScoreSegmentDO> getScoreSegmentList(String year, String provinceName, String subjectSelection);

    /**
     * 根据年份、省份、科目选择类型和分数查询一分一段数据
     *
     * @param year 年份
     * @param provinceName 省份名称
     * @param subjectSelection 科目选择类型
     * @param score 分数
     * @return 一分一段数据
     */
    ScoreSegmentDO getScoreSegmentByScore(String year, String provinceName, String subjectSelection, String score);

    /**
     * 检查指定条件的数据是否已存在
     *
     * @param year 年份
     * @param provinceName 省份名称
     * @param subjectSelection 科目选择类型
     * @return 是否存在
     */
    boolean existsScoreSegment(String year, String provinceName, String subjectSelection);

    /**
     * 从API获取一分一段数据并保存到数据库
     *
     * @param appkey API密钥
     * @param year 年份
     * @param provinceName 省份名称
     * @param subjectSelection 科目选择类型
     * @return 获取结果
     */
    Map<String, Object> fetchAndSaveScoreSegment(String appkey, String year, String provinceName, String subjectSelection);

    /**
     * 根据条件查询一分一段数据
     *
     * @param year 年份
     * @param provinceName 省份名称
     * @param subjectSelection 科目选择类型
     * @param minScore 最小分数
     * @param maxScore 最大分数
     * @param batchName 批次名称
     * @return 一分一段数据列表
     */
    List<ScoreSegmentDO> getScoreSegmentListByCondition(String year, String provinceName, String subjectSelection,
                                                       String minScore, String maxScore, String batchName);
}
