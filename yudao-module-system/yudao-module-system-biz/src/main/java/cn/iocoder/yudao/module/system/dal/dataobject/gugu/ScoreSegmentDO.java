package cn.iocoder.yudao.module.system.dal.dataobject.gugu;

import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

/**
 * 高考一分一段数据 DO
 */
@TableName("system_score_segment")
@KeySequence("system_score_segment_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ScoreSegmentDO extends BaseDO {

    /**
     * 主键ID
     */
    @TableId
    private Long id;

    /**
     * 年份
     */
    private String year;

    /**
     * 省份名称
     */
    private String provinceName;

    /**
     * 科目选择类型
     */
    private String subjectSelection;

    /**
     * 高考分数
     */
    private String examinationScore;

    /**
     * 该分数考生人数
     */
    private Integer candidateCount;

    /**
     * 累计考生人数
     */
    private Integer totalCandidates;

    /**
     * 位次范围
     */
    private String rankingRange;

    /**
     * 录取批次名称
     */
    private String admissionBatchName;

    /**
     * 最低录取控制分数线
     */
    private String minimumAdmissionScore;

    /**
     * 位次
     */
    private String ranking;

    /**
     * 历史分数数据，JSON字符串格式
     */
    private String historicalScores;
}
