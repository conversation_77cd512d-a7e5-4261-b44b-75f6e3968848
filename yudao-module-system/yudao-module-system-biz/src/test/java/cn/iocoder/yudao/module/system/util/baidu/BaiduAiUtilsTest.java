package cn.iocoder.yudao.module.system.util.baidu;

import com.baidubce.appbuilder.model.appbuilderclient.AppBuilderClientIterator;
import org.junit.jupiter.api.Test;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

/**
 * {@link BaiduAiUtils} 的单元测试
 */
public class BaiduAiUtilsTest {

    @Test
    public void testOpenApiCreateConversation() throws IOException {
        // 测试使用OpenAPI创建对话
        String conversationId = BaiduAiUtils.createConversation(BaiduAiUtils.Version.GUGU.getAppId(), true);
        System.out.println("Created conversation with ID: " + conversationId);
    }

    @Test
    public void testOpenApiRunConversation() throws IOException {
        // 测试使用OpenAPI进行对话（使用流式响应）
        String conversationId = BaiduAiUtils.createConversation(BaiduAiUtils.Version.GUGU.getAppId(), true);
        String response = BaiduAiUtils.runConversation(
                BaiduAiUtils.Version.GUGU.getAppId(),
                "你好，请介绍一下北京大学",
                conversationId,
                false,
                true);
        System.out.println("Response (stream=true): " + response);

        // 测试使用OpenAPI进行对话（不使用流式响应）
        String conversationId2 = BaiduAiUtils.createConversation(BaiduAiUtils.Version.GUGU.getAppId(), true);
        String response2 = BaiduAiUtils.runConversation(
                BaiduAiUtils.Version.GUGU.getAppId(),
                "你好，请介绍一下清华大学",
                conversationId2,
                false,
                false);
        System.out.println("Response (stream=false): " + response2);
    }

    @Test
    public void testChatWithOptions() throws Exception {
        // 测试使用AppBuilderClient方式
        Object result1 = BaiduAiUtils.chatWithOptions(
                BaiduAiUtils.Version.GUGU.getAppId(),
                "介绍一下清华大学",
                false,
                false);
        System.out.println("AppBuilderClient result type: " + result1.getClass().getName());

        // 测试使用OpenAPI方式（不使用流式响应）
        Object result2 = BaiduAiUtils.chatWithOptions(
                BaiduAiUtils.Version.GUGU.getAppId(),
                "介绍一下清华大学",
                false,
                true);
        System.out.println("OpenAPI result (stream=false): " + result2);

        // 测试使用OpenAPI方式（使用流式响应）
        Object result3 = BaiduAiUtils.chatWithOptions(
                BaiduAiUtils.Version.GUGU.getAppId(),
                "介绍一下北京大学",
                true,
                true);
        System.out.println("OpenAPI result (stream=true): " + result3);
    }

    @Test
    public void testProcessQueryWithOptions() {
        // 测试使用AppBuilderClient方式处理查询
        String response1 = BaiduAiUtils.processQueryWithOptions(
                BaiduAiUtils.Version.GUGU.getAppId(),
                "介绍一下上海交通大学",
                false,
                false);
        System.out.println("AppBuilderClient response: " + response1);

        // 测试使用OpenAPI方式处理查询（不使用流式响应）
        String response2 = BaiduAiUtils.processQueryWithOptions(
                BaiduAiUtils.Version.GUGU.getAppId(),
                "介绍一下上海交通大学",
                false,
                true);
        System.out.println("OpenAPI response (stream=false): " + response2);

        // 测试使用OpenAPI方式处理查询（使用流式响应）
        String response3 = BaiduAiUtils.processQueryWithOptions(
                BaiduAiUtils.Version.GUGU.getAppId(),
                "介绍一下复旦大学",
                true,
                true);
        System.out.println("OpenAPI response (stream=true): " + response3);
    }

    @Test
    public void testGetSchoolDetails() {
        // 测试使用AppBuilderClient方式获取学校详情
        String details1 = BaiduAiUtils.getSchoolDetails("复旦大学", BaiduAiUtils.Version.SCHOOL, false);
        System.out.println("AppBuilderClient school details: " + details1);

        // 测试使用OpenAPI方式获取学校详情
        String details2 = BaiduAiUtils.getSchoolDetails("复旦大学", BaiduAiUtils.Version.SCHOOL, true);
        System.out.println("OpenAPI school details: " + details2);
    }

    @Test
    public void testChatWithFileIds() throws Exception {
        // 测试带文件ID的聊天（使用流式响应）
        List<String> fileIds = new ArrayList<>();
        fileIds.add("cdd1e194-cfb7-4173-a154-795fae8535d9"); // 示例文件ID，实际使用时需要替换为有效的文件ID

        Object result = BaiduAiUtils.chatWithOptions(
                BaiduAiUtils.Version.GUGU.getAppId(),
                "根据文件中的数据，统计这几所学校小学生有多少",
                true,
                true,
                fileIds);

        System.out.println("Chat with file IDs result (stream=true): " + result);

        // 测试带文件ID的聊天（不使用流式响应）
        Object result2 = BaiduAiUtils.chatWithOptions(
                BaiduAiUtils.Version.GUGU.getAppId(),
                "根据文件中的数据，统计这几所学校小学生有多少",
                false,
                true,
                fileIds);

        System.out.println("Chat with file IDs result (stream=false): " + result2);
    }

    @Test
    public void testProcessQueryWithFileIds() {
        // 测试带文件ID的查询（使用流式响应）
        List<String> fileIds = new ArrayList<>();
        fileIds.add("cdd1e194-cfb7-4173-a154-795fae8535d9"); // 示例文件ID，实际使用时需要替换为有效的文件ID

        String response = BaiduAiUtils.processQueryWithOptions(
                BaiduAiUtils.Version.GUGU.getAppId(),
                "分析文件中的学生成绩数据",
                true,
                true,
                fileIds);

        System.out.println("Process query with file IDs response (stream=true): " + response);

        // 测试带文件ID的查询（不使用流式响应）
        String response2 = BaiduAiUtils.processQueryWithOptions(
                BaiduAiUtils.Version.GUGU.getAppId(),
                "分析文件中的学生成绩数据",
                false,
                true,
                fileIds);

        System.out.println("Process query with file IDs response (stream=false): " + response2);
    }
}
