package cn.iocoder.yudao.module.system.service.user;

import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 分数解析功能测试
 */
public class ScoreParsingTest {

    /**
     * 模拟 AdminUserServiceImpl 中的 parseScoreSafely 方法
     */
    private double parseScoreSafely(String scoreStr) {
        if (scoreStr == null || scoreStr.trim().isEmpty()) {
            return 0.0;
        }
        
        try {
            // 去除空格
            String cleanScore = scoreStr.trim();
            
            // 处理区间分数（如"600-650"），取平均值
            if (cleanScore.contains("-")) {
                String[] parts = cleanScore.split("-");
                if (parts.length == 2) {
                    double min = Double.parseDouble(parts[0].trim());
                    double max = Double.parseDouble(parts[1].trim());
                    return (min + max) / 2.0;
                }
            }
            
            // 处理可能包含非数字字符的情况
            // 提取数字部分
            String numericPart = cleanScore.replaceAll("[^0-9.]", "");
            if (!numericPart.isEmpty()) {
                return Double.parseDouble(numericPart);
            }
            
            return 0.0;
        } catch (NumberFormatException e) {
            return 0.0;
        }
    }

    @Test
    public void testParseNormalScore() {
        // 测试正常分数
        assertEquals(650.0, parseScoreSafely("650"), 0.01);
        assertEquals(650.5, parseScoreSafely("650.5"), 0.01);
        assertEquals(650.0, parseScoreSafely(" 650 "), 0.01);
    }

    @Test
    public void testParseRangeScore() {
        // 测试区间分数
        assertEquals(625.0, parseScoreSafely("600-650"), 0.01);
        assertEquals(625.0, parseScoreSafely(" 600 - 650 "), 0.01);
        assertEquals(550.5, parseScoreSafely("500-601"), 0.01);
    }

    @Test
    public void testParseScoreWithNonNumericChars() {
        // 测试包含非数字字符的分数
        assertEquals(650.0, parseScoreSafely("650分"), 0.01);
        assertEquals(650.0, parseScoreSafely("分数:650"), 0.01);
        assertEquals(650.0, parseScoreSafely("650(理科)"), 0.01);
        assertEquals(650.5, parseScoreSafely("650.5分"), 0.01);
    }

    @Test
    public void testParseInvalidScore() {
        // 测试无效分数
        assertEquals(0.0, parseScoreSafely(null), 0.01);
        assertEquals(0.0, parseScoreSafely(""), 0.01);
        assertEquals(0.0, parseScoreSafely("   "), 0.01);
        assertEquals(0.0, parseScoreSafely("无分数"), 0.01);
        assertEquals(0.0, parseScoreSafely("N/A"), 0.01);
        assertEquals(0.0, parseScoreSafely("--"), 0.01);
    }

    @Test
    public void testParseComplexCases() {
        // 测试复杂情况
        assertEquals(650.0, parseScoreSafely("理科650分"), 0.01);
        assertEquals(625.0, parseScoreSafely("600-650分"), 0.01);
        assertEquals(650.0, parseScoreSafely("最低分:650"), 0.01);
        assertEquals(0.0, parseScoreSafely("暂无数据"), 0.01);
    }

    @Test
    public void testCalculatePriority() {
        // 测试优先级计算逻辑
        
        // 模拟热门专业和知名学校
        String[] hotMajors = {"计算机", "软件", "人工智能"};
        String[] topSchools = {"北京大学", "清华大学", "复旦大学"};
        
        // 测试计算机专业 + 北京大学 + 高分
        int score1 = calculateTestPriority("计算机科学与技术", "北京大学", "650");
        
        // 测试普通专业 + 普通学校 + 低分
        int score2 = calculateTestPriority("历史学", "某某学院", "500");
        
        // 热门专业和知名学校应该有更高的优先级
        assertTrue(score1 > score2);
    }

    /**
     * 模拟优先级计算逻辑
     */
    private int calculateTestPriority(String majorName, String schoolName, String averageScore) {
        int score = 0;
        
        // 热门专业加分
        String[] hotMajors = {"计算机", "软件", "人工智能", "数据科学", "网络工程", "信息安全"};
        if (majorName != null) {
            for (String hotMajor : hotMajors) {
                if (majorName.contains(hotMajor)) {
                    score += 10;
                    break;
                }
            }
        }
        
        // 知名学校加分
        String[] topSchools = {"北京大学", "清华大学", "复旦大学", "上海交通大学", "浙江大学"};
        if (schoolName != null) {
            boolean isTopSchool = false;
            for (String topSchool : topSchools) {
                if (schoolName.equals(topSchool)) {
                    score += 20;
                    isTopSchool = true;
                    break;
                }
            }
            if (!isTopSchool && schoolName.contains("大学")) {
                score += 5;
            }
        }
        
        // 分数加分
        if (averageScore != null) {
            double avgScore = parseScoreSafely(averageScore);
            score += (int) (avgScore / 100);
        }
        
        return score;
    }

    @Test
    public void testPriorityScoring() {
        // 测试不同组合的优先级分数
        
        // 最高优先级：热门专业 + 知名学校 + 高分
        int maxScore = calculateTestPriority("计算机科学与技术", "北京大学", "650");
        
        // 中等优先级：热门专业 + 普通学校 + 中等分数
        int midScore = calculateTestPriority("软件工程", "某某大学", "580");
        
        // 最低优先级：普通专业 + 普通学校 + 低分
        int minScore = calculateTestPriority("历史学", "某某学院", "500");
        
        System.out.println("最高优先级分数: " + maxScore);
        System.out.println("中等优先级分数: " + midScore);
        System.out.println("最低优先级分数: " + minScore);
        
        assertTrue(maxScore > midScore);
        assertTrue(midScore > minScore);
        
        // 验证具体分数范围
        assertTrue(maxScore >= 30); // 热门专业(10) + 知名学校(20) + 高分(6) = 36
        assertTrue(midScore >= 15); // 热门专业(10) + 普通大学(5) + 中等分数(5) = 20
        assertTrue(minScore >= 5);  // 普通专业(0) + 普通学校(0) + 低分(5) = 5
    }
}
