package cn.iocoder.yudao.module.system.service.gugu;

import cn.iocoder.yudao.module.system.controller.admin.gugu.gugu.CollegeEnrollmentPlanInfo;
import cn.iocoder.yudao.module.system.controller.admin.gugu.gugu.CollegeEnrollmentPlanQueryReqVO;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * 招生计划缓存服务测试
 */
@SpringBootTest
@ActiveProfiles("test")
public class CollegeEnrollmentPlanServiceTest {

    @Resource
    private CollegeEnrollmentPlanService collegeEnrollmentPlanService;

    @Test
    public void testCachePerformance() {
        // 测试参数
        String schoolName = "北京大学";
        String majorName = "计算机科学与技术";
        String provinceName = "北京";
        String typeName = "理科";
        Integer year = 2024;

        // 第一次查询（应该调用API）
        long startTime1 = System.currentTimeMillis();
        List<CollegeEnrollmentPlanInfo> result1 = collegeEnrollmentPlanService.getEnrollmentPlanForMajor(
                schoolName, majorName, provinceName, typeName, year);
        long endTime1 = System.currentTimeMillis();
        long duration1 = endTime1 - startTime1;

        System.out.println("第一次查询耗时: " + duration1 + "ms");
        System.out.println("第一次查询结果数量: " + (result1 != null ? result1.size() : 0));

        // 第二次查询（应该使用缓存）
        long startTime2 = System.currentTimeMillis();
        List<CollegeEnrollmentPlanInfo> result2 = collegeEnrollmentPlanService.getEnrollmentPlanForMajor(
                schoolName, majorName, provinceName, typeName, year);
        long endTime2 = System.currentTimeMillis();
        long duration2 = endTime2 - startTime2;

        System.out.println("第二次查询耗时: " + duration2 + "ms");
        System.out.println("第二次查询结果数量: " + (result2 != null ? result2.size() : 0));

        // 验证缓存效果
        System.out.println("性能提升: " + (duration1 > 0 ? (double) duration1 / duration2 : 0) + "倍");

        // 验证结果一致性
        boolean resultEquals = (result1 == null && result2 == null) ||
                              (result1 != null && result2 != null && result1.size() == result2.size());
        System.out.println("结果一致性: " + resultEquals);
    }

    @Test
    public void testApiLevelCache() {
        // 测试API级别的缓存
        CollegeEnrollmentPlanQueryReqVO reqVO = new CollegeEnrollmentPlanQueryReqVO();
        reqVO.setPageIndex(1);
        reqVO.setPageSize(10);
        reqVO.setSchoolName("清华大学");
        reqVO.setCollegeMajorName("软件工程");
        reqVO.setProvinceName("北京");
        reqVO.setType("理科");
        reqVO.setYear(2024);

        // 第一次查询
        long startTime1 = System.currentTimeMillis();
        Map<String, Object> result1 = collegeEnrollmentPlanService.getCollegeEnrollmentPlanInfo(reqVO);
        long endTime1 = System.currentTimeMillis();

        // 第二次查询
        long startTime2 = System.currentTimeMillis();
        Map<String, Object> result2 = collegeEnrollmentPlanService.getCollegeEnrollmentPlanInfo(reqVO);
        long endTime2 = System.currentTimeMillis();

        System.out.println("API级缓存 - 第一次查询耗时: " + (endTime1 - startTime1) + "ms");
        System.out.println("API级缓存 - 第二次查询耗时: " + (endTime2 - startTime2) + "ms");
        System.out.println("API级缓存 - 第一次查询成功: " + result1.get("success"));
        System.out.println("API级缓存 - 第二次查询成功: " + result2.get("success"));
    }

    @Test
    public void testEnrollmentPlanFilter() {
        // 测试招生计划过滤功能
        String[] testMajors = {
            "计算机科学与技术", "软件工程", "人工智能", "数据科学与大数据技术",
            "网络工程", "信息安全", "物联网工程", "数字媒体技术"
        };

        String[] testSchools = {
            "北京大学", "清华大学", "中国人民大学", "北京航空航天大学",
            "北京理工大学", "中国农业大学", "北京师范大学", "中央民族大学"
        };

        String province = "北京";
        String typeName = "理科";
        Integer year = 2024;

        int totalTests = 0;
        int hasEnrollmentPlan = 0;

        for (String school : testSchools) {
            for (String major : testMajors) {
                totalTests++;

                long startTime = System.currentTimeMillis();
                List<CollegeEnrollmentPlanInfo> result = collegeEnrollmentPlanService.getEnrollmentPlanForMajor(
                        school, major, province, typeName, year);
                long endTime = System.currentTimeMillis();

                if (result != null && !result.isEmpty()) {
                    hasEnrollmentPlan++;
                    System.out.println(String.format("✓ %s - %s: 有招生计划 (%d条), 耗时: %dms",
                            school, major, result.size(), endTime - startTime));
                } else {
                    System.out.println(String.format("✗ %s - %s: 无招生计划, 耗时: %dms",
                            school, major, endTime - startTime));
                }
            }
        }

        System.out.println(String.format("\n招生计划过滤测试结果: %d/%d (%.1f%%) 的专业有招生计划",
                hasEnrollmentPlan, totalTests, (double) hasEnrollmentPlan / totalTests * 100));
    }
}
