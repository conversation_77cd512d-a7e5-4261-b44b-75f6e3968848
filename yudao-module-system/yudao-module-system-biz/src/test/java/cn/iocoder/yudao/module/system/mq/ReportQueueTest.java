package cn.iocoder.yudao.module.system.mq;

import cn.iocoder.yudao.framework.test.core.ut.BaseDbUnitTest;
import cn.iocoder.yudao.module.system.mq.producer.ReportProducer;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.data.redis.connection.stream.RecordId;

import javax.annotation.Resource;

/**
 * 报告队列测试类
 *
 * <AUTHOR>
 */
public class ReportQueueTest extends BaseDbUnitTest {

    @Resource
    private ReportProducer reportProducer;

    @Test
    public void testSendReportGenerateMessage() {
        // 准备参数
        Long userId = 1L;
        Integer answerRecordId = 100;
        String question = "测试问题内容";
        Integer version = 1;
        String name = "测试报告";

        // 调用
        RecordId recordId = reportProducer.sendReportGenerateMessage(userId, answerRecordId, question, version, name);

        // 断言
        System.out.println("消息发送成功，记录ID: " + recordId);
    }
}
