package cn.iocoder.yudao.module.system.util;

import org.junit.jupiter.api.Test;

import java.util.List;
import java.util.Set;

import static org.junit.jupiter.api.Assertions.*;

/**
 * CollectionUtils8 测试类
 */
public class CollectionUtils8Test {

    @Test
    public void testSetOf() {
        Set<String> set = CollectionUtils8.setOf("a", "b", "c");
        assertEquals(3, set.size());
        assertTrue(set.contains("a"));
        assertTrue(set.contains("b"));
        assertTrue(set.contains("c"));
        
        // 测试不可变性
        assertThrows(UnsupportedOperationException.class, () -> set.add("d"));
    }

    @Test
    public void testListOf() {
        List<String> list = CollectionUtils8.listOf("a", "b", "c");
        assertEquals(3, list.size());
        assertEquals("a", list.get(0));
        assertEquals("b", list.get(1));
        assertEquals("c", list.get(2));
        
        // 测试不可变性
        assertThrows(UnsupportedOperationException.class, () -> list.add("d"));
    }

    @Test
    public void testMutableSetOf() {
        Set<String> set = CollectionUtils8.mutableSetOf("a", "b", "c");
        assertEquals(3, set.size());
        assertTrue(set.contains("a"));
        
        // 测试可变性
        set.add("d");
        assertEquals(4, set.size());
        assertTrue(set.contains("d"));
    }

    @Test
    public void testMutableListOf() {
        List<String> list = CollectionUtils8.mutableListOf("a", "b", "c");
        assertEquals(3, list.size());
        assertEquals("a", list.get(0));
        
        // 测试可变性
        list.add("d");
        assertEquals(4, list.size());
        assertEquals("d", list.get(3));
    }

    @Test
    public void testEmptyCollections() {
        Set<String> emptySet = CollectionUtils8.emptySet();
        assertTrue(emptySet.isEmpty());
        
        List<String> emptyList = CollectionUtils8.emptyList();
        assertTrue(emptyList.isEmpty());
    }

    @Test
    public void testSingletonCollections() {
        Set<String> singletonSet = CollectionUtils8.singletonSet("test");
        assertEquals(1, singletonSet.size());
        assertTrue(singletonSet.contains("test"));
        
        List<String> singletonList = CollectionUtils8.singletonList("test");
        assertEquals(1, singletonList.size());
        assertEquals("test", singletonList.get(0));
    }

    @Test
    public void testIsEmpty() {
        assertTrue(CollectionUtils8.isEmpty(null));
        assertTrue(CollectionUtils8.isEmpty(CollectionUtils8.emptyList()));
        assertFalse(CollectionUtils8.isEmpty(CollectionUtils8.listOf("a")));
    }

    @Test
    public void testIsNotEmpty() {
        assertFalse(CollectionUtils8.isNotEmpty(null));
        assertFalse(CollectionUtils8.isNotEmpty(CollectionUtils8.emptyList()));
        assertTrue(CollectionUtils8.isNotEmpty(CollectionUtils8.listOf("a")));
    }

    @Test
    public void testSize() {
        assertEquals(0, CollectionUtils8.size(null));
        assertEquals(0, CollectionUtils8.size(CollectionUtils8.emptyList()));
        assertEquals(3, CollectionUtils8.size(CollectionUtils8.listOf("a", "b", "c")));
    }

    @Test
    public void testAsSet() {
        Set<String> set = CollectionUtils8.asSet("a", "b", "c");
        assertEquals(3, set.size());
        assertTrue(set.contains("a"));
        
        // 测试可变性
        set.add("d");
        assertEquals(4, set.size());
    }

    @Test
    public void testAsList() {
        List<String> list = CollectionUtils8.asList("a", "b", "c");
        assertEquals(3, list.size());
        assertEquals("a", list.get(0));
        
        // 测试可变性
        list.add("d");
        assertEquals(4, list.size());
    }

    @Test
    public void testEmptyArrays() {
        Set<String> emptySet = CollectionUtils8.asSet();
        assertTrue(emptySet.isEmpty());
        
        List<String> emptyList = CollectionUtils8.asList();
        assertTrue(emptyList.isEmpty());
    }

    @Test
    public void testNullArrays() {
        Set<String> set = CollectionUtils8.asSet((String[]) null);
        assertTrue(set.isEmpty());
        
        List<String> list = CollectionUtils8.asList((String[]) null);
        assertTrue(list.isEmpty());
    }
}
