package cn.iocoder.yudao.module.system.util;

import org.junit.jupiter.api.Test;

import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.junit.jupiter.api.Assertions.assertFalse;

/**
 * {@link ChineseSegmentationUtil} 的单元测试
 */
public class ChineseSegmentationUtilTest {

    @Test
    public void testSegment() {
        // 测试基本分词功能
        List<String> result1 = ChineseSegmentationUtil.segment("计算机科学与技术");
        System.out.println("计算机科学与技术 分词结果: " + result1);
        assertTrue(result1.contains("计算机"));
        assertTrue(result1.contains("科学"));
        assertTrue(result1.contains("技术"));

        // 测试带括号的专业名称
        List<String> result2 = ChineseSegmentationUtil.segment("软件工程（嵌入式培养）");
        System.out.println("软件工程（嵌入式培养） 分词结果: " + result2);
        assertTrue(result2.contains("软件"));
        assertTrue(result2.contains("工程"));
        // 括号内容应该被分词器分离
        assertTrue(result2.contains("嵌入式") || result2.contains("培养"));

        // 测试较长的专业名称
        List<String> result3 = ChineseSegmentationUtil.segment("电子信息科学与工程");
        System.out.println("电子信息科学与工程 分词结果: " + result3);
        assertTrue(result3.contains("电子"));
        assertTrue(result3.contains("信息"));
        assertTrue(result3.contains("科学"));
        assertTrue(result3.contains("工程"));

        // 测试特殊格式的专业名称
        List<String> result4 = ChineseSegmentationUtil.segment("建筑电气与智能化");
        System.out.println("建筑电气与智能化 分词结果: " + result4);
        assertTrue(result4.contains("建筑"));
        assertTrue(result4.contains("电气"));
        assertTrue(result4.contains("智能化") || result4.contains("智能"));

        // 测试短专业名称
        List<String> result5 = ChineseSegmentationUtil.segment("法学");
        System.out.println("法学 分词结果: " + result5);
        assertTrue(result5.contains("法学"));

        // 测试带数字的专业名称
        List<String> result6 = ChineseSegmentationUtil.segment("数据科学与大数据技术");
        System.out.println("数据科学与大数据技术 分词结果: " + result6);
        assertTrue(result6.contains("数据") || result6.contains("大数据"));
        assertTrue(result6.contains("科学"));
        assertTrue(result6.contains("技术"));
    }

    @Test
    public void testSegmentByHanLP() {
        // 测试HanLP分词功能
        List<String> result1 = ChineseSegmentationUtil.segmentByHanLP("计算机科学与技术");
        System.out.println("计算机科学与技术 HanLP分词结果: " + result1);
        assertTrue(result1.contains("计算机"));
        // 科学和技术应该被过滤掉，因为它们是停用词
        assertFalse(result1.contains("科学"));
        assertFalse(result1.contains("技术"));

        // 测试复杂专业名称
        List<String> result2 = ChineseSegmentationUtil.segmentByHanLP("建筑电气与智能化");
        System.out.println("建筑电气与智能化 HanLP分词结果: " + result2);
        assertFalse(result2.isEmpty());
        assertTrue(result2.contains("建筑") || result2.contains("电气") || result2.contains("智能化"));
        // 与应该被过滤掉，因为它是停用词
        assertFalse(result2.contains("与"));

        // 测试带括号的专业名称
        List<String> result3 = ChineseSegmentationUtil.segmentByHanLP("软件工程（嵌入式培养）");
        System.out.println("软件工程（嵌入式培养） HanLP分词结果: " + result3);
        assertTrue(result3.contains("软件"));
        // 工程应该被过滤掉，因为它是停用词
        assertFalse(result3.contains("工程"));
        assertTrue(result3.contains("嵌入式") || result3.contains("培养"));

        // 测试带数字的专业名称
        List<String> result4 = ChineseSegmentationUtil.segmentByHanLP("数据科学与大数据技术");
        System.out.println("数据科学与大数据技术 HanLP分词结果: " + result4);
        assertTrue(result4.contains("数据") || result4.contains("大数据"));
        // 科学、与和技术应该被过滤掉，因为它们是停用词
        assertFalse(result4.contains("科学"));
        assertFalse(result4.contains("与"));
        assertFalse(result4.contains("技术"));
    }

    @Test
    public void testStopWords() {
        // 测试停用词过滤
        List<String> result1 = ChineseSegmentationUtil.segment("计算机应用技术");
        System.out.println("计算机应用技术 分词结果: " + result1);
        assertTrue(result1.contains("计算机"));
        // 应用和技术应该被过滤掉，因为它们是停用词
        assertFalse(result1.contains("应用"));
        assertFalse(result1.contains("技术"));

        // 测试全是停用词的情况
        List<String> result2 = ChineseSegmentationUtil.segment("管理学与应用");
        System.out.println("管理学与应用 分词结果: " + result2);
        // 当全是停用词时，应该返回原始的分词结果
        assertFalse(result2.isEmpty());

        // 测试复杂专业名称
        List<String> result3 = ChineseSegmentationUtil.segment("人工智能与数据科学");
        System.out.println("人工智能与数据科学 分词结果: " + result3);
        assertTrue(result3.contains("人工智能") || result3.contains("智能"));
        assertTrue(result3.contains("数据"));
        // 与和科学应该被过滤掉，因为它们是停用词
        assertFalse(result3.contains("与"));
        assertFalse(result3.contains("科学"));
    }

    @Test
    public void testSegmentByRules() {
        // 测试按规则分词
        List<String> result = ChineseSegmentationUtil.segmentByRules("计算机科学与技术");
        System.out.println("计算机科学与技术 规则分词结果: " + result);
        assertTrue(result.contains("计算机科学") || (result.contains("计算机") && result.contains("科学")));
        assertTrue(result.contains("技术"));
    }

    @Test
    public void testSegmentByCharacteristics() {
        // 测试按特征分词
        List<String> result = ChineseSegmentationUtil.segmentByCharacteristics("软件工程技术");
        System.out.println("软件工程技术 特征分词结果: " + result);
        assertTrue(result.contains("软件") || result.contains("软件工程"));
        assertTrue(result.contains("工程") || result.contains("技术") || result.contains("工程技术"));
    }

    @Test
    public void testSegmentByLength() {
        // 测试4字专业名称
        List<String> result1 = ChineseSegmentationUtil.segmentByLength("计算机学");
        System.out.println("计算机学 长度分词结果: " + result1);
        assertEquals(2, result1.size());
        assertEquals("计算", result1.get(0));
        assertEquals("机学", result1.get(1));

        // 测试6字专业名称
        List<String> result2 = ChineseSegmentationUtil.segmentByLength("计算机科学技术");
        System.out.println("计算机科学技术 长度分词结果: " + result2);
        assertEquals(2, result2.size());
        assertEquals("计算机", result2.get(0));
        assertEquals("科学技术", result2.get(1));

        // 测试长专业名称
        List<String> result3 = ChineseSegmentationUtil.segmentByLength("电子信息科学与工程技术");
        System.out.println("电子信息科学与工程技术 长度分词结果: " + result3);
        assertTrue(result3.size() >= 2);
        assertEquals("电子信", result3.get(0));
        assertEquals("技术", result3.get(1));
    }

    @Test
    public void testSegmentByNGram() {
        // 测试2-Gram分词
        List<String> result = ChineseSegmentationUtil.segmentByNGram("计算机", 2);
        System.out.println("计算机 2-Gram分词结果: " + result);
        assertEquals(2, result.size());
        assertEquals("计算", result.get(0));
        assertEquals("算机", result.get(1));
    }
}
