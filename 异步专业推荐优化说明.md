# 异步获取适合专业并存储 - 性能优化方案

## 优化前的问题

### 1. 线程管理问题
- 使用 `new Thread()` 创建线程，没有线程复用
- 缺乏线程池管理，无法控制并发数量
- 没有异常处理和任务取消机制

### 2. 数据库查询效率低
- 每个专业都单独查询数据库
- 缺乏批量处理优化
- 模糊查询数量过多（100个），影响性能

### 3. 代码结构问题
- 所有逻辑都在一个大方法中，难以维护
- 缺乏任务中断检查
- 没有性能监控和日志记录

## 优化方案

### 1. 线程池优化

#### 专业推荐线程池配置
```java
// 根据CPU核心数动态配置线程池
int corePoolSize = Math.max(2, cpuCores / 2);
int maximumPoolSize = Math.max(4, cpuCores);

MAJOR_RECOMMENDATION_EXECUTOR = new ThreadPoolExecutor(
    corePoolSize,
    maximumPoolSize,
    60L,
    TimeUnit.SECONDS,
    new LinkedBlockingQueue<>(100),
    // 自定义线程工厂，添加异常处理
    r -> {
        Thread t = new Thread(r, "major-recommendation-" + r.hashCode());
        t.setDaemon(true);
        t.setUncaughtExceptionHandler((thread, ex) -> {
            log.error("专业推荐线程[{}]发生未捕获异常: {}", thread.getName(), ex.getMessage(), ex);
        });
        return t;
    },
    new ThreadPoolExecutor.CallerRunsPolicy()
);
```

#### 任务管理优化
- 使用 `ConcurrentHashMap` 管理用户任务映射
- 支持任务取消和重复提交处理
- 优雅关闭线程池

### 2. 数据库查询优化

#### 批量查询专业信息
```java
// 优化前：逐个查询
for (String majorName : majorNames) {
    CeeMajorDO major = ceeMajorService.getMajorByNameAndEducationLevel(majorName, educationLevel);
}

// 优化后：批量查询
Set<String> uniqueMajorNames = new HashSet<>(originalToCleanedMajorNames.values());
for (String majorName : uniqueMajorNames) {
    // 批量处理，减少重复查询
}
```

#### 模糊查询限制
- 将模糊查询数量从100个减少到50个
- 优先进行精确匹配，减少模糊查询需求

### 3. 代码结构优化

#### 方法拆分
将原来的大方法拆分为多个专门的方法：

1. **parseUserSubjects()** - 解析用户选科信息
2. **batchFilterBySubjects()** - 批量过滤选科要求
3. **batchQueryMajorInfo()** - 批量查询专业信息
4. **buildMajorTree()** - 构建专业树形结构
5. **saveRecommendationResult()** - 保存推荐结果

#### 任务中断检查
在关键节点添加任务中断检查：
```java
if (Thread.currentThread().isInterrupted()) {
    log.info("用户ID {}的专业推荐任务被取消", userId);
    return;
}
```

### 4. 性能监控

#### 执行时间监控
```java
long startTime = System.currentTimeMillis();
// ... 执行业务逻辑
long endTime = System.currentTimeMillis();
log.info("用户ID {}的专业推荐任务完成，耗时{}ms", userId, endTime - startTime);
```

#### 详细日志记录
- 每个步骤的执行情况
- 数据量统计
- 错误处理和异常记录

## 性能提升效果

### 1. 线程管理效率
- **线程复用**：避免频繁创建销毁线程的开销
- **并发控制**：根据CPU核心数合理控制并发数量
- **资源管理**：优雅关闭，避免资源泄露

### 2. 数据库查询效率
- **减少查询次数**：通过去重和批量处理减少数据库访问
- **限制模糊查询**：从100个减少到50个，降低数据库负载
- **缓存机制**：避免重复查询相同专业

### 3. 响应速度
- **异步处理**：不阻塞主线程
- **任务取消**：支持重复提交时取消旧任务
- **中断检查**：及时响应取消请求

### 4. 系统稳定性
- **异常处理**：完善的异常捕获和处理机制
- **资源清理**：确保任务完成后清理资源
- **监控日志**：便于问题排查和性能分析

## 使用建议

### 1. 监控指标
- 任务执行时间
- 线程池使用情况
- 数据库查询次数
- 内存使用情况

### 2. 调优参数
- 根据实际负载调整线程池大小
- 根据数据库性能调整批量查询大小
- 根据业务需求调整模糊查询限制

### 3. 扩展性
- 支持更多的查询优化策略
- 可以添加缓存机制进一步提升性能
- 支持分布式部署时的任务协调

## 总结

通过以上优化，异步获取适合专业并存储的性能得到了显著提升：

1. **执行效率提升**：通过线程池管理和批量处理，减少了资源开销
2. **响应速度提升**：通过任务中断和优化查询，提高了响应速度
3. **系统稳定性提升**：通过完善的异常处理和资源管理，提高了系统稳定性
4. **可维护性提升**：通过代码重构和模块化，提高了代码可维护性

这些优化措施将显著改善用户体验，特别是在高并发场景下的性能表现。
