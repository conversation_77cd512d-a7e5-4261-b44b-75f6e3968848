-- 为system_cee_major表添加推荐相关字段
-- 执行前请确保备份数据库

-- 添加是否推荐专业字段
ALTER TABLE `system_cee_major` 
ADD COLUMN `is_recommended` tinyint(1) DEFAULT 0 COMMENT '是否推荐专业' AFTER `recommend_schools`;

-- 添加就业方向字段
ALTER TABLE `system_cee_major` 
ADD COLUMN `career_direction` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '就业方向' AFTER `is_recommended`;

-- 创建索引以提高查询性能
CREATE INDEX `idx_is_recommended` ON `system_cee_major` (`is_recommended`);
CREATE INDEX `idx_career_direction` ON `system_cee_major` (`career_direction`);
