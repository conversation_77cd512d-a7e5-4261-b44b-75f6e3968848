package cn.iocoder.yudao.module.system.util;

import java.util.regex.Pattern;

/**
 * 内容清理工具类
 * 用于清理 ai_normal_question_content 表中的奇怪字符
 */
public class CleanContentUtil {
    
    // 匹配 ^[数字]^ 或 ^[数字][数字]^ 等模式的正则表达式
    private static final Pattern STRANGE_PATTERN = Pattern.compile("\\^\\[[0-9]+\\](\\[[0-9]+\\])?\\^");
    
    /**
     * 清理内容中的奇怪字符
     * @param content 原始内容
     * @return 清理后的内容
     */
    public static String cleanStrangeCharacters(String content) {
        if (content == null || content.isEmpty()) {
            return content;
        }
        
        // 使用正则表达式替换所有匹配的模式
        return STRANGE_PATTERN.matcher(content).replaceAll("");
    }
    
    /**
     * 检查内容是否包含奇怪字符
     * @param content 内容
     * @return 是否包含奇怪字符
     */
    public static boolean containsStrangeCharacters(String content) {
        if (content == null || content.isEmpty()) {
            return false;
        }
        return STRANGE_PATTERN.matcher(content).find();
    }
}
